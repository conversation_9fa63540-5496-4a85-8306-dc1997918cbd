spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  cloud:
    nacos:
      discovery:
        enabled: false
  application:
    name: bff-volvoworks-wechat
  profiles:
    active: dev

management:
  health:
    db:
      enabled: false
  endpoint:
    health:
      show-details: always
server:
  port: 8080
logging:
  config: classpath:logback-volvo-huawei.xml
  level:
    com.volvo.bff.volvoworks.aftersales.common.feign.interceptor.MyFeignLogger: debug
request:
  header:
    forwarding:
      list: authtoken,authorization,cookie,grayid
mse-in:
  base-url: https://msein-preprod.digitalvolvo.com
  mid:
    mid-end-org-center:
      url: ${mse-in.base-url}/mid/mid-end-org-center
      path: /
  tob:
    customerbusiness-service:
      url: ${mse-in.base-url}/2b/customerbusiness-service
      path: /
    application-aftersales-management:
      url: ${mse-in.base-url}/2b/application-aftersales-management
      path: /
    dmscus-customer:
      url: ${mse-in.base-url}/2b/dmscus-customer
      path: /
    dmscloud-report:
      url: ${mse-in.base-url}/2b/dmscloud-report
      path: /
    dmscloud-customer:
      url: ${mse-in.base-url}/2b/dmscloud-customer
      path: /
    dmscus-report:
      url: ${mse-in.base-url}/2b/dmscus-report
      path: /
    dmscloud-service:
      url: ${mse-in.base-url}/2b/dmscloud-service
      path: /
    dmscus-ifservice:
      url: ${mse-in.base-url}/2b/dmscus-ifservice
      path: /
    application-maintain-management:
      url: ${mse-in.base-url}/2b/application-maintain-management
      path: /
    dmscus-information:
      url: ${mse-in.base-url}/2b/dmscus-information
      path: /
    dms-finance-service:
      url: ${mse-in.base-url}/2b/dms-finance-service
      path: /
    user-service:
      url: ${mse-in.base-url}/2b/user-service
      path: /
    dmscus-part:
      url: ${mse-in.base-url}/2b/dmscus-part
      path: /
    application-postsale-management:
      url: ${mse-in.base-url}/2b/application-postsale-management
      path: /
