package com.volvo.bff.volvoworks.wechat.common.config;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import com.alibaba.ttl.threadpool.TtlExecutors;

@EnableAsync
@Configuration
public class ThreadPoolConfig {
	
    private Integer coreSize = Runtime.getRuntime().availableProcessors() + 1;
    private Integer maxSize = Runtime.getRuntime().availableProcessors() * 2;
    private Integer aliveTime = 0;
    @Value("${framework.threadPool.capacity:500}")
    private Integer capacity;
	
    @Bean("asyncExecutor")
    Executor threadPoolExecutor(){
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数，当池内线程数小于此值时会优先创建新线程
        executor.setCorePoolSize(coreSize);
        // 线程超时关闭时间
        executor.setKeepAliveSeconds(aliveTime);
        // 任务等待队列，若当前核心线程数已满则将任务放入此队列进行等待，若此队列也满则开启新线程直至达到MaxPoolSize
        executor.setQueueCapacity(capacity);
        // 最大线程数，若全部线程数超过此值则执行拒绝策略
        executor.setMaxPoolSize(maxSize);
        executor.setKeepAliveSeconds(1);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setThreadNamePrefix("task-thread-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        executor.initialize();
        return TtlExecutors.getTtlExecutor(executor);
    }
}
