package com.volvo.bff.volvoworks.wechat.common.model.vo.app;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel("增项管理查询已派工未质检当前技师的工单结果vo")
public class QueryRepairingOrderResultVo {

    @ApiModelProperty(value = "工单号",name = "roNo")
    private String roNo;

    @ApiModelProperty(value = "服务专员",name = "serviceAdvisor")
    private String serviceAdvisor;

    @ApiModelProperty(value = "指定技师",name = "chiefTechnician")
    private String chiefTechnician;

    @ApiModelProperty(value = "车主",name = "ownerName")
    private String ownerName;

    @ApiModelProperty(value = "车牌号",name = "license")
    private String license;

    @ApiModelProperty(value = "VIN",name = "vin")
    private String vin;

    @ApiModelProperty(value = "车型",name = "model")
    private String model;

    @ApiModelProperty(value = "预交车时间",name = "endTimeSupposed")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endTimeSupposed;

    @ApiModelProperty(value = "工单状态",name = "roStatus")
    private Long roStatus;

    @ApiModelProperty(value = "备货状态",name = "isStockUp")
    private Integer isStockUp;

    @ApiModelProperty(value = "群聊id", name = "groupId")
    private String groupId;

    @ApiModelProperty(value = "群聊状态", name = "groupState")
    private Integer groupStatus;

    public Integer getIsStockUp() {
        return isStockUp;
    }

    public void setIsStockUp(Integer isStockUp) {
        this.isStockUp = isStockUp;
    }

    public String getRoNo() {
        return roNo;
    }

    public void setRoNo(String roNo) {
        this.roNo = roNo;
    }

    public String getServiceAdvisor() {
        return serviceAdvisor;
    }

    public void setServiceAdvisor(String serviceAdvisor) {
        this.serviceAdvisor = serviceAdvisor;
    }

    public String getChiefTechnician() {
        return chiefTechnician;
    }

    public void setChiefTechnician(String chiefTechnician) {
        this.chiefTechnician = chiefTechnician;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getLicense() {
        return license;
    }

    public void setLicense(String license) {
        this.license = license;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Date getEndTimeSupposed() {
        return endTimeSupposed;
    }

    public void setEndTimeSupposed(Date endTimeSupposed) {
        this.endTimeSupposed = endTimeSupposed;
    }

    public Long getRoStatus() {
        return roStatus;
    }

    public void setRoStatus(Long roStatus) {
        this.roStatus = roStatus;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public Integer getGroupStatus() {
        return groupStatus;
    }

    public void setGroupStatus(Integer groupStatus) {
        this.groupStatus = groupStatus;
    }
}
