package com.volvo.bff.volvoworks.wechat.common.model.dto.LucencyWorkshop;

import lombok.Data;

@Data
public class WorkShopBalanceOrderDTO {
    /**
     * 预计交车时间、姓名、车牌、车型、状态、结算单确认状态、工单号、服务顾问；
     */
    //经销商code
    private String ownerCode;
    //车牌号
    private String license;
    //vin
    private String vin;
    //工单号
    private String roNo;
    //预交车时间
    private String endTimeSupposed;
    //工单状态
    private String roStatus;
    //送修人
    private String deliverer;
    //服务顾问
    private String serviceAdvisor;
    //车型
    private String model;
    //结算单确认状态
    private String orderConfirmStatus;
    //标签
    private String label;
    //交车状态
    private String deliveryTag;




}
