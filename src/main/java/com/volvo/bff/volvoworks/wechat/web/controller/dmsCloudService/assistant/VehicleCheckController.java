package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.assistant;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.assistant.VehicleCheckService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/VehicleCheckController")
@Api(value = "车辆检查", tags = {"服务助手-车辆检查"})
public class VehicleCheckController {

    @Autowired
    VehicleCheckService vehicleCheckService;

    /**
     * 查询所有车辆检查
     *
     * <AUTHOR>
     * @date 2020年3月16日
     */
    @ApiOperation(value = "查询所有车辆检查", notes = "查询所有预检单", httpMethod = "GET")
    @RequestMapping(value = "/getVehicleCheckList", method = RequestMethod.GET)
    public ResponseDTO<PageBean<VehicleCheckResultListVo>> getVehicleCheckList(VehicleCheckQueryParamsVo vehicleCheckQueryParamsVo) {
        return ResponseUtils.success(vehicleCheckService.getVehicleCheckList(vehicleCheckQueryParamsVo));
    }


    @ApiOperation(value = "查询车辆检查明细", notes = "查询车辆检查明细", httpMethod = "GET")
    @RequestMapping(value = "/getVehicleCheckDetail", method = RequestMethod.GET)
    public ResponseDTO<VehicleCheckDetailListResultVo> getVehicleCheckDetail(String yjNo, String roNo) {
        return ResponseUtils.success(vehicleCheckService.getVehicleCheckDetail(yjNo, roNo));
    }

    /**
     * 保存车辆检查明细
     *
     * <AUTHOR>
     * @date 2020年3月24日
     */

    @ApiOperation(value = "保存车辆检查明细", notes = "保存车辆检查明细", httpMethod = "POST")
    @RequestMapping(value = "/saveVehicleCheckDetail", method = RequestMethod.POST)
    @ResponseBody
    public void saveVehicleCheckDetail(@RequestBody VehicleCheckSaveVo vehicleCheckSaveVo) {
        vehicleCheckService.saveVehicleCheckDetail(vehicleCheckSaveVo);
    }


    @ApiOperation(value = "查询车辆检查增修项", notes = "查询车辆检查增修项", httpMethod = "GET")
    @RequestMapping(value = "/getAdditionalTraining", method = RequestMethod.GET)
    public ResponseDTO<AdditionalTrainingResultVo> getAdditionalTraining(String yjNo, String roNo) {
        return ResponseUtils.success(vehicleCheckService.getAdditionalTraining(yjNo,roNo));
    }


}
