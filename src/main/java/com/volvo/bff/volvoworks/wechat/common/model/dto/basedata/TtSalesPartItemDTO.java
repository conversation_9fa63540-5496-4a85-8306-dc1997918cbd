package com.volvo.bff.volvoworks.wechat.common.model.dto.basedata;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

//配件销售子表字段
@Data
public class TtSalesPartItemDTO {
    @ApiModelProperty(value = "配件明细新增状态", name = "itemId", required = false)
    private String itemId;
    @ApiModelProperty(value = "仓库代码", name = "storageCode", required = false)
    private String storageCode;
    @ApiModelProperty(value = "领料人", name = "receiver", required = false)
    private String receiver;
    @ApiModelProperty(value = "库位代码", name = "storagePositionCode", required = false)
    private String storagePositionCode;
    @ApiModelProperty(value = "计量单位代码", name = "unitCode", required = false)
    private String unitCode;
    @ApiModelProperty(value = "配件销售单号", name = "salesPartNo", required = false)
    private String salesPartNo;
    @ApiModelProperty(value = "发料人", name = "sender", required = false)
    private String sender;
    @ApiModelProperty(value = "原配件销售单号", name = "oldSalesPartNo", required = false)
    private String oldSalesPartNo;
    @ApiModelProperty(value = "配件编号", name = "partNo", required = false)
    private String partNo;
    @ApiModelProperty(value = "配件中文名称", name = "partName", required = false)
    private String partName;
    @ApiModelProperty(value = "是否入帐", name = "isFinished", required = false)
    private Integer isFinished;
    @ApiModelProperty(value = "是否打折", name = "isDiscount", required = false)
    private Integer isDiscount;
    @ApiModelProperty(value = "流水号", name = "batchNo", required = false)
    private Integer batchNo;
    @ApiModelProperty(value = "价格类型", name = "priceType", required = false)
    private Integer priceType;
    @ApiModelProperty(value = "发料时间", name = "sendTime", required = false)
    private Date sendTime;
    @ApiModelProperty(value = "入帐日期", name = "finishedDate", required = false)
    private Date finishedDate;
    @ApiModelProperty(value = "折扣率", name = "discount", required = false)
    private Float discount;
    @ApiModelProperty(value = "价格系数", name = "priceRate", required = false)
    private Float priceRate;
    @ApiModelProperty(value = "配件数量", name = "partQuantity", required = false)
    private Float partQuantity;
    @ApiModelProperty(value = "销售折扣率", name = "salesDiscount", required = false)
    private Float salesDiscount;
    @ApiModelProperty(value = "配件成本单价", name = "partCostPrice", required = false)
    private Double partCostPrice;
    @ApiModelProperty(value = "配件销售单价", name = "partSalesPrice", required = false)
    private Double partSalesPrice;
    @ApiModelProperty(value = "销售金额", name = "salesAmount", required = false)
    private Double salesAmount;
    @ApiModelProperty(value = "配件销售金额", name = "partSalesAmount", required = false)
    private Double partSalesAmount;
    @ApiModelProperty(value = "配件成本金额", name = "partCostAmount", required = false)
    private Double partCostAmount;
    @ApiModelProperty(value = "明细更新状态", name = "flag", required = false)
    private String flag;
    @ApiModelProperty(value = "不含税单价", name = "partSalesPriceNotax", required = false)
    private Double partSalesPriceNotax;
    @ApiModelProperty(value = "不含税金额", name = "partSalesAmountNotax", required = false)
    private Double partSalesAmountNotax;
    @ApiModelProperty(value = "销售方式", name = "partSalesAmountNotax", required = false)
    private String salesType;
    private String isShort;
    @ApiModelProperty(value = "入库不含税金额", name = "shortItems", required = false)
    private Double inAmount;
    @ApiModelProperty(value = "最新进货价", name = "customerNameIn", required = false)
    private Double latestPrice;
    @ApiModelProperty(value = "零件来源", name = "oemTag", required = false)
    private Integer oemTag; 
    @ApiModelProperty(value = "账类", name = "partMony", required = false)
    private Integer partMony; 
    
    @ApiModelProperty(value = "退料标识位", name = "partout", required = false)
    private Integer partOut; 
    
    @ApiModelProperty(value = "付款部门", name = "payOrg", required = false)
    private String payOrg;

    @ApiModelProperty(value = "是否交换件", name = "isExchangePart", required = false)
    private Integer isExchangePart;
    @ApiModelProperty(value = "是否同意返还", name = "isReturn", required = false)
    private Integer allowedExchange;

    @ApiModelProperty(value = "流水id", name = "flowId", required = false)
    private Integer flowId;

    /**
     * 剩余数量-传入-（新增时pc端就是总数量app是直接查出来的）
     * */
    @ApiModelProperty(value = "剩余数量", name = "residualQuantity", required = false)
    private BigDecimal residualQuantity;

    @ApiModelProperty(value = "剩余金额", name = "residualAmount", required = false)
    private BigDecimal residualAmount;

    /**
     * 溯源件数量-传入-（新增时根据接口写入app是直接查出来的）
     * */
    @ApiModelProperty(value = "溯源件数量-传入-（新增时根据接口写入app是直接查出来的）", name = "sourceInQuantity", required = false)
    private BigDecimal sourceInQuantity;

    /**
     * 溯源件剩余数量-传入
     * */
    @ApiModelProperty(value = "溯源件剩余数量-传入", name = "sourceResidualQuantity", required = false)
    private BigDecimal sourceResidualQuantity;

    /**
     * 入库的溯源码
     * */
    @ApiModelProperty(value = "入库的溯源码", name = "sourceCodes", required = false)
    private List<String> sourceCodes;

    /*
     * app传入，总数量
     * */
    @ApiModelProperty(value = "总数量", name = "practicalQuantity", required = false)
    private BigDecimal practicalQuantity;

    /**
     * 是否包含溯源码
     */
    private String isSource;

    private String isContainSource;


    public void setDefaultForOneClickDelivery() {
        if(this.partSalesPrice!=null){
            this.partSalesPriceNotax=BigDecimal.valueOf(this.partSalesPrice).multiply(new BigDecimal("1.13")).doubleValue();
            this.partSalesAmountNotax=BigDecimal.valueOf(this.partSalesPriceNotax).multiply(BigDecimal.valueOf(this.partQuantity)).doubleValue();
        }
    }
}
