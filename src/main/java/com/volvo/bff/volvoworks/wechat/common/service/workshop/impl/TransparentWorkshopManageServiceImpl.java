package com.volvo.bff.volvoworks.wechat.common.service.workshop.impl;


import com.volvo.bff.volvoworks.wechat.common.feign.ApplicationMaintainManagementFeign;
import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudReportFeign;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.workshop.*;
import com.volvo.bff.volvoworks.wechat.common.model.dto.workshop.BookingStatusDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.*;
import com.volvo.bff.volvoworks.wechat.common.service.workshop.TransparentWorkshopManageService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;


@Service
public class TransparentWorkshopManageServiceImpl implements TransparentWorkshopManageService {

    @Autowired
    private ApplicationMaintainManagementFeign applicationMaintainManagementFeign;

    @Autowired
    private DmscloudReportFeign dmscloudReportFeign;

    /**
     * 3.11服务看板预约（企微店端）
     * @param bookingOrderVo 预约参数查询
     * @return 分页结果集
     */
    @Override
    public PageBean<BookingOrderDto> queryBookingWeCom(BookingOrderVo bookingOrderVo) {
        DmsResultDTO<PageBean<BookingOrderDto>> response = applicationMaintainManagementFeign.queryBookingWeCom(bookingOrderVo);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public BookingStatusDto queryBookingStatus(BookingOrderVo bookingOrderVo) {
        DmsResultDTO<BookingStatusDto> response = applicationMaintainManagementFeign.queryBookingStatusCount(bookingOrderVo);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    /**
     * 3.12服务看板到店（企微店端）
     * @param vehicleEntranceVO 查询参数
     * @return 分页结果接
     */
    @Override
    public PageBean<VehicleEntranceParamsVO> queryVehicleEntranceList(VehicleEntranceParamsVO vehicleEntranceVO) {
        DmsResultDTO<PageBean<VehicleEntranceParamsVO>> response = dmscloudReportFeign.queryVehicleEntranceList(vehicleEntranceVO);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    /**
     * 获取 未分拨，已分拨 全部数量
     * @return 数量对象
     */
    @Override
    public VehicleEntranceCountDto queryAllocatedCount(@RequestBody VehicleEntranceParamsVO vehicleEntranceVO) {
        DmsResultDTO<VehicleEntranceCountDto> response = dmscloudReportFeign.queryAllocatedCount(vehicleEntranceVO);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    /**
     * 服务看板缺件查询（企微店端）
     */
    @Override
    public PageBean<ShortPartItemWeComDto> queryShortPartWeCom(ReminderShortPartItemVo shortPartItemVo) {
        DmsResultDTO<PageBean<ShortPartItemWeComDto>> response = applicationMaintainManagementFeign.queryShortPartWeCom(shortPartItemVo);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    /**
     * 查询未到货，已到货，部分到货数量
     *
     * @return 返回对象（包含数量）
     */
    @Override
    public MissingPartsStatusDto getShortPartStatus(ReminderShortPartItemVo shortPartItemVo) {
        DmsResultDTO<MissingPartsStatusDto> response = applicationMaintainManagementFeign.getShortPartStatus(shortPartItemVo);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    /**
     * 根据缺料主键查询 明细
     *
     * @param shortPartItemVo 明细数据
     * @return 明细数据
     */
    @Override
    public ReminderShortPartItemDto queryShortPartItem(ReminderShortPartItemVo shortPartItemVo) {
        DmsResultDTO<ReminderShortPartItemDto> response = applicationMaintainManagementFeign.queryShortPartItem(shortPartItemVo);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    /**
     * 记录通话记录
     * @param workshopCallRecordDto 通话记录详情
     * @return true false
     */
    @Override
    public Boolean addCallLog(WorkshopCallRecordDto workshopCallRecordDto) {
        DmsResultDTO<Boolean> response = applicationMaintainManagementFeign.addCallLog(workshopCallRecordDto);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    /**
     * 查询通话记录
     * @param ownerCode 经销商
     * @param roNo 工单号
     * @param serviceAdvisor 服务顾问
     * @return 通话记录
     */
    @Override
    public PageBean<WorkshopCallRecordDto> queryCallItem(String ownerCode, String roNo, String serviceAdvisor, Integer pageNum, Integer pageSize) {
        DmsResultDTO<PageBean<WorkshopCallRecordDto>> response = applicationMaintainManagementFeign.queryCallItem(ownerCode, roNo, serviceAdvisor, pageNum, pageSize);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    /**
     * 修改预计交车时间
     * @param roNo 工单号
     * @param endTimeSupposed 预交车时间
     */
    @Override
    public void updateRepairOrderStatus(String roNo, String endTimeSupposed) {
        DmsResultDTO<Void> response = applicationMaintainManagementFeign.updateRepairOrderStatus(roNo, endTimeSupposed);
        ErrorConversionUtils.errorCodeConversion(response);
    }
}
