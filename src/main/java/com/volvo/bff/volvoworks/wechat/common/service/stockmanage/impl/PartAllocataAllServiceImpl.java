package com.volvo.bff.volvoworks.wechat.common.service.stockmanage.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.dto.stockmanage.ListPartAllocateInItemDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import com.volvo.bff.volvoworks.wechat.common.service.stockmanage.PartAllocataAllService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;
@Service
public class PartAllocataAllServiceImpl implements PartAllocataAllService  {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Override
    public PageInfoDto queryAllocateInfoPage(Map<String, String> query){
        DmsResultDTO<PageInfoDto> response = dmscloudServiceFeign.queryAllocateInfoPage(query);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public List<Map> queryAllocateInfoById(String id) {
        DmsResultDTO<List<Map>> response = dmscloudServiceFeign.queryAllocateInfoPage(id);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public void feeSettlement(@RequestBody ListPartAllocateInItemDto listPartAllocateInItemDto) {
        DmsResultDTO<Void> response = dmscloudServiceFeign.feeSettlement(listPartAllocateInItemDto);
        ErrorConversionUtils.errorCodeConversion(response);
    }
}
