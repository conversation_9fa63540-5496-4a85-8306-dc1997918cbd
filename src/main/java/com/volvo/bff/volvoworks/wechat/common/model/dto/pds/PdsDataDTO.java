package com.volvo.bff.volvoworks.wechat.common.model.dto.pds;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@ApiModel("PdsDataDTO")
public class PdsDataDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	// pds data id
	@ApiModelProperty(name = "pdsDataId")
	private Integer pdsDataId;
	// 标注点y
	@ApiModelProperty(name = "pointy")
	private String pointy;
	// 标注点x
	@ApiModelProperty(name = "pointx")
	private String pointx;
	// 宽度
	@ApiModelProperty(name = "width")
	private Integer width;
	// 高度
	@ApiModelProperty(name = "height")
	private Integer height;
	// 标注点类型
	@ApiModelProperty(name = "pointType")
	private String pointType;
	// reserve1
	@ApiModelProperty(name = "pdsDataReserve1")
	private String pdsDataReserve1;
	// reserve2
	@ApiModelProperty(name = "pdsDataReserve2")
	private String pdsDataReserve2;
	// reserve3
	@ApiModelProperty(name = "pdsDataReserve3")
	private String pdsDataReserve3;
}