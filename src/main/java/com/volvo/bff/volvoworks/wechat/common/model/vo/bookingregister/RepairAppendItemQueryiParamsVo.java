package com.volvo.bff.volvoworks.wechat.common.model.vo.bookingregister;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 附加项目查询参数VOs
 * <AUTHOR>
 */
@Data
@ApiModel("附加项目查询参数VO")
public class RepairAppendItemQueryiParamsVo {

    private String rowKey;

    @ApiModelProperty(value="工单号 ",name="roNo")
    private String roNo;

    @ApiModelProperty(value="项目编号ID ",name="itemId")
    private String itemId;

    @ApiModelProperty(value="收费类别",name="manageSortCode")
    private String manageSortCode;

    @ApiModelProperty(value="付款部门ID",name="pqymentDepartmentId")
    private Integer pqymentDepartmentId;

    @ApiModelProperty(value="'TYPEID';",name="typeId")
    private String typeId;

    @ApiModelProperty(value="'账类';",name="accountsType")
    private String accountsType;

    //自己后台翻译用的字段
    @ApiModelProperty(name = "帐类名称",value = "accountsType")
    private String accountsName;

    @ApiModelProperty(value="收费区分",name="chargePartitionCode")
    private String chargePartitionCode;

    @ApiModelProperty(value="附加项目代码",name="addItemCode")
    private String addItemCode;

    @ApiModelProperty(value="附加项目名称 ",name="addItemName")
    private String addItemName;

    @ApiModelProperty(value="附加项目数量 ",name="addItemCount")
    private String addItemCount;

    @ApiModelProperty(value="附加项目单价",name="addItemPrice")
    private Double addItemPrice;

    @ApiModelProperty(value="附加项目金额",name="addItemAmount")
    private Double addItemAmount;

    @ApiModelProperty(value="折扣率",name="discount")
    private Double discount;

    //优惠金额（数据库无）
    @ApiModelProperty(value="优惠金额",name="discountAmount")
    private Double discountAmount;

    @ApiModelProperty(name = "应收金额",value = "receivableAmount")
    private String receivableAmount;

    @ApiModelProperty(value="实收金额",name="receiveAmount")
    private Double receiveAmount;

    @ApiModelProperty(value="备注",name="reMark")
    private String remark;

    @ApiModelProperty(name = "预约单号",value = "bookingOrderNo")
    private String bookingOrderNo;

    @ApiModelProperty(name = "用户ID",value = "createdBy")
    private Integer createdBy;

    @ApiModelProperty(name = "序号",value = "jobNo")
    private Integer jobNo;

    @ApiModelProperty(name = "所有者代码",value = "ownerCode")
    private String  ownerCode;

    @ApiModelProperty(name = "CSC代码",value = "cscCode")
    private String  cscCode;

    @ApiModelProperty(name = "交修代码",value = "cscCode")
    private String  itemCode;

    @ApiModelProperty(name = "交修项目名称",value = "cscCode")
    private String  itemName;

    @ApiModelProperty(name = "QB代码",value = "cscCode")
    private String  qbNo;

    @ApiModelProperty(value = "是否保养活动工单",name = "IsUpkeepActivityOrder")
    private Integer IsUpkeepActivityOrder;

    @ApiModelProperty(value = "乐观锁",name = "recordVersion")
    private String recordVersion;

    @ApiModelProperty(value = "活动编号",name = "activityCode")
    private String activityCode;

    @ApiModelProperty(name = "确认状态",value = "orderConfirmStatus")
    private String orderConfirmStatus;
}
