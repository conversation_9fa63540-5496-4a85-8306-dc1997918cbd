package com.volvo.bff.volvoworks.wechat.common.model.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("vida工单明细vo")
public class VidaOrderDetailsVo {

    @ApiModelProperty(value = "jobNo",name = "jobNo")
    private String jobNo;

    @ApiModelProperty(value = "cscCode",name = "cscCode")
    private String cscCode;

    @ApiModelProperty(value = "cscContent",name = "cscContent")
    private String cscContent;

    @ApiModelProperty(value = "cscClass4",name = "cscClass4")
    private String cscClass4;

    @ApiModelProperty(value = "维修工时",name = "repairWorkHourList")
    private List<VidaRepairWorkHourVo> repairWorkHourList;

    @ApiModelProperty(value = "维修零件",name = "repairPartList")
    private List<VidaRepairPartVo > repairPartList;

    public String getJobNo() {
        return jobNo;
    }

    public void setJobNo(String jobNo) {
        this.jobNo = jobNo;
    }

    public String getCscCode() {
        return cscCode;
    }

    public void setCscCode(String cscCode) {
        this.cscCode = cscCode;
    }

    public String getCscContent() {
        return cscContent;
    }

    public void setCscContent(String cscContent) {
        this.cscContent = cscContent;
    }

    public String getCscClass4() {
        return cscClass4;
    }

    public void setCscClass4(String cscClass4) {
        this.cscClass4 = cscClass4;
    }

    public List<VidaRepairWorkHourVo> getRepairWorkHourList() {
        return repairWorkHourList;
    }

    public void setRepairWorkHourList(List<VidaRepairWorkHourVo> repairWorkHourList) {
        this.repairWorkHourList = repairWorkHourList;
    }

    public List<VidaRepairPartVo> getRepairPartList() {
        return repairPartList;
    }

    public void setRepairPartList(List<VidaRepairPartVo> repairPartList) {
        this.repairPartList = repairPartList;
    }
}
