package com.volvo.bff.volvoworks.wechat.common.service.basedata.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import com.volvo.bff.volvoworks.wechat.common.service.basedata.PartBreakageService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class PartBreakageServiceImpl implements PartBreakageService {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Override
    public PageInfoDto queryPartLossByLossNo(Map<String, String> map){
        DmsResultDTO<PageInfoDto> response = dmscloudServiceFeign.queryPartLossByLossNo(map);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public List<Map> queryPartLossItem(String lossNo){
        DmsResultDTO<List<Map>> response = dmscloudServiceFeign.queryPartLossItem(lossNo);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public Map<String, Object> outStorage(String lossNo, String inventoryNo, String operType, String operReason){
        DmsResultDTO<Map<String, Object>> response = dmscloudServiceFeign.outStorage(lossNo, inventoryNo, operType, operReason);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }
}
