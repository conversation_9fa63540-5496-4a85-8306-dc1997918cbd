package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.stockmanage;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.stockmanage.PartBuyService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(value = "/stockmanage/partbuy", tags = {"采购入库Controller"})
@RestController
@RequestMapping("/stockmanage/partbuy")
public class PartBuyController {

    @Autowired
    private PartBuyService partBuyService;

    @ApiOperation(value = "配件采购入库-主界面查询单据", notes = "配件采购入库主单查询", httpMethod = "GET")
    @GetMapping(value = "/queryStockInOrder")
    @ResponseBody
    public ResponseDTO<PageInfoDto> queryStockInOrder(@RequestParam Map<String, String> queryParam) {
        PageInfoDto dto = partBuyService.queryStockInOrder(queryParam);
        return ResponseUtils.success(dto);
    }


    @ApiOperation(value = "配件采购入库-具体单据明细数据查询", notes = "具体单据明细数据查询", httpMethod = "GET")
    @GetMapping(value = "/queryOrderDetail")
    @ResponseBody
    public ResponseDTO<List<Map>> queryOrderDetail(@RequestParam Map<String, String> queryParam) {
        List<Map>  list =partBuyService.queryOrderDetail(queryParam);
        return ResponseUtils.success(list);
    }
}
