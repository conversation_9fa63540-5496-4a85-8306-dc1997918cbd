package com.volvo.bff.volvoworks.wechat.common.exception;

import org.springframework.http.HttpStatus;

import lombok.Data;

/*
 *
 * <AUTHOR>
 * UtilException
 * @date 2016年2月26日
 */
@Data
public class ServiceException extends UtilException {

    /*
     * <AUTHOR> UtilException
     * @date 2016年2月26日 tags
     */
    private static final long serialVersionUID = -5620134357529456759L;

    public ServiceException(Exception e) {
        super(e);
    }

    public ServiceException(String msg) {
        super(msg);
    }

    public ServiceException(String msg, Exception e) {
        super(msg, e);
    }

    public ServiceException(Integer code, HttpStatus httpStatus, String msg, String errorMessage, String displayMessage) {
        super(code, httpStatus, msg, errorMessage, displayMessage);
    }
    
    public ServiceException(Integer code, String msg, String errorMessage, String displayMessage) {
        this(code, HttpStatus.valueOf(code), msg, errorMessage, displayMessage);
    }
    
    public ServiceException(String errorMessage, String displayMessage) {
        this(HttpStatus.INTERNAL_SERVER_ERROR.value(), HttpStatus.INTERNAL_SERVER_ERROR, "", errorMessage, displayMessage);
    }
    
    public ServiceException(ExceptionEnum exceptionEnum) {
        this(HttpStatus.INTERNAL_SERVER_ERROR.value(), HttpStatus.INTERNAL_SERVER_ERROR, "", exceptionEnum.getErrorMessage(), exceptionEnum.getDisplayMessage());
    }
}
