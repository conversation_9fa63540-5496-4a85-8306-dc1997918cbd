package com.volvo.bff.volvoworks.wechat.web.controller.bffAftersales;


import com.volvo.bff.volvoworks.wechat.common.feign.MidEndDictCenterFeign;
import com.volvo.bff.volvoworks.wechat.common.model.dto.city.MidResponse;
import com.volvo.bff.volvoworks.wechat.common.model.dto.city.RegionAllDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Api(value = "/basicInformation", tags = {""})
@Slf4j
@RestController
@RequestMapping("/basicInformation")
public class ProvinceAndCityController {


    @Autowired
    private MidEndDictCenterFeign midEndDictCenterFeign;



    @ApiOperation(value = "查询省市", notes = "查询省市", httpMethod = "GET")
    @GetMapping(value="/region/city")
    public List<RegionAllDto> getProvinceAndCity(){
        log.info("getProvinceAndCity");
        MidResponse<List<RegionAllDto>> provinceAndCity = midEndDictCenterFeign.getProvinceAndCity();
        return provinceAndCity.getData();
    }


    @ApiOperation(value = "根据id获取省", notes = "根据id获取省", httpMethod = "GET")
    @GetMapping(value="/region/queryByRegionId")
    public List<RegionAllDto> queryByRegionId(@RequestParam("id") Integer id){
        log.info("queryByRegionId");
        MidResponse<List<RegionAllDto>> provinceAndCity = midEndDictCenterFeign.getProvinceById(id);
        return provinceAndCity.getData();
    }

}
