package com.volvo.bff.volvoworks.wechat.common.service.assistant;


import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.*;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface VehiclePreviewService {

    /**
     * show  查询所有预检单
     *
     * @param vehiclePreviewQueryParamsVo 请求参数vo
     * @return List<VehiclePreviewResultVo>
     */
    PageBean<VehiclePreviewResultVo> queryAllPreview(VehiclePreviewQueryParamsVo vehiclePreviewQueryParamsVo);

    /**
     * show  根据车牌号查信息
     *
     * @param  flag 请求参数vo
     * @param  license 请求参数vo
     * @return List<VehiclePreviewResultVo>
     */
    List<CustomerInfoResultVo> queryCusInfoByLicense(String flag,String license);

    /**
     * show  新增预检单
     *
     * @param  vehiclePreviewSaveVo 新增数据
     * @return List<VehiclePreviewResultVo>
     */
    String savePreview(VehiclePreviewSaveVo vehiclePreviewSaveVo);

    /**
     * show  查询预检单明细
     *
     *
     * @param  yjNo 请求参数vo
     * @return List<VehiclePreviewResultVo>
     */
    VehiclePreviewDetailResultVo queryPreviewDetail(String yjNo);

    /**
     * show  删除预检单
     *
     *
     * @param  yjNo 请求参数vo
     */
    void deletePreview(String yjNo);


}
