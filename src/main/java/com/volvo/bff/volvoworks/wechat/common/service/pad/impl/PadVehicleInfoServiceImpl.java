package com.volvo.bff.volvoworks.wechat.common.service.pad.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.pad.LogTransparentWorkshopVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.pad.PadQueryOwnerVo;
import com.volvo.bff.volvoworks.wechat.common.service.pad.PadVehicleInfoService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class PadVehicleInfoServiceImpl  implements PadVehicleInfoService {

    @Resource
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Override
    public String logTransparentWorkshop(LogTransparentWorkshopVo logTransparentWorkshopVo) {
        DmsResultDTO<String> response = dmscloudServiceFeign.logTransparentWorkshop(logTransparentWorkshopVo);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public String queryVehicleEntryTime(PadQueryOwnerVo padQueryOwnerVo) {
        DmsResultDTO<String> response = dmscloudServiceFeign.queryVehicleEntryTime(padQueryOwnerVo);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }
}
