package com.volvo.bff.volvoworks.wechat.common.model.vo.midEndInterface;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ModelVO extends MidEndBaseVO{
    private String companyCode;
    private String dataSources;
    private Integer fuelType;
    private Integer id;
    private Integer isValid;
    private String modelCode;
    private String modelName;
    private String modelNameEn;
    private Integer seriesId;
    private Integer modelId;
    private Integer SERIES_ID;
    private Integer MODEL_ID;
    private String MODEL_NAME;
    private String modelYear;
    private String modelCodeName;

    public ModelVO(){

    }
    public ModelVO(Integer id){
        this.seriesId=id;

    }

    public String getModelCodeName() {        return modelCodeName;    }

    public void setModelCodeName(String modelCodeName) {        this.modelCodeName = modelCodeName;    }

    public String getModelYear() {
        return modelYear;
    }

    public void setModelYear(String modelYear) {
        this.modelYear = modelYear;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getDataSources() {
        return dataSources;
    }

    public void setDataSources(String dataSources) {
        this.dataSources = dataSources;
    }

    public Integer getFuelType() {
        return fuelType;
    }

    public void setFuelType(Integer fuelType) {
        this.fuelType = fuelType;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
        this.modelId=id;
        this.MODEL_ID=id;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
        this.MODEL_NAME=modelName;
    }

    public String getModelNameEn() {
        return modelNameEn;
    }

    public void setModelNameEn(String modelNameEn) {
        this.modelNameEn = modelNameEn;
    }

    public Integer getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(Integer seriesId) {
        this.seriesId = seriesId;
        this.SERIES_ID=seriesId;
    }

    @JsonProperty(value = "MODEL_ID")
    public Integer getMODEL_ID() {
        return MODEL_ID;
    }

    public void setMODEL_ID(Integer MODEL_ID) {
        this.MODEL_ID = MODEL_ID;
    }
    @JsonProperty(value = "MODEL_NAME")
    public String getMODEL_NAME() {
        return MODEL_NAME;
    }

    public void setMODEL_NAME(String MODEL_NAME) {
        this.MODEL_NAME = MODEL_NAME;
    }
    @JsonProperty(value = "SERIES_ID")
    public Integer getSERIES_ID() {
        return SERIES_ID;
    }

    public void setSERIES_ID(Integer SERIES_ID) {
        this.SERIES_ID = SERIES_ID;
    }

    public Integer getModelId() {
        return modelId;
    }

    public void setModelId(Integer modelId) {
        this.modelId = modelId;
    }

    @Override
    public String toString() {
        return "ModelVO{" +
                "companyCode='" + companyCode + '\'' +
                ", dataSources='" + dataSources + '\'' +
                ", fuelType=" + fuelType +
                ", id=" + id +
                ", isValid=" + isValid +
                ", modelCode='" + modelCode + '\'' +
                ", modelName='" + modelName + '\'' +
                ", modelNameEn='" + modelNameEn + '\'' +
                ", seriesId=" + seriesId +
                '}';
    }
}
