package com.volvo.bff.volvoworks.wechat.common.service.repairAssign;

import com.volvo.bff.volvoworks.wechat.common.model.dto.repairAssign.TtRoAssignDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.AssginqualityInspectionResultVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.RepairAssignWorkShopTraceDTO;

import java.util.List;
import java.util.Map;

public interface RepairAssignService {

    PageInfoDto queryRepairAssign(Map<String, String> queryParam);

    List<Map> queryRoLabourByRoNOss(String id);

    List<Map> queryAllRoAssign(String id);

    void updateAssign(TtRoAssignDTO dto);

    void selfInspectionRegistration(TtRoAssignDTO dto);

    String workshopTrace(RepairAssignWorkShopTraceDTO assignWorkShopTraceDTO);

    AssginqualityInspectionResultVo qualityInspection(String roNo);

    PageInfoDto queryLabour(String id);
}
