package com.volvo.bff.volvoworks.wechat.common.service.order;

import java.util.Map;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.QueryRepairingOrderResultV2Vo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.QueryStructureCheckRrquestV2Vo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;

public interface RepairOrderService {

    PageInfoDto searchRepairOrder(Map<String, String> queryParam);

    PageBean<QueryRepairingOrderResultV2Vo> queryAllRepairOrder(QueryStructureCheckRrquestV2Vo queryParams);
}
