package com.volvo.bff.volvoworks.wechat.common.model.vo.basedata;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("ResponseDTO")
public class ResponseDTO<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    private static Integer DEFAULT_SUCCESS_CODE = 200;
    private static String DEFAULT_SUCCESS_MSG = "成功";

    private static Integer DEFAULT_FAIL_CODE = 500;
    private static String DEFAULT_FAIL_MSG = "失败";

    @ApiModelProperty(value = "返回代码", required = true)
    private Integer code;
    @ApiModelProperty(value = "返回描述")
    private String msg;
    @ApiModelProperty(value = "返回数据")
    private T data;

    public ResponseDTO success() {
        this.code = DEFAULT_SUCCESS_CODE;
        this.msg = DEFAULT_SUCCESS_MSG;
        return this;
    }

    public ResponseDTO successMsg(String msg) {
        this.code = DEFAULT_SUCCESS_CODE;
        this.msg = msg;
        this.data = null;
        return this;
    }

    public ResponseDTO successData(T data) {
        this.code = DEFAULT_SUCCESS_CODE;
        this.msg = DEFAULT_SUCCESS_MSG;
        this.data = data;
        return this;
    }

    public ResponseDTO successMsgData(String msg, T data) {
        this.code = DEFAULT_SUCCESS_CODE;
        this.msg = msg;
        this.data = data;
        return this;
    }

    public ResponseDTO fail() {
        this.code = DEFAULT_FAIL_CODE;
        this.msg = DEFAULT_FAIL_MSG;
        return this;
    }

    public ResponseDTO fail(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
        return this;
    }

    public ResponseDTO failCode(Integer code) {
        this.code = code;
        this.msg = DEFAULT_FAIL_MSG;
        return this;
    }

    public ResponseDTO failMsg(String msg) {
        this.code = DEFAULT_FAIL_CODE;
        this.msg = msg;
        return this;
    }

    public ResponseDTO failData(T data) {
        this.code = DEFAULT_FAIL_CODE;
        this.msg = DEFAULT_FAIL_MSG;
        this.data = data;
        return this;
    }

    public ResponseDTO failCodeMsg(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
        return this;
    }

    public ResponseDTO failCodeData(Integer code, T data) {
        this.code = code;
        this.msg = DEFAULT_FAIL_MSG;
        this.data = data;
        return this;
    }

    public ResponseDTO failMsgData(String msg, T data) {
        this.code = DEFAULT_FAIL_CODE;
        this.msg = msg;
        this.data = data;
        return this;
    }

    public boolean successful() {
        return Objects.equals(this.code, DEFAULT_SUCCESS_CODE);
    }

    public boolean failed() {
        return !successful();
    }
}
