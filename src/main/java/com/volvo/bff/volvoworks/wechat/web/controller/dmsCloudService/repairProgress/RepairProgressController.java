package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.repairProgress;


import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.*;
import com.volvo.bff.volvoworks.wechat.common.service.workshop.RepairProgressService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 维修进度看板
 * <AUTHOR>
 * @Date  created in  2020/6/1
 */
@Api(value = "/repairManage/repairReception", tags = {"维修进度看板"})
@RestController
@RequestMapping("/repairManage/repairReception")
public class RepairProgressController {

    @Autowired
    private RepairProgressService repairProgressService;

    /**
     * 维修进度看板
     * <AUTHOR>
     * @Date  created in  2020/6/1
     */
    @ApiOperation(value = "维修进度看板", notes = "维修进度看板", httpMethod = "GET")
    @RequestMapping(value = "/repairProgress", method = RequestMethod.GET)
    public ResponseDTO<PageBean<RepairProgressResultVo>> getRepairProgress(RepairProgressParamsVo paramsVo) {
        return ResponseUtils.success(repairProgressService.getRepairProgress(paramsVo));
    }
}
