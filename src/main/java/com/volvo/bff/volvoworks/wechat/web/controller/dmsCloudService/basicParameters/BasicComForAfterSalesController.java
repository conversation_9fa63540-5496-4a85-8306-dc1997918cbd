package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.basicParameters;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.basedata.BasicComForAfterSalesService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Api("/basedata/BasicParametersAfterSales")
@RestController
@RequestMapping("/basedata/BasicParametersAfterSales")
public class BasicComForAfterSalesController {

    @Autowired
    private BasicComForAfterSalesService basicComService;
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @RequestMapping(value="/findAllRepairType",method=RequestMethod.GET)
    @ResponseBody
    public ResponseDTO<List<Map>> findAllRepairType(){
        List<Map> result=basicComService.queryAllRepairType();
        return ResponseUtils.success(result);
    }

    /**
     * 查询所有基础参数
     * @return
     */
    @ApiOperation(value = "查询所有基础参数", notes = "查询所有基础参数", httpMethod = "GET")
    @RequestMapping(method = RequestMethod.GET)
    @ResponseBody
    public ResponseDTO<Map<String, String>> findAllRepair(){
        return ResponseUtils.success(basicComService.findAllRepair());
    }
}
