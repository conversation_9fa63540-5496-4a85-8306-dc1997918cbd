package com.volvo.bff.volvoworks.wechat.common.advice;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.RestResultResponse;
import com.volvo.bff.volvoworks.wechat.common.utils.JSONUtil;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

@ControllerAdvice
public class ResponseAdvisor implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    /**
     * 在真正返回结果之前，修改返回值的格式
     * <AUTHOR>
     * @since 2018/9/19 0019
     */
    @Override
    public Object beforeBodyWrite(Object body,
                                  MethodParameter returnType,
                                  MediaType selectedContentType, Class<? extends HttpMessageConverter<?>>
                                          selectedConverterType,
                                  ServerHttpRequest request,
                                  ServerHttpResponse response) {
        if (body instanceof ResponseDTO){
            return body;
        }

        if (body instanceof RestResultResponse) {
            return body;
        }

        if (returnType.getMethod().getReturnType().equals(String.class)) {
            return JSONUtil.objectToJson(new RestResultResponse<>().data(body));
        }
        if (request.getURI().toString().contains("/swagger") || request.getURI().toString().contains("/v2/api-docs")|| request.getURI().toString().contains("/actuator")) {
            return body;
        }
        return new RestResultResponse<>().data(body);
    }
}
