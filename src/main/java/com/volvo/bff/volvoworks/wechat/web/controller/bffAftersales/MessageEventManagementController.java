package com.volvo.bff.volvoworks.wechat.web.controller.bffAftersales;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.workshop.MessageEventManagementService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/web-event/v1")
@Slf4j
public class MessageEventManagementController {

    @Autowired
    private MessageEventManagementService messageEventManagementService;
    
    /**
     * 更新是否已读
     * @param id
     * @return
     * 查询 mq 配置
     *
     * @return 配置信息
     */
    @ApiOperation(value = "更新是否已读", notes = "更新是否已读", httpMethod = "GET")
    @GetMapping("/updateMsgById")
    public ResponseDTO<Boolean> updateMsgById(@RequestParam(value = "id") String id) {
		log.info("MessageEventManagementController-updateMsgById : {}", id);
        return ResponseUtils.success(messageEventManagementService.updateMsgById(id));
    }
}
