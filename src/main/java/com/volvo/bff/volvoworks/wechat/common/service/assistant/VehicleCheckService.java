package com.volvo.bff.volvoworks.wechat.common.service.assistant;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.*;

/**
 * <AUTHOR>
 */
public interface VehicleCheckService {

    /**
     * show  查询所有车辆检查
     *
     * @param vehicleCheckQueryParamsVo 请求参数vo
     * @return IPage<VehicleCheckResultListVo>
     */
    PageBean<VehicleCheckResultListVo> getVehicleCheckList(VehicleCheckQueryParamsVo vehicleCheckQueryParamsVo);

    /**
     * show  查询车辆检查明
     * @param yjNo 请求参数
     * @param roNo 请求参数
     * @return
     */
    VehicleCheckDetailListResultVo getVehicleCheckDetail(String yjNo, String roNo);

    /**
     * show  保存车辆检查明细
     * @param vehicleCheckSaveVo 请求参数
     *
     * @return
     */
    void saveVehicleCheckDetail(VehicleCheckSaveVo vehicleCheckSaveVo);

    /**
     * show  查询车辆检查增修项
     * @param yjNo 请求参数
     * @param roNo 请求参数
     * @return
     */
    AdditionalTrainingResultVo getAdditionalTraining(String yjNo, String roNo);
}
