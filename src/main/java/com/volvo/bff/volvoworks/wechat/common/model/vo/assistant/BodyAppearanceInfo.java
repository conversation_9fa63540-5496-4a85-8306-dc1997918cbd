package com.volvo.bff.volvoworks.wechat.common.model.vo.assistant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("新增预检单车身外观参数Vo")
public class BodyAppearanceInfo {

    @ApiModelProperty(value = "标识code",name = "contentCode")
    private String contentCode;

    @ApiModelProperty(value = "状态码",name = "statusCode")
    private String statusCode;

    @ApiModelProperty(value = "状态码名称",name = "statusCode")
    private String contentName;

    @ApiModelProperty(value = "图片地址",name = "fileBaseUrl")
    private String fileBaseUrl;

    @ApiModelProperty(value = "录音地址",name = "recUrl")
    private String recUrl;

    @ApiModelProperty(value = "视频地址",name = "videoUrl")
    private String videoUrl;

    @ApiModelProperty(value = "备注",name = "remark3")
    private String remark3;


}
