package com.volvo.bff.volvoworks.wechat.web.controller.bffAftersales;

import com.volvo.bff.volvoworks.wechat.common.model.dto.vhc.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.vhc.VehicleHealthCheckService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @Description 车辆健康检查控制
 * @Date 2024/9/25 14:36
 */
@RestController
@RequestMapping("/vehicleHealthCheck/v1")
@Api(value = "车辆健康检查", tags = {"车辆健康检查"})
@Slf4j
@AllArgsConstructor
public class VehicleHealthCheckController {

    private final VehicleHealthCheckService vehicleHealthCheckService;


    @ApiOperation(value = "查询车辆健康检查详情", notes = "", httpMethod = "POST")
    @PostMapping("/getVehicleHealthCheckDetail")
    public ResponseDTO<VehicleHealthCheckDetailDto> getVehicleHealthCheckDetail(@RequestBody VehicleHealthCheckDetailParamDto vehicleHealthCheckDetailParamDto){
      return ResponseUtils.success(vehicleHealthCheckService.getVehicleHealthCheckDetail(vehicleHealthCheckDetailParamDto));
    }

    @ApiOperation(value = "根据大类id查询小类信息", notes = "", httpMethod = "GET")
    @GetMapping("/getVhcItemInfoByClassId")
    public ResponseDTO<List<VhcItemConfigInfoDto>> getVhcItemInfoByClassId(@RequestParam("classId") Integer classId, @RequestParam("configClassId") String configClassId){
        //根据大类配置表id查询大类
        return ResponseUtils.success(vehicleHealthCheckService.getVhcItemInfoByClassId(classId, configClassId));
    }

    @ApiOperation(value = "保存车辆健康检查信息", notes = "", httpMethod = "POST")
    @PostMapping("/saveVehicleHealthCheckInfo")
    public void saveVehicleHealthCheckInfo(@RequestBody VehicleHealthCheckInfoDto vehicleHealthCheckInfoDto){
        vehicleHealthCheckService.saveVehicleHealthCheckInfo(vehicleHealthCheckInfoDto);
    }
}
