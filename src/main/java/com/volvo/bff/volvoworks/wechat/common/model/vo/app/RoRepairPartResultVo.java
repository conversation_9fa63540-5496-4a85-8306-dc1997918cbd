package com.volvo.bff.volvoworks.wechat.common.model.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("工单维修零件信息")
public class RoRepairPartResultVo {

    @ApiModelProperty(value = "维修零件名称", name = "partName")
    private String partName;

    @ApiModelProperty(value = "零件数量", name = "partQuantity")
    private Double partQuantity;

//    @ApiModelProperty(value = "金额", name = "partSalesAmount")
//    private Double partSalesAmount;
//    @ApiModelProperty(value = "折扣率", name = "discount",hidden = true)
//    private Double discount;

    @ApiModelProperty(value = "实收金额", name = "receiveAmount")
    private Double receiveAmount;

    public Double getReceiveAmount() {
        return receiveAmount;
    }

    public void setReceiveAmount(Double receiveAmount) {
        this.receiveAmount = receiveAmount;
    }

    public String getPartName() {
        return partName;
    }

    public void setPartName(String partName) {
        this.partName = partName;
    }

    public Double getPartQuantity() {
        return partQuantity;
    }

    public void setPartQuantity(Double partQuantity) {
        this.partQuantity = partQuantity;
    }

}
