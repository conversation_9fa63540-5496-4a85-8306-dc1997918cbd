package com.volvo.bff.volvoworks.wechat.common.model.vo.assistant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 快速工单查询结果vo
 * <AUTHOR>
 * @date 2020-03-13
 */
@Data
@ApiModel("维修历史结果vo")
public class RepairHistoryResultVo {

    @ApiModelProperty(value = "服务顾问",name = "serviceAdvisor")
    private String serviceAdvisor;

    @ApiModelProperty(value = "开单时间",name = "createdAt")
    private String createdAt;

    @ApiModelProperty(value = "工时费用",name = "labourAmount")
    private String labourAmount;

    @ApiModelProperty(value = "进厂行驶里程",name = "inMileage")
    private String inMileage;

    @ApiModelProperty(value = "维修项目list",name = "roLabourList")
    List<String> roLabourList;
}
