package com.volvo.bff.volvoworks.wechat.common.service.midendauthcenter;

import java.util.List;

import com.volvo.bff.volvoworks.wechat.common.model.dto.midendauthcenter.RoleDealerUserInfoRequestDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.midendauthcenter.RoleDealerUserInfoRespDTO;

public interface RoleDealerService {

	/**
	 * 根据角色代码查询本店员工
	 * @param dto
	 * @return
	 */
	List<RoleDealerUserInfoRespDTO> queryRoleDealerUser(RoleDealerUserInfoRequestDTO dto);
	
}
