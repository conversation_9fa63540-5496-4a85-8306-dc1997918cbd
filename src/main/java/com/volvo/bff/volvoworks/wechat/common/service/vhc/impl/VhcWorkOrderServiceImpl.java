package com.volvo.bff.volvoworks.wechat.common.service.vhc.impl;

import com.volvo.bff.volvoworks.wechat.common.exception.ServiceException;
import com.volvo.bff.volvoworks.wechat.common.feign.ApplicationMaintainManagementFeign;
import com.volvo.bff.volvoworks.wechat.common.model.dto.vhc.VhcConfirmRepairDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.vhc.VhcQuotedDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.vhc.VhcWorkOrderService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
public class VhcWorkOrderServiceImpl implements VhcWorkOrderService {

    @Autowired
    private ApplicationMaintainManagementFeign applicationMaintainManagementFeign;

    @Override
    public VhcQuotedDTO queryMaintenanceItems(String vhcNo) {
        log.info("checkedPartShort:{}", vhcNo);
        if(StringUtils.isBlank(vhcNo)){
            throw new ServiceException("检查单号不能为空");
        }
        DmsResultDTO<VhcQuotedDTO> queryMaintenanceItems = applicationMaintainManagementFeign.queryMaintenanceItems(vhcNo);
        ErrorConversionUtils.errorCodeConversion(queryMaintenanceItems);
        return queryMaintenanceItems.getData();
    }

    @Override
    public void pushCustomer(String vhcNo, String roNo, Integer flag, String itemIds) {
        log.info("pushCustomer:{},{},{}", vhcNo, roNo, flag);
        if(StringUtils.isBlank(vhcNo) || StringUtils.isBlank(roNo)){
            throw new ServiceException("检查单号,工单号不能为空");
        }
        DmsResultDTO<Void> pushCustomer = applicationMaintainManagementFeign.pushCustomer(vhcNo, roNo, flag, itemIds);
        ErrorConversionUtils.errorCodeConversion(pushCustomer);
    }

    @Override
    public void confirmRepair(VhcConfirmRepairDTO dto) {
        log.info("confirmRepair:{},", dto);
        if(Objects.isNull(dto)){
            throw new ServiceException("参数为空");
        }
        DmsResultDTO<Void> saveMaintenanceItems = applicationMaintainManagementFeign.confirmRepair(dto);
        ErrorConversionUtils.errorCodeConversion(saveMaintenanceItems);
    }
}
