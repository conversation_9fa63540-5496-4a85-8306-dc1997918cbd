package com.volvo.bff.volvoworks.wechat.common.model.dto.order;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 * TODO description
 * <AUTHOR>
 * @date 2017年4月14日
 */
@Data
public class TtMaintainTableDTO {

    private String isSelected;
    private String itemUpdateStatus;//新增修改或删除
    private Long itemId;
    private String itemIdLabour;
    private Long oldItemId;
    private String labourItemId;
    private String partInfix;//配件中缀
    private String consignExterior;//是否委外
    private String manageSortCode;//收费类别代码
    /*是否不修*/
    private String needlessRepair;
    private Double oemLimitPrice;//OEM销售限价
    private Double limitPrice;//销售限价
    private String partBatchNo;//进货批号
    private Integer preCheck;//是否预检
    private Double priceRate;//价格系数
    private Integer priceType;//价格类型
    private Integer printBatchNo;//预捡单打印流水号
    private Date printRpTime;//预先捡料单打印时间
    private String isFinished;
    private String cardId;
    private String activityCode;
    private String storageName;
    private String storageCode;
    private String storagePositionCode;
    private String isMainPart;
    private String partNo;
    private String partName;
    private String unitCode;
    private Double partQuantity;
    private Double useableStock;
    private Double lockedQuantity;
    private String isDiscount;
    private Double partSalesPrice;
    private int isShortFlag; //是否记入缺料
    private Double partSalesAmount;
    private String chargePartitionCode;
    private String sender;//发料人
    private String receiver;//领料人
    private String batchNo;
    private String outStockNo;
    private String labourName;
    private String labourCode;
    private String partModelGroupCodeSet;
    private Double discount;
    private Double partCostPrice;
    private Double partCostAmount;
    private Double addRate;
    private Integer nonOneOff;
    private String roNo;
    private String 	ischanged;
    private String partnames;
    private String isAddition;

    private String retrunFromOrder; //退前料的原始工单号

    private Object isBattery;  //the battery part's flag
    private String newBatterySequence;
    private String oldBatterySequence;
    private String oldBatteryPartno;
    private String pickType;

    private Long itemidHandProject;  //
    private String accountsType;   //帐类
    private String typeId;  //TYPE
    private String pqymentDepartmentId;  //付款部门
    private String jobNo;  //关联交修项目NO
    private Integer repairWay; //维修方式
    
    private String repairTypeCode;//维修类型代码

    private String isExchangePart;//是否交换件
    private String allowedExchange;//同意返还
    
    
    private String productKind;//产品类别 
    private String functionCode;//功能码
    
    private Integer confirmtlFlag;//退料标识

    private Integer isServeContractBuyOrder;//是否服务合同购买工单

    private Integer isUpkeepActivityOrder; //是否保养活动工单
    private Integer isHappenUpgradePrice;//是否产生升级差价
    private String oldPartNo;   //升级前-原零件代码
    private Long itemIdReturnMaterial;// 退料id
    private String servicePkgCode;// 服务套餐代码
    private Integer flowId; // 流水id
    private Integer isUpgrade;//是否可升级
    //非数据库字段
    private Double oldPartSalesPrice;
    private Integer REASON;
    /**
     * 剩余数量-传入-（新增时pc端就是总数量app是直接查出来的）
     * */
    private BigDecimal residualQuantity;


    /**
     * 溯源件数量-传入-（新增时根据接口写入app是直接查出来的）
     * */
    private BigDecimal sourceInQuantity;

    /**
     * 溯源件剩余数量-传入
     * */
    private BigDecimal sourceResidualQuantity;

    /**
     * 入库的溯源码
     * */
    private List<String> sourceCodes;
    /*
     * app传入，总数量
     * */
    private BigDecimal practicalQuantity;

    //数据库不存在字段
    private String recordId;

    private BigDecimal sourcePracticalQuantity;

    /**
     * 标准工时关联工时代码
     */
    private String standardLabourCode;

    @ApiModelProperty(value="零件的权益包类型")
    private String interestType;

    public String getLabourCode() {
        return labourCode;
    }

    public void setLabourCode(String labourCode) {
        change(labourCode);
    }

    private void change(String labourCode){
        if(labourCode!=null && labourCode.indexOf('~')!=-1){
            String s[]=labourCode.split("~");
            this.labourCode = s[0];
            this.labourItemId=s[1];
        }else{
            this.labourCode=labourCode;
        }
    }
}

