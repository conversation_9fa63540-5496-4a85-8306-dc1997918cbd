package com.volvo.bff.volvoworks.wechat.common.model.vo.assistant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 快速工单查询Vo
 * <AUTHOR>
 * @date  2020-03-13
 */
@Data
@ApiModel("快速工单查询Vo")
public class QuickRepairOrderQueryParamsVo {
    @ApiModelProperty(value = "车牌号",name = "license")
    private String license;
    @ApiModelProperty(value = "车主编号",name = "ownerNo")
    private String ownerNo;
    @ApiModelProperty(value = "车主姓名",name = "ownerName")
    private String ownerName;
    @ApiModelProperty(value = "工单创建开始时间",name = "roCreateDateBegin")
    private String roCreateDateBegin;
    @ApiModelProperty(value = "工单创建结束时间",name = "roCreateDateEnd")
    private String roCreateDateEnd;
    @ApiModelProperty(value = "维修类型",name = "repairType")
    private String repairType;
    @ApiModelProperty(value = "服务工程师",name = "serviceAdvisor")
    private String serviceAdvisor;
    @ApiModelProperty(value = "指定技师",name = "chiefTechnician")
    private String chiefTechnician;
    @ApiModelProperty(value = "工单编号",name = "roNo")
    private String roNo;
    @ApiModelProperty(value = "经销商代码",name = "ownerCode")
    private String ownerCode;
    @ApiModelProperty(value = "交车状态",name = "deliveredVehicleStatus")
    private String deliveredVehicleStatus;




}
