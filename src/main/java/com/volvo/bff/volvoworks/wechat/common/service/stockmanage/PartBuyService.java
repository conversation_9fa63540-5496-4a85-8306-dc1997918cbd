package com.volvo.bff.volvoworks.wechat.common.service.stockmanage;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

public interface PartBuyService {

    //配件采购入库-主界面查询单据
    PageInfoDto queryStockInOrder(Map<String, String> queryParam);

    //配件采购入库-具体单据明细数据查询
    List<Map> queryOrderDetail(Map<String, String> queryParam);
}
