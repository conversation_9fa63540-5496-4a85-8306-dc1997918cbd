package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.basedata;

import com.volvo.bff.volvoworks.wechat.common.model.dto.basedata.ReportPayOffDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.basedata.ReportPayOffService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/basedata/reportPayOff")
@Api(tags = "配件报溢")
public class ReportPayOffController {

    @Autowired
    private ReportPayOffService reportPayOffService;


    @ApiOperation(value = "配件报溢界面报溢单查询", notes = "根据一个不必填条件查询配件报溢单", httpMethod = "GET")
    @RequestMapping(method = RequestMethod.GET)
    @ResponseBody
    public ResponseDTO<PageInfoDto> findAllList(@RequestParam Map<String, String> queryParam) {
        PageInfoDto pageInfoDto = reportPayOffService.findAllList(queryParam);
        return ResponseUtils.success(pageInfoDto);
    }


    @ApiOperation(value = "根据报溢单号查询报溢明细", notes = "根据报溢单号查询报溢明细", httpMethod = "GET")
    @RequestMapping(value = "/findItemByPartProfit", method = RequestMethod.GET)
    @ResponseBody
    public ResponseDTO<List<Map>>findItemByPartProfit(@RequestParam(value = "profitNo") String profitNo) {

        return ResponseUtils.success(reportPayOffService.findItemByPartProfit(profitNo));
    }


    @ApiOperation(value = "入账按钮", notes = "入账按钮", httpMethod = "POST")
    @RequestMapping(value = "/btnAccount", method = RequestMethod.POST)
    public ResponseDTO<ResponseEntity<ReportPayOffDTO>> btnAccount(@RequestBody ReportPayOffDTO dto, UriComponentsBuilder uriCB) {
        return ResponseUtils.success(reportPayOffService.btnAccount(dto));
    }
}
