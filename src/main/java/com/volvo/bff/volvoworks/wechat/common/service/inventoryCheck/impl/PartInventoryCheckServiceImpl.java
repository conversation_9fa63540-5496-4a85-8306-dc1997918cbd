package com.volvo.bff.volvoworks.wechat.common.service.inventoryCheck.impl;


import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.dto.stockmanage.InventoryItemDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.stockmanage.PartInventoryDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.stockmanage.PartInventorySourceCodeDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import com.volvo.bff.volvoworks.wechat.common.service.inventoryCheck.PartInventoryCheckService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
@Service
public class PartInventoryCheckServiceImpl implements PartInventoryCheckService {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Override
    public PageInfoDto findAllInventoryInfo(Map<String, String> map){
        DmsResultDTO<PageInfoDto> response = dmscloudServiceFeign.findAllInventoryInfo(map);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public Map<String, Object> findAllInventoryItemInfoById(String id, String num, Map<String, String> queryParam){
        DmsResultDTO<Map<String, Object>> response = dmscloudServiceFeign.findAllInventoryItemInfoById(id,num,queryParam);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public PartInventoryDTO btnConfirm(InventoryItemDTO dto){
        DmsResultDTO<PartInventoryDTO> response = dmscloudServiceFeign.btnConfirm(dto);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public String saveInventoryInfo(InventoryItemDTO dto){
        DmsResultDTO<String> response = dmscloudServiceFeign.saveInventoryInfo(dto);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public Map<String, Object> savePartInventorySourceCode(PartInventorySourceCodeDTO dto){
        DmsResultDTO<Map<String, Object>> response = dmscloudServiceFeign.savePartInventorySourceCode(dto);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }
}
