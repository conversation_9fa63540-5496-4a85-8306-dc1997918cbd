package com.volvo.bff.volvoworks.wechat.common.model.vo.accidentClues;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "线索联系人信息")
public class AccidentClueContact {

  @ApiModelProperty(value = "联系人")
  private String contacts;

  @ApiModelProperty(value = "手机号")
  private String contactsPhone;

  @ApiModelProperty(value = "客户ID")
  private Long oneId;

  @ApiModelProperty(value = "acId")
  private Long acId;

  @ApiModelProperty(value = "是否车主")
  private Integer isOwner = 10041002;

  @ApiModelProperty(value = "原联系人")
  private String originalContacts;

  @ApiModelProperty(value = "原手机号")
  private String originalContactsPhone;

  @ApiModelProperty(value = "id")
  private Long id;

  public AccidentClueContact() {
  }

  public AccidentClueContact(String contacts, String contactsPhone, Integer isOwner) {
    this.contacts = contacts;
    this.contactsPhone = contactsPhone;
    this.isOwner = isOwner;
  }
}
