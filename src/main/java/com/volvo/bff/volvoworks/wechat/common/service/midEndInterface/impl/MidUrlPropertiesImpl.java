package com.volvo.bff.volvoworks.wechat.common.service.midEndInterface.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.dto.basedata.LoginInfoDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.midEndInterface.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.midEndInterface.SeriesVO;
import com.volvo.bff.volvoworks.wechat.common.service.midEndInterface.MidUrlProperties;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class MidUrlPropertiesImpl  implements MidUrlProperties {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Override
    public List<ModelVO> getVehicleModelAll(){
        DmsResultDTO<List<ModelVO>> response = dmscloudServiceFeign.getVehicleModelAll();
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public List<SeriesVO> getAllSeriesCodeUrl(){
        DmsResultDTO<List<SeriesVO>> response = dmscloudServiceFeign.getVehicleSeriesAll();
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public List<BrandVO> getAllBrandCodeUrl(){
        DmsResultDTO<List<BrandVO>> response = dmscloudServiceFeign.getVehicleBrandAll();
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public LoginInfoDto getLoginInfo(){
        DmsResultDTO<LoginInfoDto> response = dmscloudServiceFeign.getLoginInfo();
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }
}
