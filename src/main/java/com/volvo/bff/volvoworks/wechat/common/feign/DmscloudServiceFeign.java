package com.volvo.bff.volvoworks.wechat.common.feign;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.accidentClues.AppointmentDetailResultVo;
import com.volvo.bff.volvoworks.wechat.common.model.dto.basedata.*;
import com.volvo.bff.volvoworks.wechat.common.model.dto.common.CommonConfigDto;
import com.volvo.bff.volvoworks.wechat.common.model.dto.order.ListAdMaintainDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.order.OrderConfirmVo;
import com.volvo.bff.volvoworks.wechat.common.model.dto.part.ListPartBuyItemDto;
import com.volvo.bff.volvoworks.wechat.common.model.dto.repairAssign.TtRoAssignDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.stockmanage.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.accidentClues.ClueRepairOrderResultVO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.accidentClues.RepairOrderHistoryParamsVO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.AllAppointmentResultListVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.AppointmentQueryParamsVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.bookingregister.BookingOrderParamsVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.midEndInterface.BrandVO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.midEndInterface.ModelVO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.midEndInterface.SeriesVO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.pad.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.print.PrintDataVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.print.PrintParamVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.reception.PzusedateilParemVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.AssginqualityInspectionResultVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.RepairAssignWorkShopTraceDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.RepairProgressParamsVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.RepairProgressResultVo;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;

import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 售后产品-customerRepair feign 调用接口
 * <AUTHOR>
 * @date 2020-06-16
 */
@FeignClient(name = "dmscloud-service",
url = "${mse-in.tob.dmscloud-service.url}",
path = "${mse-in.tob.dmscloud-service.path}")
public interface DmscloudServiceFeign {


    /**
     * 配件采购入库-主界面查询单据
     * @return
     */
    @GetMapping(value = "/stockmanage/partbuy/queryStockInOrder")
    @ApiOperation(value = "配件采购入库-主界面查询单据", notes = "配件采购入库-主界面查询单据")
    DmsResultDTO<PageInfoDto> queryClueRepairOrder(@RequestParam Map<String, String> queryParam);

    @GetMapping(value = "/stockmanage/partbuy/queryOrderDetail")
    @ApiOperation(value = "配件采购入库-具体单据明细数据查询", notes = "配件采购入库-具体单据明细数据查询")
    DmsResultDTO<List<Map>> queryOrderDetail(@RequestParam Map<String, String> queryParam);

    @GetMapping(value = "/stockmanage/partAllocateQuery/queryAllInfo")
    @ApiOperation(value = "调拨退回列表", notes = "调拨退回列表")
    DmsResultDTO<PageInfoDto> queryAllocateInfoPage(@RequestParam Map<String, String> map);

    @GetMapping(value = "/stockmanage/partAllocateQuery/queryInfoById/{id}")
    @ApiOperation(value = "调拨退回明细", notes = "调拨退回明细")
    DmsResultDTO<List<Map>> queryAllocateInfoPage(@PathVariable(value = "id") String id);

    @PostMapping(value = "/stockmanage/partAllocateQuery/feeSettlement")
    @ApiOperation(value = "调拨退回确认", notes = "调拨退回确认")
    DmsResultDTO<Void> feeSettlement(@RequestBody ListPartAllocateInItemDto listPartAllocateInItemDto);

    @GetMapping(value = "/order/repair/searchRepairOrder")
    @ApiOperation(value = "查询在修工单", notes = "查询在修工单")
    DmsResultDTO<PageInfoDto> searchRepairOrder(@RequestParam Map<String, String> queryParam);

    @GetMapping(value = "/basedata/ttRepairOrder/SearchMaintainPicking/queryMaintainPicking")
    @ApiOperation(value = "维修领料界面查询领料配件明细", notes = "维修领料界面查询领料配件明细")
    DmsResultDTO<Map<String, Object>> queryMaintainPicking(@RequestParam Map<String, String> queryParam);

    @PostMapping(value = "/basedata/ttRepairOrder/btnSave")
    @ApiOperation(value = "配件维修领料-保存", notes = "配件维修领料-保存")
    DmsResultDTO<Integer> saveMaintainPart(@RequestBody ListAdMaintainDTO listAdMaintainDTO);

    @PostMapping(value = "/basedata/ttRepairOrder/account", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "配件维修领料-入账", notes = "配件维修领料-入账")
    DmsResultDTO<String> account(@RequestBody ListAdMaintainDTO listAdMaintainDTO);

    @GetMapping(value = "/basedata/ttSalesPart/SearchSalesPart")
    @ApiOperation(value = "备件销售单弹窗查询", notes = "备件销售单弹窗查询")
    DmsResultDTO<PageInfoDto> SearchSalesPart(@RequestParam Map<String, String> queryParam);

    @GetMapping(value = "/basedata/ttSalesPart/item/{salesPartNo}")
    @ApiOperation(value = "备件明细查询", notes = "备件明细查询")
    DmsResultDTO<List<Map>> queryPartSalesItem(@PathVariable(value = "salesPartNo") String salesPartNo);

    @PostMapping(value = "/basedata/ttSalesPart/save", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "备件销售保存", notes = "备件销售保存")
    DmsResultDTO<String> savePartSales(@RequestBody ListTtSalesPartItemDTO listTtSalesPartItemDTO);

    @PostMapping(value = "/basedata/ttSalesPart/enterRecord")
    @ApiOperation(value = "备件销售出库", notes = "备件销售出库")
    DmsResultDTO<Void> accountPartSales(@RequestBody ListTtSalesPartItemDTO listTtSalesPartItemDTO);

    @GetMapping(value = "/stockmanage/partallocateouts")
    @ApiOperation(value = "配件调拨出库/调拨退回单", notes = "配件调拨出库/调拨退回单")
    DmsResultDTO<PageInfoDto> searchPartAllocateOut(@RequestParam Map<String, String> queryParam);

    @GetMapping(value = "/stockmanage/partallocateouts/Items/{ALLOCATE_OUT_NO}")
    @ApiOperation(value = "根据必要条件调拨出库单号查询出库/调拨退回单明细表数据", notes = "根据必要条件调拨出库单号查询出库/调拨退回单明细表数据")
    DmsResultDTO<List<Map>> getPartAllocateOutItems(@PathVariable(value = "ALLOCATE_OUT_NO") String allocateOutNo, @RequestParam Map<String, String> queryParam);

    @PostMapping(value = "/stockmanage/partallocateouts/appPartAllotOut")
    @ApiOperation(value = "APP配件调拨出库", notes = "APP配件调拨出库")
    DmsResultDTO<Void> appPartAllotOut(@RequestBody ListPartAllocateOutItemDto listPartAllocateOutItemDto);

    @PostMapping(value = "/stockmanage/partallocateouts/save", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "配件调拨出库/调拨退回", notes = "配件调拨出库/调拨退回")
    DmsResultDTO<String> savePartAllocateIn(@RequestBody ListPartAllocateOutItemDto listPartAllocateOutItemDto);

    @PostMapping(value = "/stockmanage/partallocateouts/enterRecord")
    @ApiOperation(value = "配件调拨出库/调拨退回提交（出库）", notes = "配件调拨出库/调拨退回提交（出库）")
    DmsResultDTO<Void> accountPartAllocateIn(@RequestBody ListPartAllocateOutItemDto listPartAllocateOutItemDto);

    @GetMapping(value = "/basedata/inventoryCheck")
    @ApiOperation(value = "查询盘点单信息", notes = "查询盘点单信息")
    DmsResultDTO<PageInfoDto> findAllInventoryInfo(@RequestParam Map<String, String> queryParam);

    @GetMapping(value = "/basedata/inventoryCheck/{id}/{num}")
    @ApiOperation(value = "根据盘点单号查询盘点单明细", notes = "根据盘点单号查询盘点单明细")
    DmsResultDTO<Map<String, Object>> findAllInventoryItemInfoById(@PathVariable(value ="id" ) String id, @PathVariable(value ="num" ) String num,@RequestParam Map<String, String> queryParam);

    @PostMapping(value = "/basedata/inventoryCheck/btnConfirm")
    @ApiOperation(value = "盘点确认按钮", notes = "盘点确认按钮")
    DmsResultDTO<PartInventoryDTO> btnConfirm(@RequestBody InventoryItemDTO dto);

    @PostMapping(value = "/basedata/inventoryCheck/saveInventoryInfo", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "保存按钮", notes = "保存按钮")
    DmsResultDTO<String> saveInventoryInfo(@RequestBody InventoryItemDTO dto);

    @GetMapping(value = "/basedata/reportPayOff")
    @ApiOperation(value = "配件报溢界面报溢单查询", notes = "配件报溢界面报溢单查询")
    DmsResultDTO<PageInfoDto> findAllList(@RequestParam Map<String, String> queryParam);

    @GetMapping(value = "/basedata/reportPayOff/findItemByPartProfit")
    @ApiOperation(value = "根据报溢单号查询报溢明细", notes = "根据报溢单号查询报溢明细")
    DmsResultDTO<List<Map>> findItemByPartProfit(@RequestParam(value = "profitNo") String profitNo);

    @PostMapping(value = "/basedata/reportPayOff/btnAccount")
    @ApiOperation(value = "入账按钮", notes = "入账按钮")
    DmsResultDTO<ResponseEntity<ReportPayOffDTO>> btnAccount(@RequestBody ReportPayOffDTO dto);

    @GetMapping(value = "/basedata/partBreakage/queryPartLossByLossNo")
    @ApiOperation(value = "报损单弹窗查询", notes = "报损单弹窗查询")
    DmsResultDTO<PageInfoDto> queryPartLossByLossNo(@RequestParam Map<String, String> param);

    @GetMapping(value = "/basedata/partBreakage/queryPartLossItem")
    @ApiOperation(value = "报损单弹窗查询", notes = "报损单弹窗查询")
    DmsResultDTO<List<Map>> queryPartLossItem(@RequestParam String lossNo);

    @GetMapping(value = "/basedata/partBreakage/outStorage")
    @ApiOperation(value = "报损单出库", notes = "报损单出库")
    DmsResultDTO<Map<String, Object>> outStorage(@RequestParam String lossNo,
                                                        @RequestParam String inventoryNo,
                                                        @RequestParam String operType,
                                                        @RequestParam String operReason);

    @PostMapping(value = "/basedata/inventoryCheck/savePartInventorySourceCode")
    @ApiOperation(value = "盘点扫码登记溯源零件溯源码", notes = "盘点扫码登记溯源零件溯源码")
    DmsResultDTO<Map<String, Object>> savePartInventorySourceCode(@RequestBody PartInventorySourceCodeDTO dto);

    @PostMapping(value = "/basedata/partStocks/getOutSourcePartQuantity")
    @ApiOperation(value = "查询出库时溯源件数量", notes = "查询出库时溯源件数量")
    DmsResultDTO<List<Map<String,Object>>> getOutSourcePartQuantity(@RequestBody List<Map<String,String>> params);

    @PostMapping(value = "/basedata/partStocks/getInSourcePartQuantity")
    @ApiOperation(value = "查询出库时溯源件数量", notes = "查询出库时溯源件数量")
    DmsResultDTO<List<Map<String,Object>>> getInSourcePartQuantity(@RequestBody List<Map<String,String>> params);

    @GetMapping(value = "/stockmanage/sourceCodeInfo/checkSourceCode")
    @ApiOperation(value = "校验当前的溯源码", notes = "校验当前的溯源码")
    DmsResultDTO<BigDecimal> checkSourceCode(@RequestParam Integer code, @RequestParam String jdShipNo, @RequestParam String businessNo, @RequestParam String partNo, @RequestParam String sourceCode, @RequestParam int dirDetail);

    @PostMapping(value = "/stockmanage/sourceCodeInfo/saveNoSystemRecordSourcePartInOutFlow")
    @ApiOperation(value = "无系统记录溯源件保存流水", notes = "无系统记录溯源件保存流水")
    DmsResultDTO<Void> saveNoSystemRecordSourcePartInOutFlow(@RequestBody SourcePartFlowDto req);

    @GetMapping(value = "/stockmanage/partReturn/queryPartReturn")
    @ApiOperation(value = "配件采购退货-主界面查询单据", notes = "配件采购退货主单查询")
    DmsResultDTO<PageDto> queryPartReturn(@RequestParam Map<String, Object> queryParam);

    @GetMapping(value = "/stockmanage/partReturn/queryPartReturnDetail")
    @ApiOperation(value = "配件采购退货-具体单据明细数据查询", notes = "配件采购退货-具体单据明细数据查询")
    DmsResultDTO<List<Map>> queryPartReturnDetail(@RequestParam Map<String, String> queryParam);

    @PostMapping(value = "/stockmanage/partReturn/deletePartReturn")
    @ApiOperation(value = "配件采购退货-作废", notes = "配件采购退货-作废")
    DmsResultDTO<Void> deletePartReturn(@RequestBody ListPartReturnDTO listPartReturnDto);

    @PostMapping(value = "/stockmanage/partReturn/partReturnOut", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "配件采购入库-出库", notes = "配件采购入库-出库")
    DmsResultDTO<String> partReturnOut(@RequestBody ListPartReturnDTO listPartReturnDto);

    @PostMapping(value = "/stockmanage/partbuy/enterRecord", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<String> accountStockBuy(@RequestBody ListPartBuyItemDto listPartBuyItemDTO);

    @ApiOperation(value = " 查询白名单", notes = "查询白名单")
    @GetMapping(value = "/whitelist/checkWhitelist")
    DmsResultDTO<Boolean> checkWhitelist(@RequestParam(value = "ownerCode") String ownerCode, @RequestParam(value = "modType") Integer modType, @RequestParam(value = "rosterType") Integer rosterType, @RequestParam(value = "vin") String vin);

    @GetMapping(value = "/orderHistoryInterfaceApi/queryClueRepairOrder")
    @ResponseBody
    @Headers({"content-type:application/json"})
    @ApiOperation(value = "线索需求查询工单", notes = "线索需求查询工单")
    DmsResultDTO<List<ClueRepairOrderResultVO>> getClueRepairOrder(@RequestParam RepairOrderHistoryParamsVO paramsVo);

    /**
     * 查询预约单明细
     * @param bookingOrderNo  预约单号
     * @return
     */
    @ApiOperation(value = "查询预约单明细", notes = "查询预约单明细")
    @GetMapping(value = "/AppointmentCheck/queryAppointmentDetail")
    DmsResultDTO<AppointmentDetailResultVo> queryAppointmentDetail(@RequestParam(value = "bookingOrderNo") String bookingOrderNo);

    @ApiOperation(value = "根据分组和KEY获取配置信息", notes = "根据分组和KEY获取配置信息")
    @GetMapping(value = "/common/config")
    DmsResultDTO<CommonConfigDto> getConfigByKey(@RequestParam String configKey, @RequestParam String groupType);

    @ApiOperation(value = "查询已派工未质检且派工技师为当前登录的工单信息", notes = "查询已派工未质检当前登录技师的工单信息")
    @GetMapping("/additionalRepairSuggestion/queryRepairOrder")
    DmsResultDTO<PageBean<QueryRepairingOrderResultVo>> queryRepairOrder(@RequestParam QueryStructureCheckRrquestVo queryParams);

    @ApiOperation(value = "查询工单增修项目", notes = "查询工单增修项目")
    @GetMapping("/additionalRepairSuggestion/queryAdditionalRepairSuggestion")
    DmsResultDTO<List<AdditionalRepairSuggestionResultVo>> queryAdditionalRepairSuggestion(@RequestParam("roNo") String roNo);

    @ApiOperation(value = "查询工单增修项目明细", notes = "查询工单增修项目明细")
    @GetMapping("/additionalRepairSuggestion/queryAdditionalRepairSuggestionDetail")
    DmsResultDTO<AdditionalRepairSuggestionResultVo> queryAdditionalRepairSuggestionDetail(@RequestParam("itemId") String itemId);

    @ApiOperation(value = "保存工单增修项目", notes = "保存工单增修项目")
    @PostMapping(value = "/additionalRepairSuggestion/saveAdditionalRepairSuggestion", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<String> saveAdditionalRepairSuggestion(@RequestBody SaveAdditionalRepairSuggestionVo saveParams);

    @ApiOperation(value = "根据vin查询VIDA工单列表", notes = "根据vin查询VIDA工单列表")
    @GetMapping("/additionalRepairSuggestion/getVidaListByVin")
    DmsResultDTO<PageBean<VidaOrderListResultVo>> getVidaListByVin(@RequestParam("vin") String vin);

    @ApiOperation(value = "删除", notes = "根据vin查询VIDA工单列表")
    @GetMapping("/additionalRepairSuggestion/deleteAdditionalRepairSuggestion")
    DmsResultDTO<Void> deleteAdditionalRepairSuggestion(@RequestParam("itemId") String itemId);

    @ApiOperation(value = "查询所有预约单", notes = "查询所有预约单")
    @GetMapping(value = "/AppointmentCheck/queryAllAppointment")
    DmsResultDTO<PageBean<AllAppointmentResultListVo>> queryAllAppointment(@Valid AppointmentQueryParamsVo appointmentQueryParamsVo);

    @ApiOperation(value = "查询预约单三种状态数量", notes = "查询预约单三种状态数量")
    @GetMapping(value = "/AppointmentCheck/queryStatusNum")
    DmsResultDTO<AppointmentStatusResultVo> queryStatusNum(@Valid AppointmentNumQueryParamsVo appointmentNumQueryParamsVo);

    @ApiOperation(value = "取消预约单", notes = "取消预约单")
    @GetMapping(value = "/AppointmentCheck/deleteAppointment")
    DmsResultDTO<Void> deleteAppointment(@RequestParam("bookingOrderNo") String bookingOrderNo);

    @ApiOperation(value = "修改预约进厂时间", notes = "修改预约单进厂时间")
    @GetMapping(value = "/AppointmentCheck/updateAppointmentDate")
    DmsResultDTO<Void> updateAppointmentDate(@RequestParam("bookingOrderNo") String bookingOrderNo,
                                             @RequestParam("bookingComeTime") String bookingComeTime);

    @ApiOperation(value = "查询派工信息", notes = "查询派工信息")
    @GetMapping(value = "/repairAssign/queryRepairAssign")
    DmsResultDTO<PageInfoDto> queryRepairAssign(@RequestParam Map<String, String> queryParam);

    @ApiOperation(value = "根据工单查询维修项目", notes = "根据工单查询维修项目")
    @GetMapping(value = "/repairAssign/queryRoLabourByRoNOess/{id}")
    DmsResultDTO<List<Map>> queryRoLabourByRoNOss(@PathVariable(value = "id") String id);

    @ApiOperation(value = "根据维修项目查询派工单所有", notes = "根据维修项目查询派工单所有")
    @GetMapping(value = "/repairAssign/queryAllRoAssign/{id}")
    DmsResultDTO<List<Map>> queryAllRoAssign(@PathVariable(value = "id") String id);

    @ApiOperation(value = "分项派工保存", notes = "分项派工保存", httpMethod = "PUT")
    @PutMapping(value = "/repairAssign/updateAssign")
    DmsResultDTO<Void> updateAssign(@RequestBody TtRoAssignDTO dto);

    @ApiOperation(value = "根据派工单查询维修项目", notes = "根据派工单查询维修项目")
    @GetMapping(value = "/repairAssign/queryLabour/{id}")
    DmsResultDTO<PageInfoDto> queryLabour(@PathVariable(value = "id") String id);

    @ApiOperation(value = "自检登记", notes = "自检登记", httpMethod = "PUT")
    @PutMapping(value = "/repairAssign/selfInspectionRegistration")
    DmsResultDTO<Void> selfInspectionRegistration(@RequestBody TtRoAssignDTO dto);

    @ApiOperation(value = "透明车间(派工上下工位数据留痕)", notes = "透明车间(派工上下工位数据留痕)")
    @PostMapping(value = "/repairAssign/workshopTrace", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<String> workshopTrace(@RequestBody RepairAssignWorkShopTraceDTO assignWorkShopTraceDTO);

    @ApiOperation(value = "质检页面查询带出数据查询", notes = "质检页面查询带出数据查询")
    @GetMapping(value = "/repairAssign/qualityInspection/{roNo}")
    DmsResultDTO<AssginqualityInspectionResultVo> qualityInspection(@PathVariable("roNo") String roNo);

    @ApiOperation(value = "暂停开始", notes = "暂停开始")
    @PostMapping(value = "/roPauseDetail/ZaneDetail/paintball", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<String> queryZaneDetail(@RequestBody List<PzusedateilParemVo> zaneDetail);

    @ApiOperation(value = "暂停结束", notes = "暂停结束")
    @PostMapping(value = "/roPauseDetail/ZaneDetail/paintbox", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<String> pauseDaelList(@RequestBody List<PzusedateilParemVo> zaneDetail);

    @ApiOperation(value = "查询所有预检单", notes = "查询所有预检单")
    @GetMapping(value = "/VehiclePreviewController/queryAllPreview")
    DmsResultDTO<PageBean<VehiclePreviewResultVo>> queryAllPreview(@Valid VehiclePreviewQueryParamsVo vehiclePreviewQueryParamsVo);

    @ApiOperation(value = "根据车牌查询客户信息", notes = "根据车牌查询客户信息")
    @GetMapping(value = "/VehiclePreviewController/queryCusInfoByLicense")
    DmsResultDTO<List<CustomerInfoResultVo>> queryCusInfoByLicense(@RequestParam("flag") String flag,@RequestParam("license") String license);

    @ApiOperation(value = "新增预检单", notes = "新增预检单")
    @PostMapping(value = "/VehiclePreviewController/savePreview", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<String> savePreview(@RequestBody VehiclePreviewSaveVo vehiclePreviewSaveVo);

    @ApiOperation(value = "查询预检单明细", notes = "查询预检单明细")
    @GetMapping(value = "/VehiclePreviewController/queryPreviewDetail")
    DmsResultDTO<VehiclePreviewDetailResultVo> queryPreviewDetail(@RequestParam("yjNo") String yjNo);

    @ApiOperation(value = "删除预检单", notes = "删除预检单")
    @GetMapping(value = "/VehiclePreviewController/deletePreview")
    DmsResultDTO<Void> deletePreview(@RequestParam("yjNo") String yjNo);

    @ApiOperation(value = "维修历史", notes = "维修历史")
    @GetMapping(value = "/AssistantCommonController/queryRepairHistory")
    DmsResultDTO<List<RepairHistoryResultVo>> queryRepairHistory(@RequestParam("vin")  String vin);

    @ApiOperation(value = "根据环检单查询返回所有", notes = "根据环检单查询返回所有")
    @GetMapping(value = "/padVehiclePreviewApi/queryOwnerVehicleResultAll")
    DmsResultDTO<PadCustomerRequireResultAllVo> queryOwnerVehicleResultAll(@RequestParam("yjNo") String yjNo);

    @ApiOperation(value = "车主信息查询", notes = "车主信息查询")
    @PostMapping(value = "/padVehiclePreviewApi/queryOwnerVehicle")
    DmsResultDTO<PadVehiclePreviewResultVo> queryOwnerVehicle(@RequestBody PadVehiclePreviewVo padVehiclePreviewVo);

    @ApiOperation(value = "探针接口", notes = "探针接口")
    @PostMapping(value = "/padQueryOwnerVehicleApi/log_transparent_workshop", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<String> logTransparentWorkshop(@RequestBody LogTransparentWorkshopVo logTransparentWorkshopVo);

    @ApiOperation(value = "创建环检单时确定入场时间", notes = "创建环检单时确定入场时间")
    @PostMapping(value = "/padQueryOwnerVehicleApi/queryVehicleEntryTime", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<String> queryVehicleEntryTime(@RequestBody PadQueryOwnerVo padQueryOwnerVo);

    @ApiOperation(value = "车主信息查询", notes = "车主信息查询")
    @PostMapping(value = "/padVehiclePreviewApi/checkOwnerVehicleVin", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<String> checkOwnerVehicleVin(@RequestBody PadVehiclePreviewVo padVehiclePreviewVo);

    @ApiOperation(value = "环检单完成按钮", notes = "环检单完成按钮")
    @GetMapping(value = "/padVehiclePreviewApi/precheckFinshed", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<String> precheckFinshed(@RequestParam("yjNo") String yjNo);

    @ApiOperation(value = "环检单-转工单", notes = "环检单-转工单", httpMethod = "GET")
    @GetMapping(value = "/padVehiclePreviewApi/savePreviewTransferOrder")
    DmsResultDTO<RepairResultVo> savePreviewTransferOrder(@RequestParam("yjNo") String yjNo,
                                                   @RequestParam(value = "ownerCode",required = false) String ownerCode,
                                                   @RequestParam(value = "signImgUrl",required = false) String signImgUrl);

    @ApiOperation(value = "新增环检单-第一页", notes = "新增环检单-第一页", httpMethod = "POST")
    @PostMapping(value = "/SurroundCheckController/saveSuroundCheck", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<String> saveSuroundCheck1(@RequestBody SurroundCheckVO surroundCheckVO);

    @ApiOperation(value = "新增环检单-第二页", notes = "新增环检单-第二页", httpMethod = "POST")
    @PostMapping(value = "/SurroundCheckController/saveSuroundCheck2", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<String> saveSuroundCheck2(@RequestBody PadPreviewInteriorVo appearanceCheckVO);

    @ApiOperation(value = "新增环检单-内饰", notes = "新增环检单-内饰", httpMethod = "POST")
    @PostMapping(value = "/SurroundCheckController/saveSuroundCheckInterior", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<String> saveSuroundCheckInterior(@RequestBody PadPreviewInteriorVo padPreviewInteriorVo);

    @ApiOperation(value = "新增环检单-客户需求", notes = "新增环检单-客户需求", httpMethod = "POST")
    @PostMapping(value = "/SurroundCheckController/savePreviewCustomerRequire", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<String> savePreviewCustomerRequire(@RequestBody PadCustomerRequireVo padCustomerRequireVo);

    @ApiOperation(value = "环检单图片上传", notes = "环检单图片上传", httpMethod = "POST")
    @PostMapping(value = "/SurroundCheckController/saveSendBaseFile", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<String> saveSendBaseFile(@RequestBody Map<String,String> map);

    @ApiOperation(value = "车辆燃料类型查询", notes = "车辆燃料类型查询", httpMethod = "GET")
    @GetMapping(value = "/basedata/freeSettlement/getFuelTypeByVin", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<String> getFuelTypeByVin(@RequestParam("vin") String vin);

    @ApiOperation(value = "通过预约单查询信息", notes = "通过预约单查询信息", httpMethod = "GET")
    @GetMapping(value = "/BookingRegister/getBookingOrderInfoByCode2")
    DmsResultDTO<BookingOrderParamsVo> getBookingOrderInfoByCode2(@RequestParam String bookingOrderNo);

    @GetMapping("/damagedPart/queryEstimate")
    DmsResultDTO<PageBean<DamagedEstimateOrderResultVo>> queryEstimateForDamaged(@SpringQueryMap DamagedEstimateOrderParamVo damagedEstimateOrderParamVo);

    @ApiOperation(value = "查询级联部件", notes = "查询级联部件", httpMethod = "GET")
    @GetMapping("/damagedPart/queryDamagedPartList")
    DmsResultDTO<List<DamagedPartResultDto>> queryDamagedPartList(@RequestParam String estimateNo, @RequestParam String locId);

    @ApiOperation(value = "定损维护部件保存", notes = "定损维护部件保存", httpMethod = "POST")
    @PostMapping(value = "/damagedPart/saveDamaged", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<String> saveDamaged(@RequestBody SaveDamagedPartVo saveDamagedPartVo);

    @ApiOperation(value = "查询定损维护部位结果", notes = "查询定损维护部位结果", httpMethod = "GET")
    @GetMapping("/damagedPart/queryDamagedPart")
    DmsResultDTO<SaveDamagedPartVo> queryDamagedPart(@SpringQueryMap SaveDamagedPartVo saveDamagedPartVo);

    @ApiOperation(value = "查询定损维护部位结果", notes = "查询定损维护部位结果", httpMethod = "GET")
    @GetMapping("/damagedPart/queryEstimateDetail")
    DmsResultDTO<PageBean<EstimateDetailResultVo>> queryEstimateDetail(@RequestParam("estimateNo") String estimateNo);

    @ApiOperation(value = "删除定损维护部位", notes = "删除定损维护部位", httpMethod = "GET")
    @GetMapping(value ="/damagedPart/deleteEstimate", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<String> deleteEstimate(@RequestParam("estimateNo") String estimateNo,@RequestParam("locId") String locId);

    @ApiOperation(value = "查询所有工单", notes = "获取系统中工单信息", httpMethod = "GET")
    @GetMapping("/assistant/repair/progress/findAll")
    DmsResultDTO<PageBean<QuickRepairOrderQueryResultVo>> findAllRepairOrder(@SpringQueryMap QuickRepairOrderQueryParamsVo queryParamsVo);

    @ApiOperation(value = "根据工单号查询工单明细", notes = "根据工单号查询工单明细", httpMethod = "GET")
    @GetMapping("/assistant/repair/progress/findRepairOrderInfoByRoNo/{orderNum}")
    DmsResultDTO<RepairOrderProgressQueryDetailResultVo> findRepairOrderInfoByRoNo(@PathVariable("orderNum") String roNo);

    @ApiOperation(value = "查询工单交车状态数量数据", notes = "获取工单交车状态数据", httpMethod = "GET")
    @GetMapping("/assistant/repair/progress/findSubmitCarStatus")
    DmsResultDTO<SubmitCarStatusResultVo> findSubmitCarStatus(@SpringQueryMap QuickRepairOrderQueryParamsVo paramsVo);

    @GetMapping("/queryRepairOrder/queryAllRepairOrder")
    DmsResultDTO<PageBean<QueryRepairingOrderResultVo>> queryAllRepairOrder(@SpringQueryMap QueryStructureCheckRrquestVo queryParams);

    @ApiOperation(value = "查询工单明细信息", notes = "查询工单明细信息", httpMethod = "GET")
    @GetMapping("/queryRepairOrder/queryRepairOrderDetail")
    DmsResultDTO<RepairOrderDetailsResultVo> queryRepairOrderDetail(@SpringQueryMap RepairOrderDetailParamsVo paramsVo);

    @ApiOperation(value = "扫码保存卡券oneId", notes = "扫码保存卡券oneId", httpMethod = "POST")
    @PostMapping("/queryRepairOrder/saveAvailableOneId")
    DmsResultDTO<Void> saveAvailableOneId(@Valid @RequestBody RepairOrderAvailableOneIdParamsVo paramsVo);

    @ApiOperation(value = "产品:查询全部车型 沃尔沃:查询所有车款", notes = "产品:查询全部车型 沃尔沃:查询所有车款", httpMethod = "GET")
    @GetMapping(value = "/midEnd/basicData/model")
    DmsResultDTO<List<ModelVO>> getVehicleModelAll();

    @ApiOperation(value = "产品:查询全部车系 沃尔沃:查询所有车型", notes = "产品:查询全部车系 沃尔沃:查询所有车型", httpMethod = "GET")
    @GetMapping(value = "/midEnd/basicData/series")
    DmsResultDTO<List<SeriesVO>> getVehicleSeriesAll();

    @ApiOperation(value = "查询全部品牌", notes = "查询全部品牌", httpMethod = "GET")
    @GetMapping(value = "/midEnd/basicData/brand")
    DmsResultDTO<List<BrandVO>> getVehicleBrandAll();

    @ApiOperation(value = "工单/结算单打印数据获取", notes = "工单/结算单打印数据获取", httpMethod = "POST")
    @PostMapping("/printBalanceRo/balancePrintData")
    DmsResultDTO<PrintDataVo> balancePrintData(@RequestBody PrintParamVo paramsVo);

    @ApiOperation(value = "维修进度看板", notes = "维修进度看板", httpMethod = "GET")
    @GetMapping(value = "/repairManage/repairReception/repairProgress")
    DmsResultDTO<PageBean<RepairProgressResultVo>> getRepairProgress(@RequestParam RepairProgressParamsVo paramsVo);

    @ApiOperation(value = "查询在修工单根据条件", notes = "查询在修工单根据条件", httpMethod = "POST")
    @PostMapping(value = "/structureCheckApi/queryStructureCheckByCondition")
    DmsResultDTO<PageBean<QueryStructureCheckResultVo>> queryStructureCheckByCondition(@RequestBody QueryStructureCheckRrquestVo queryStructureCheckRrquestVo);

    @ApiOperation(value = "根据工单号和车牌号查询维修工时", notes = "根据工单号和车牌号查询维修工时", httpMethod = "GET")
    @GetMapping(value = "/structureCheckApi/queryStructureCheckWorkHour")
    DmsResultDTO<PageBean<QueryStructureWorkHourResultVo>> queryStructureCheckWorkHourByLienceAndRoNO(@RequestParam("license") String license,@RequestParam("roNo") String roNo);

    @ApiOperation(value = "根据工单号和车牌号和维修工时代码查询单个维修工时", notes = "根据工单号和车牌号和维修工时代码查询单个维修工时", httpMethod = "GET")
    @GetMapping(value = "/structureCheckApi/queryOneStructureCheckWorkHour")
    DmsResultDTO<JSONObject> queryOneStructureCheckWorkHour(@RequestParam("itemId") Integer itemId, @RequestParam("roNo") String roNo, @RequestParam("whCode") String whCode);

    @ApiOperation(value = "保存或更新结构件维修工时", notes = "保存或更新结构件维修工时", httpMethod = "POST")
    @PostMapping(value = "/structureCheckApi/saveOrUpdateStructureOneWorkHour", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<String> saveOrUpdateStructureOneWorkHour(@RequestBody StructureWorkHourOneRequestVo structureWorkHourOneRequestVo);

    @ApiOperation(value = "查询所有车辆检查", notes = "查询所有预检单", httpMethod = "GET")
    @GetMapping(value = "/VehicleCheckController/getVehicleCheckList")
    DmsResultDTO<PageBean<VehicleCheckResultListVo>> getVehicleCheckList(@RequestParam VehicleCheckQueryParamsVo vehicleCheckQueryParamsVo);

    @ApiOperation(value = "查询车辆检查明细", notes = "查询车辆检查明细", httpMethod = "GET")
    @GetMapping(value = "/VehicleCheckController/getVehicleCheckDetail")
    DmsResultDTO<VehicleCheckDetailListResultVo> getVehicleCheckDetail(@RequestParam String yjNo,@RequestParam String roNo);

    @ApiOperation(value = "保存车辆检查明细", notes = "保存车辆检查明细", httpMethod = "POST")
    @RequestMapping(value = "/VehicleCheckController/saveVehicleCheckDetail", method = RequestMethod.POST)
    @ResponseBody
    DmsResultDTO<Void> saveVehicleCheckDetail(@RequestBody VehicleCheckSaveVo vehicleCheckSaveVo);

    @ApiOperation(value = "查询车辆检查增修项", notes = "查询车辆检查增修项", httpMethod = "GET")
    @GetMapping(value = "/getAdditionalTraining")
    DmsResultDTO<AdditionalTrainingResultVo> getAdditionalTraining(@RequestParam String yjNo,@RequestParam String roNo);

    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping(value = "/basedata/BasicParametersAfterSales/findAllRepairType")
    DmsResultDTO<List<Map>> findAllRepairType();

    @ApiOperation(value = "查询维修类型", notes = "查询维修类型", httpMethod = "GET")
    @GetMapping(value = "/basedataRepairtypes/findAllRepairType")
    DmsResultDTO<List<Map>> queryRepairtypes(@RequestParam("ownerCode") String ownerCode);

    @ApiOperation(value = "查询所有基础参数", notes = "查询所有基础参数", httpMethod = "GET")
    @GetMapping(value = "/basedata/BasicParametersAfterSales")
    DmsResultDTO<Map<String, String>> findAllRepair();

    @ApiOperation(value = "获取登陆人信息", notes = "获取登陆人信息", httpMethod = "GET")
    @GetMapping(value = "/midEnd/basicData/getLoginInfo")
    DmsResultDTO<LoginInfoDto> getLoginInfo();

    @ApiOperation(value = "判断工单结算是否已确认", notes = "判断工单结算是否已确认", httpMethod = "POST")
    @PostMapping(value = "/orderConfirm/getorderStatementConfirm", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<String> getorderStatementConfirm(@RequestBody OrderConfirmVo confirm);

    @ApiOperation(value = "工单结算确认", notes = "工单结算确认", httpMethod = "POST")
    @RequestMapping(value = "/orderConfirm/orderStatementConfirm", method = RequestMethod.POST)
    @ResponseBody
    DmsResultDTO<Void> orderStatementConfirm(@RequestBody OrderConfirmVo confirm);

    @ApiOperation(value = "获取登陆人信息", notes = "获取登陆人信息", httpMethod = "GET")
    @GetMapping(value = "/orderCporConfirm/selectWhitelistByOwnerCode/customerInterAspect")
    DmsResultDTO<Boolean> selectWhitelistByOwnerCode(@RequestParam("ownerCode") String ownerCode);

}
