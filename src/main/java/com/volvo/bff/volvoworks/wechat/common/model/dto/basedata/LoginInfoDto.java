package com.volvo.bff.volvoworks.wechat.common.model.dto.basedata;

import io.swagger.annotations.ApiModelProperty;

import java.util.Locale;

public class LoginInfoDto {
    @ApiModelProperty("集团、车厂唯一ID")
    private String appId;
    @ApiModelProperty("公司ID")
    private Long companyId;
    @ApiModelProperty("公司CODE")
    private String companyCode;
    @ApiModelProperty("当前登录公司CODE")
    private String ownerCode;
    @ApiModelProperty("二网时当前公司CODE")
    private String ownerParCode;
    @ApiModelProperty("数据类型：公司、部门、大区、小区")
    private Long orgType;
    @ApiModelProperty("数据来源：经销商、集团、车厂")
    private Long dataType;
    @ApiModelProperty("员工组织ID")
    private Long orgId;
    @ApiModelProperty("员工本组织以及以下ID")
    private String orgIds;
    @ApiModelProperty("登录方式：pc/APP")
    private String loginWay = "10051001";
    @ApiModelProperty("国际化")
    private Locale locale;
    @ApiModelProperty("员工ID")
    private Long employeeId;
    @ApiModelProperty("员工编号（过时）（售后用）")
    private String employeeNo;
    @ApiModelProperty("用户ID")
    private Long userId;
    @ApiModelProperty("用户编号（过时）")
    private String userCode;
    @ApiModelProperty("EMPID")
    private Long empId;
    @ApiModelProperty("职位编码")
    private Long appRole;
    @ApiModelProperty("集团代码")
    private String groupCode;
    @ApiModelProperty("用户名称")
    private String userName;
    @ApiModelProperty("经销商名称")
    private String dealerName;
    @ApiModelProperty("组织名称")
    private String orgName;
    @ApiModelProperty("经销商ID")
    private String ownerId;
    @ApiModelProperty("用户是否检查经销商代码是否与当前session 中代码一致")
    private Boolean leanCheck = true;
    @ApiModelProperty("组织代码")
    private String orgCode;
    @ApiModelProperty("员工编号手机号")
    private String mobile;
    @ApiModelProperty("用户职位ID")
    private String userOrgId;
    @ApiModelProperty("客服顾问ID")
    private String consultantId;
    @ApiModelProperty("用于网关存")
    private String uuid;

    public LoginInfoDto() {
    }

    public String getUserOrgId() {
        return this.userOrgId;
    }

    public void setUserOrgId(String userOrgId) {
        this.userOrgId = userOrgId;
    }

    public String getAppId() {
        return this.appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public Long getCompanyId() {
        return this.companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getCompanyCode() {
        return this.companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getOwnerCode() {
        return this.ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getOwnerParCode() {
        return this.ownerParCode;
    }

    public void setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
    }

    public Long getOrgType() {
        return this.orgType;
    }

    public void setOrgType(Long orgType) {
        this.orgType = orgType;
    }

    public Long getDataType() {
        return this.dataType;
    }

    public void setDataType(Long dataType) {
        this.dataType = dataType;
    }

    public Long getOrgId() {
        return this.orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgIds() {
        return this.orgIds;
    }

    public void setOrgIds(String orgIds) {
        this.orgIds = orgIds;
    }

    public String getLoginWay() {
        return this.loginWay;
    }

    public void setLoginWay(String loginWay) {
        this.loginWay = loginWay;
    }

    public Locale getLocale() {
        return this.locale;
    }

    public void setLocale(Locale locale) {
        this.locale = locale;
    }

    public Long getEmployeeId() {
        return this.employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public String getEmployeeNo() {
        return this.employeeNo;
    }

    public void setEmployeeNo(String employeeNo) {
        this.employeeNo = employeeNo;
    }

    public Long getUserId() {
        return this.userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserCode() {
        return this.userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public Long getEmpId() {
        return this.empId;
    }

    public void setEmpId(Long empId) {
        this.empId = empId;
    }

    public Long getAppRole() {
        return this.appRole;
    }

    public void setAppRole(Long appRole) {
        this.appRole = appRole;
    }

    public String getGroupCode() {
        return this.groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getUserName() {
        return this.userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getDealerName() {
        return this.dealerName;
    }

    public void setDealerName(String dealerName) {
        this.dealerName = dealerName;
    }

    public String getOrgName() {
        return this.orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOwnerId() {
        return this.ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public Boolean getLeanCheck() {
        return this.leanCheck;
    }

    public void setLeanCheck(Boolean leanCheck) {
        this.leanCheck = leanCheck;
    }

    public String getOrgCode() {
        return this.orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getMobile() {
        return this.mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getConsultantId() {
        return this.consultantId;
    }

    public void setConsultantId(String consultantId) {
        this.consultantId = consultantId;
    }

    public String getUuid() {
        return this.uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String toString() {
        return "LoginInfoDto [appId=" + this.appId + ", companyId=" + this.companyId + ", companyCode=" + this.companyCode + ", ownerCode=" + this.ownerCode + ", ownerParCode=" + this.ownerParCode + ", orgType=" + this.orgType + ", dataType=" + this.dataType + ", orgId=" + this.orgId + ", orgIds=" + this.orgIds + ", loginWay=" + this.loginWay + ", locale=" + this.locale + ", employeeId=" + this.employeeId + ", employeeNo=" + this.employeeNo + ", userId=" + this.userId + ", userCode=" + this.userCode + ", empId=" + this.empId + ", appRole=" + this.appRole + ", groupCode=" + this.groupCode + ", userName=" + this.userName + ", dealerName=" + this.dealerName + ", orgName=" + this.orgName + ", ownerId=" + this.ownerId + ", leanCheck=" + this.leanCheck + ", orgCode=" + this.orgCode + ", mobile=" + this.mobile + ", userOrgId=" + this.userOrgId + ", consultantId=" + this.consultantId + ", uuid=" + this.uuid + "]";
    }
}
