package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.basedata;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.basedata.PartStockService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Api("/basedata/partStocks")
@RestController
@RequestMapping("/basedata/partStocks")
public class PartStockController {

    @Autowired
    private PartStockService partStockService;

    @ApiOperation(value = "查询出库时溯源件数量", notes = "", httpMethod = "POST")
    @PostMapping("/getOutSourcePartQuantity")
    public ResponseDTO<List<Map<String,Object>>> getOutSourcePartQuantity(@RequestBody List<Map<String,String>> params ){
        return ResponseUtils.success(partStockService.getOutSourcePartQuantity(params));
    }

    @ApiOperation(value = "查询出库时溯源件数量", notes = "", httpMethod = "POST")
    @PostMapping("/getInSourcePartQuantity")
    public ResponseDTO<List<Map<String,Object>>> getInSourcePartQuantity(@RequestBody List<Map<String,String>> params ){
        return ResponseUtils.success(partStockService.getInSourcePartQuantity(params));
    }
}
