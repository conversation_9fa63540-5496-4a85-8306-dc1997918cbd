package com.volvo.bff.volvoworks.wechat.common.service.app;

import com.alibaba.fastjson.JSONObject;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.QueryStructureCheckResultVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.QueryStructureCheckRrquestVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.QueryStructureWorkHourResultVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.StructureWorkHourOneRequestVo;
import org.springframework.web.bind.annotation.RequestBody;

public interface StructureCheckService {

    PageBean<QueryStructureCheckResultVo> queryStructureCheckByCondition(QueryStructureCheckRrquestVo queryStructureCheckRrquestVo);

    PageBean<QueryStructureWorkHourResultVo> queryStructureCheckWorkHourByLienceAndRoNO(String license, String roNo);

    JSONObject queryOneStructureCheckWorkHour(Integer itemId, String roNo, String whCode);

    String saveOrUpdateStructureOneWorkHour(@RequestBody StructureWorkHourOneRequestVo structureWorkHourOneRequestVo);
}
