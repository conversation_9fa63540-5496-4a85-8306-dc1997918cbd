package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.assistant.VehiclePreviewService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/VehiclePreviewController")
@Api(value = "车辆预检",tags = {"服务助手-车辆预检"})
@Slf4j
public class VehiclePreviewController {

    @Autowired
    VehiclePreviewService vehiclePreviewService;


    /**
     * 查询所有预检单
     *
     * <AUTHOR>
     * @date 2020年3月16日
     */
    @ApiOperation(value = "查询所有预检单", notes = "查询所有预检单", httpMethod = "GET")
    @RequestMapping(value = "/queryAllPreview", method = RequestMethod.GET)
    public ResponseDTO<PageBean<VehiclePreviewResultVo>> queryAllPreview(@Valid VehiclePreviewQueryParamsVo vehiclePreviewQueryParamsVo){

        return ResponseUtils.success(vehiclePreviewService.queryAllPreview(vehiclePreviewQueryParamsVo));
    }


    @ApiOperation(value = "根据车牌查询客户信息", notes = "根据车牌查询客户信息", httpMethod = "GET")
    @RequestMapping(value = "/queryCusInfoByLicense", method = RequestMethod.GET)
    public ResponseDTO<List<CustomerInfoResultVo>> queryCusInfoByLicense(String flag, String license) {
        return ResponseUtils.success(vehiclePreviewService.queryCusInfoByLicense(flag,license));
    }

    /**
     * 新增预检单
     *
     * <AUTHOR>
     * @date 2020年3月17日
     */

    @ApiOperation(value = "新增预检单", notes = "新增预检单", httpMethod = "POST")
    @RequestMapping(value = "/savePreview", method = RequestMethod.POST)
    @ResponseBody
    public ResponseDTO<String> savePreview(@RequestBody VehiclePreviewSaveVo vehiclePreviewSaveVo){
        log.info("创建环检单入参：{}",vehiclePreviewSaveVo.toString());
        return ResponseUtils.success(vehiclePreviewService.savePreview(vehiclePreviewSaveVo));
    }


    @ApiOperation(value = "查询预检单明细", notes = "查询预检单明细", httpMethod = "GET")
    @RequestMapping(value = "/queryPreviewDetail", method = RequestMethod.GET)
    public ResponseDTO<VehiclePreviewDetailResultVo> queryPreviewDetail(String yjNo) {
        return ResponseUtils.success(vehiclePreviewService.queryPreviewDetail(yjNo));
    }


    @ApiOperation(value = "删除预检单", notes = "删除预检单", httpMethod = "GET")
    @RequestMapping(value = "/deletePreview", method = RequestMethod.GET)
    public void deletePreview(String yjNo) {
        vehiclePreviewService.deletePreview(yjNo);
    }


}
