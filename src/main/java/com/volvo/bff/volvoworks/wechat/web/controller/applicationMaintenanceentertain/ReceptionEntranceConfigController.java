package com.volvo.bff.volvoworks.wechat.web.controller.applicationMaintenanceentertain;

import com.alibaba.fastjson.JSONObject;
import com.volvo.bff.volvoworks.wechat.common.model.dto.applicationMaintenanceentertain.ReceptionEntrancePageConfigReqDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.applicationMaintenanceentertain.IReceptionEntranceConfigService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 进场接待配置化
 * 
 * <AUTHOR>
 * @version 1.0.0 2023-04-17
 */

@RestController
@RequestMapping("/reception-entrance-config/api/v1")
@Api(tags = "进场接待配置化", description = "进场接待配置化", produces = "application/json")
@RefreshScope
public class ReceptionEntranceConfigController {
	
    @Autowired
    private IReceptionEntranceConfigService receptionEntranceConfigService;
    

    @ApiOperation(value = "查询页面配置化", notes = "查询页面配置化", httpMethod = "POST")
    @PostMapping("/queryReceptionEntranceConfigByType")
	@CrossOrigin
    public ResponseDTO<List<JSONObject>> queryReceptionEntranceConfigByType(@RequestBody ReceptionEntrancePageConfigReqDTO pageConfigReq){
        return ResponseUtils.success(receptionEntranceConfigService.queryReceptionEntranceConfigByType(pageConfigReq));
    }

}