package com.volvo.bff.volvoworks.wechat.common.feign;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 售后产品-customerRepair feign 调用接口
 * <AUTHOR>
 * @date 2020-06-16
 */
@FeignClient(name = "dmscloud-customer",
url = "${mse-in.tob.dmscloud-customer.url}", 
path = "${mse-in.tob.dmscloud-customer.path}")
public interface DmscloudCustomerFeign {

    /**
     * 查询 保险公司
     * @date 20200819
     * <AUTHOR>
     * @param vo
     * @return
     */
    @GetMapping("/basedata/insuranceCo/insurance/dicts")
    DmsResultDTO<List<Map>> getInsuranceCompany(@RequestParam(value = "isValid") String isValid);




}
