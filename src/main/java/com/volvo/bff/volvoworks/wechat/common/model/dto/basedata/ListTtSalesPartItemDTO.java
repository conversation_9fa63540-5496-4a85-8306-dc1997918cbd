package com.volvo.bff.volvoworks.wechat.common.model.dto.basedata;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

// 此类用于配件销售保存和入账功能
@Data
public class ListTtSalesPartItemDTO {

    public Iterable<? extends PartBuyItemDTO> getpartSalestable;
    @ApiModelProperty(value = "配件销售单号", name = "salesPartNo", required = false)
    private String salesPartNo;//
    @ApiModelProperty(value = "工单号", name = "roNo", required = false)
    private String roNo;//工单号
    @ApiModelProperty(value = "销售订单编号", name = "soNo", required = false)
    private String soNo;//销售订单编号
    @ApiModelProperty(value = "客户代码", name = "customerCode", required = false)
    private String customerCode;
    @ApiModelProperty(value = "客户名称", name = "customerName", required = false)
    private String customerName;
    @ApiModelProperty(value = "锁定人", name = "lockUser", required = false)
    private String lockUser;
    @ApiModelProperty(value = "是否已结算", name = "balanceStatus", required = false)
    private Integer balanceStatus;//是否已结算
    @ApiModelProperty(value = "销售材料费", name = "salesPartAmount", required = false)
    private Double salesPartAmount;//销售材料费
    @ApiModelProperty(value = "备注", name = "remark", required = false)
    private String remark;//备注
    @ApiModelProperty(value = "首席顾问师", name = "consultant", required = false)
    private String consultant; //首席顾问师
    @ApiModelProperty(value = "VIN", name = "vin", required = false)
    private String vin;
    @ApiModelProperty(value = "配件销售单明细DTO", name = "partSalestable", required = true)
    private List<TtSalesPartItemDTO> partSalestable;
    @ApiModelProperty(value = "删除行的item_id", name = "items", required = false)
    private String items;//接收删除行的item_id
    @ApiModelProperty(value = "接收缺料行的item_id", name = "shortItems", required = false)
    private String shortItems;//接收缺料行的item_id
    @ApiModelProperty(value = "一进一出入库税率", name = "shortItems", required = false)
    private Double tax;
    @ApiModelProperty(value = "供应商代码", name = "customerCodeIn", required = false)
    private String customerCodeIn;
    @ApiModelProperty(value = "供应商名称", name = "customerNameIn", required = false)
    private String customerNameIn;
    @ApiModelProperty(value = "车牌号", name = "license", required = false)
    private String license;

    @ApiModelProperty(value="销售来源",name="salesSource",required=false )
    private Integer salesSource;
    
    
    @ApiModelProperty(value="销售类型",name="salesType",required=false )
    private Integer salesType;

    @ApiModelProperty(value="客户类型",name="type",required=false )
    private Integer type;
    @ApiModelProperty(value="销售日期",name="createdAt",required=false )
    private Date createdAt;

    @ApiModelProperty(value = "是否记录缺料", name = "recordShortPart", required = true)
    private String recordShortPart;

    /**
     * 原因选择
     * */
    @ApiModelProperty(value = "原因选择", name = "reasonType", required = false)
    private String reasonType;

    /**
     * 原因
     * */
    @ApiModelProperty(value = "原因", name = "reason", required = false)
    private String reason;

    /**
     * 出入库类型（APP/PC）
     * */
    @ApiModelProperty(value = "出入库类型（APP/PC）", name = "inOutSource", required = false)
    private String inOutSource;

    /*京东出货单号 ---一键出库场景下使用*/
    private String jdNo;


    /**
     * 异常溯源码信息
     * */
    private List<String> abnormalSourceCodes;


    public void setDefaultForOneClickDelivery(String jdNo) {
        this.inOutSource="PC";
        this.salesSource=83081001;
        this.reason="一键出库";
        this.type=83391002;
        this.jdNo=jdNo;
    }
}
