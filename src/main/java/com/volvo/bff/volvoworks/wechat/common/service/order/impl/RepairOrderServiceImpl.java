package com.volvo.bff.volvoworks.wechat.common.service.order.impl;

import java.util.Map;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lop.open.api.sdk.internal.fastjson.JSON;
import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.feign.DomainOrdersFeign;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.QueryRepairingOrderResultV2Vo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.QueryStructureCheckRrquestV2Vo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.QueryStructureCheckRrquestV3Vo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import com.volvo.bff.volvoworks.wechat.common.service.order.RepairOrderService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import com.volvo.bff.volvoworks.wechat.common.utils.TransmittableThreadLocalUtils;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class RepairOrderServiceImpl implements RepairOrderService {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    
    @Autowired
    private DomainOrdersFeign domainOrdersFeign;
    
    @Override
    public PageInfoDto searchRepairOrder(Map<String, String> queryParam) {
        DmsResultDTO<PageInfoDto> response = dmscloudServiceFeign.searchRepairOrder(queryParam);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }
	@Override
	public PageBean<QueryRepairingOrderResultV2Vo> queryAllRepairOrder(QueryStructureCheckRrquestV2Vo queryParams) {
		log.info("queryAllRepairOrder : {}", JSON.toJSONString(queryParams));
		
		// token 中获取经销商信息
		String ownerCode = TransmittableThreadLocalUtils.getValueBykey("ownerCode");
		QueryStructureCheckRrquestV3Vo queryStructureCheckRrquestV3Vo = new QueryStructureCheckRrquestV3Vo();
		BeanUtils.copyProperties(queryParams, queryStructureCheckRrquestV3Vo);
		queryStructureCheckRrquestV3Vo.setOwnerCode(ownerCode);
		log.info("queryAllRepairOrder： 请求入参 : {}", JSON.toJSONString(queryStructureCheckRrquestV3Vo));
		DmsResultDTO<PageBean<QueryRepairingOrderResultV2Vo>> queryAllRepairOrder = domainOrdersFeign.queryAllRepairOrder(queryStructureCheckRrquestV3Vo);
        ErrorConversionUtils.errorCodeConversion(queryAllRepairOrder);
		return queryAllRepairOrder.getData();
	}
}
