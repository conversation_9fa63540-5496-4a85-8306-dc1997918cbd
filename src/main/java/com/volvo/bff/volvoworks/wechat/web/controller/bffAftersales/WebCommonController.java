package com.volvo.bff.volvoworks.wechat.web.controller.bffAftersales;

import com.volvo.bff.volvoworks.wechat.common.model.dto.common.CommonConfigDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.common.CommonService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api("/api/v1/web/common")
@Slf4j
@RestController
@RequestMapping("/api/v1/web/common")
public class WebCommonController {

    @Autowired
    private CommonService commonService;

    @ApiOperation(value = "根据分组和KEY获取配置信息", notes = "根据分组和KEY获取配置信息", httpMethod = "GET")
    @GetMapping(value = "/common/config")
    ResponseDTO<CommonConfigDto> getConfigByKey(@RequestParam("configKey")  String configKey, @RequestParam("groupType")  String groupType) {
        return ResponseUtils.success(commonService.getConfigByKey(configKey,groupType));
    }
}
