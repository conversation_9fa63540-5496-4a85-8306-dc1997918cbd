package com.volvo.bff.volvoworks.wechat.common.model.vo.bookingregister;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


/**
 * 维修零件Vo
 * <AUTHOR>
 * @since 2020-04-07
 */
@Data
@ApiModel("维修零件Vo")
public class RepairPartParamsVo {

    @ApiModelProperty(name = "rowKey",value = "rowKey")
    private String rowKey;

    @ApiModelProperty(name = "零件itemId",value = "itemId")
    private Long itemId;

    @ApiModelProperty(name = "salesAmount",value = "salesAmount")
    private Double salesAmount;

    @ApiModelProperty(name = "仓库Code",value = "storageCode")
    private String storageCode;

    @ApiModelProperty(value = "仓库名称", name = "storageName")
    private String storageName;

    @ApiModelProperty(name = "仓库属性",value = "storageAttribute")
    private String storageAttribute;

    @ApiModelProperty(name = "零件代码",value = "partNo")
    private String partNo;

    @ApiModelProperty(name = "交修项目名称",value = "handRepairProjectName")
    private String handRepairProjectName;

    @ApiModelProperty(name = "新旧类型(81531001：新车 81531002：次新车 81531003：尊沃车 81531004：新能源车)",value = "newOldType")
    private Integer newOldType;

    @ApiModelProperty(value = "产品大类(业务类型): 83441001:车险 83441002:非车险", name = "bizType")
    private Integer		bizType;

    @ApiModelProperty(name = "延保产品类别",value = "productTypeName")
    private String productTypeName;

    @ApiModelProperty(name = "原零件代码",value = "oldPartNo")
    private String oldPartNo;

    @ApiModelProperty(name = "延保产品类型",value = "type")
    private String type;

    @ApiModelProperty(name = "维修类型代码",value = "repairTypeCode")
    private String repairTypeCode;

    @ApiModelProperty(name = "维修方式",value = "repairWay")
    private Integer repairWay;

    @ApiModelProperty(name = "收费类别代码",value = "manageSortCode")
    private String manageSortCode;

    @ApiModelProperty(name = "零件主档延保零件",value = "isWarranty")
    private String isWarranty;

    @ApiModelProperty(name = "估价单维修项目ID",value = "eLabourId")
    private Integer eLabourId;

    @ApiModelProperty(name = "付款部门ID",value = "pqymentDepartmentId")
    private Integer pqymentDepartmentId;

    @ApiModelProperty(name = "零件名称",value = "partName")
    private String partName;

    @ApiModelProperty(name = "TYPEID",value = "typeId")
    private String typeId;

    @ApiModelProperty(name = "是否入账",value = "isFinished")
    private Integer isFinished;

    @ApiModelProperty(name = "价格类型",value = "priceType")
    private Integer priceType;

    @ApiModelProperty(name = "关联交修项目",value = "itemIdHandProject")
    private Long jobNo;

    @ApiModelProperty(name = "收费区分",value = "chargePartitionCode")
    private String chargePartitionCode;

    @ApiModelProperty(name = "零件适用车型",value = "applicableVehicleType")
    private String applicableVehicleType;

    @ApiModelProperty(name = "关联维修工时",value = "itemIdLabour")
    private Long itemIdLabour;

    @ApiModelProperty(name = "零件数量",value = "partQuantity")
    private Double partQuantity;

    @ApiModelProperty(name = "销售单价",value = "partSalesPrice")
    private Double partSalesPrice;

    @ApiModelProperty(name = "价格系数",value = "priceRate")
    private Double priceRate;

    @ApiModelProperty(name = "销售金额",value = "partSalesAmount")
    private Double partSalesAmount;

    @ApiModelProperty(name = "不可打折",value = "isDiscount")
    private Integer isDiscount;

    @ApiModelProperty(name = "折扣率",value = "discount")
    private Double discount;

    @ApiModelProperty(name = "优惠金额",value = "discountAmount")
    private String discountAmount;

    @ApiModelProperty(name = "应收金额",value = "receivableAmount")
    private String receivableAmount;

    @ApiModelProperty(name = "实收金额",value = "receiveAmount")
    private String receiveAmount;

    @ApiModelProperty(name = "主要零件",value = "isMainPart")
    private Integer isMainPart;

    @ApiModelProperty(name = "库位代码",value = "storagePositionCode")
    private String storagePositionCode;

    @ApiModelProperty(name = "是否增项",value = "isAddition")
    private Integer isAddition;

    @ApiModelProperty(name = "是否不修",value = "needlessRepair")
    private Integer needlessRepair;

    @ApiModelProperty(name = "不修原因",value = "reason")
    private Integer  reason;

    @ApiModelProperty(name = "是否内返",value = "interReturn")
    private Integer interReturn;

    @ApiModelProperty(name = "是否预检",value = "preCheck")
    private Integer preCheck;

    @ApiModelProperty(name = "零件属性",value = "partAttribute")
    private String  partAttribute;

    @ApiModelProperty(name = "流水号",value = "batchNo")
    private Integer batchNo;

    @ApiModelProperty(name = "预检单打印流水号",value = "printBatchNo")
    private Double printBatchNo;

    @ApiModelProperty(name = "活动代码",value = "activityCode")
    private String activityCode;

    @ApiModelProperty(name = "组合代码",value = "packageCode")
    private String packageCode;

    @ApiModelProperty(name = "服务套餐代码",value = "servicePkgCode")
    private String servicePkgCode;

    @ApiModelProperty(name = "服务套餐代码",value = "setCode")
    private String setCode;

    @ApiModelProperty(name = "保养套餐代码",value = "maintainPackageCode")
    private String maintainPackageCode;

    @ApiModelProperty(name = "零件备注",value = "materialRemarks")
    private String materialRemarks;

    @ApiModelProperty(name = "销售价,销售单价",value = "salesPrice")
    private Double salesPrice;

    @ApiModelProperty(name = "索赔价",value = "claimPrice")
    private Double claimPrice;

    @ApiModelProperty(name = "最新进货价",value = "latestPrice")
    private Double latestPrice;

    @ApiModelProperty(name = "计量单位代码",value = "unitCode")
    private String unitCode;

    @ApiModelProperty(name = "计量单位名称",value = "unitName")
    private String unitName;

    @ApiModelProperty(name = "是否停用",value = "partStatus")
    private Integer partStatus;

    @ApiModelProperty(name = "车型组代码",value = "modelLabourCode")
    private String modelLabourCode;

    @ApiModelProperty(name = "配件成本金额",value = "partCostAmount")
    private Double partCostAmount;

    @ApiModelProperty(name = "配件成本单价",value = "partCostPrice")
    private Double partCostPrice;

    @ApiModelProperty(name = "工时代码",value = "labourCode")
    private String labourCode;

    @ApiModelProperty(name = "网点价",value = "nodePrice")
    private Double nodePrice;

    @ApiModelProperty(name = "替代备件",value = "optionNo")
    private String optionNo;

    @ApiModelProperty(name = "备注",value = "remark")
    private String remark;

    @ApiModelProperty(name = "保险价",value = "insurancePrice")
    private Double insurancePrice;

    @ApiModelProperty(name = "是否LDC零件",value = "ldcPart")
    private Integer ldcPart;

    @ApiModelProperty(name = "是否预收款",value = "BookingAccounts")
    private Integer bookingAccounts;

    @ApiModelProperty(name = "预约数量",value = "bookingQuantity")
    private Double bookingQuantity;

    @ApiModelProperty(name = "是否预留",value = "isObligated")
    private Integer isObligated;

    @ApiModelProperty(name = "关联的维修项目uid",value = "labourUidHidden")
    private String uidHidden;

    @ApiModelProperty(value = "是否服务合同购买工单",name = "IsServeContractBuyOrder")
    private Integer IsServeContractBuyOrder;

    @ApiModelProperty(value = "是否保养活动工单",name = "IsUpkeepActivityOrder")
    private Integer IsUpkeepActivityOrder;

    @ApiModelProperty(value = "是否产生升级差价",name = "IsHappenUpgradePrice")
    private Integer IsHappenUpgradePrice;

    //用于查询维修材料的字段

    @ApiModelProperty(name = "账面库存",value = "stockQuantity")
    private String stockQuantity;

    @ApiModelProperty(name = "可用库存",value = "useableStock")
    private BigDecimal useableStock;

    @ApiModelProperty(name = "最大库存",value = "maxStock")
    private String maxStock;

    @ApiModelProperty(name = "最小库存",value = "minStock")
    private String minStock;

    @ApiModelProperty(name = "帐类",value = "accountsType")
    private String accountsType;

    @ApiModelProperty(name = "锁定量",value = "lockedQuantity")
    private Double lockedQuantity;

    //自己后台翻译用的字段
    @ApiModelProperty(name = "帐类名称",value = "accountsType")
    private String accountsName;

    @ApiModelProperty(name = "是否有效",value = "isValid")
    private Integer isValid;

    @ApiModelProperty(name = "厂端建议销售价",value = "instructPrice")
    private BigDecimal instructPrice;

    @ApiModelProperty(name = "常规订单价格",value = "planPrice")
    private BigDecimal planPrice;

    @ApiModelProperty(name = "成本价",value = "costPrice")
    private BigDecimal costPrice;

    @ApiModelProperty(name = "VIDA单号",value = "vidaNo")
    private String vidaNo;
    @ApiModelProperty(name = "VIDA单号",value = "orderNo")
    private String orderNo;

    @ApiModelProperty(name = "vida主键",value = "vidaId")
    private String vidaId;

    @ApiModelProperty(name = "原vidaJobNo",value = "vidaJobNo")
    private String vidaJobNo;

    @ApiModelProperty(name = "维修项目名称",value = "labourName")
    private String labourName;

    @ApiModelProperty(name = "预约单号",value = "bookingOrderNo")
    private String bookingOrderNo;

    @ApiModelProperty(name = "预约数量",value = "number")
    private String number;

    @ApiModelProperty(name = "PG",value = "productKind")
    private String productKind;

    @ApiModelProperty(name = "FG",value = "functionCode")
    private String functionCode;

    @ApiModelProperty(name = "目标件成本价",value = "muCostPrice")
    private BigDecimal muCostPrice;

    @ApiModelProperty(name = "目标常规订单价格",value = "muPlanPrice")
    private BigDecimal muPlanPrice;

    @ApiModelProperty(name = "目标厂端建议销售价",value = "muInstructPrice")
    private BigDecimal muInstructPrice;


    @ApiModelProperty(name = "是否交换件",value = "isExchangePart")
    private String isExchangePart;

    @ApiModelProperty(name = "是否同意返还",value = "allowedExchange")
    private String allowedExchange;

    @ApiModelProperty(name = "FG",value = "functionGroup")
    private String functionGroup;

    @ApiModelProperty(name = "PG",value = "productGroup")
    private String productGroup;

//    @ApiModelProperty(name = "是否为待订货零件",value = "isPendingItem")
//    private Integer isPendingItem;


    @ApiModelProperty(name = "用户ID",value = "createdBy")
    private Integer createdBy;

//    序号= "jobNo"

    @ApiModelProperty(name = "所有者代码",value = "ownerCode")
    private String  ownerCode;

    @ApiModelProperty(name = "零件保修成本",value = "partCost")
    private Double  partCost;

    @ApiModelProperty(name = "零件金额",value = "partFee")
    private Double  partFee;

//   零件名称=partName

//   零件号"=partNo

    @ApiModelProperty(name = "零件单价",value = " partPrice")
    private Double  partPrice;

    @ApiModelProperty(name = "数量",value = "qty")
    private Integer qty;

    @ApiModelProperty(name = "适用维修类型",value = "jobType")
    private String jobType;

    @ApiModelProperty(name = "适用账类",value = "accountGroup")
    private String accountGroup;

    @ApiModelProperty(value = "建议维修配件ID", name = "suggestMaintainPartId")
    private Integer suggestMaintainPartId;

    @ApiModelProperty(value = "是否延保",name = "IsExtendInsurance")
    private String IsExtendInsurance ;

    @ApiModelProperty(value = "延保提供方 ", name = "provider", example = "(********：PICC ********：易保)")
    private String	provider;

    @ApiModelProperty(value = "乐观锁",name = "recordVersion")
    private String recordVersion;

    @ApiModelProperty(value = "是否可升级零部件",name = "isUpgrade")
    private Integer isUpgrade;

    /**-------------------------服务合同----------------------------------*/

    @ApiModelProperty(value = "服务合同ID",name = "careBuyedId")
    private Integer careBuyedId;

    @ApiModelProperty(value = "服务合同编号",name = "serveContractCode")
    private String activityNo;

    @ApiModelProperty(value = "服务合同名称",name = "serveContractName")
    private String activityName;

    @ApiModelProperty(value = "活动类型",name = "activityType")
    private Integer activityType;

    @ApiModelProperty(value = "保养类型",name = "upkeepType")
    private String upkeepType;

    @ApiModelProperty(value = "机油级别",name = "enginOilRank")
    private String enginOilRank;

    @ApiModelProperty(value = "保养次数=》保养活动可享用次数",name = "activityAvailableTimes")
    private Integer activityAvailableTimes;

    @ApiModelProperty(value = "该车使用权益的车龄上限",name = "carAgeMax")
    private Integer carAgeMax;

    @ApiModelProperty(value = "该车使用权益的里程上限",name = "mileageMax")
    private Integer mileageMax;

    @ApiModelProperty(value = "保养年数",name = "maintainYears")
    private Integer maintainYears;

    /**
     * 套餐代码
     */
    private List<MainFileReturnVO> mainFileList;


    @ApiModelProperty(name = "确认状态",value = "orderConfirmStatus")
    private String orderConfirmStatus;

    @ApiModelProperty(name = "不返还销售价",value = "noReturnSalesPrice")
    private BigDecimal noReturnSalesPrice;
    /**
     * 溯源件数量-传入-（新增时根据接口写入app是直接查出来的）
     * */
    @ApiModelProperty(name = "溯源件数量",value = "sourceInQuantity")
    private BigDecimal sourceInQuantity;

    private String isMainSource;

    private String isEdit;

    private String isSource;

    private String isJde;

    private Integer discountType;

    private String standardLabourCode;


    private Integer isTyre;

    private List<TireInfoVo> tireInfoList;
}
