package com.volvo.bff.volvoworks.wechat.common.service.basedata.impl;


import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.basedata.BasicComForAfterSalesService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class BasicComForAfterSalesServiceImpl implements BasicComForAfterSalesService {

    @Resource
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Override
    public List<Map> queryAllRepairType() {
        DmsResultDTO<List<Map>> res = dmscloudServiceFeign.findAllRepairType();
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public Map<String, String> findAllRepair() {
        DmsResultDTO<Map<String, String>> res = dmscloudServiceFeign.findAllRepair();
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }
}
