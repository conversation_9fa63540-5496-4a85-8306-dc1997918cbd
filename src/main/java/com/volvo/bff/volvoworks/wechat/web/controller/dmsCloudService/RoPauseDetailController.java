package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.reception.PzusedateilParemVo;
import com.volvo.bff.volvoworks.wechat.common.service.pauseDetail.RoPauseDetailservice;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(value = "/roPauseDetail", tags = {"维修派工新需求"})
@RestController
@RequestMapping("/roPauseDetail")
public class RoPauseDetailController {

    @Autowired
    RoPauseDetailservice pauseDeliverance;

    @ApiOperation(value = "暂停开始", notes = "暂停开始", httpMethod = "POST")
    @RequestMapping(value = "/ZaneDetail/paintball", method = RequestMethod.POST)
    public ResponseDTO<String> queryZaneDetail(@RequestBody List<PzusedateilParemVo> zaneDetail) {
        return ResponseUtils.success(pauseDeliverance.paintball(zaneDetail));
    }

    @ApiOperation(value = "暂停结束", notes = "暂停结束", httpMethod = "POST")
    @RequestMapping(value = "/ZaneDetail/paintbox", method = RequestMethod.POST)
    public ResponseDTO<String> pauseDaelList(@RequestBody List<PzusedateilParemVo> zaneDetail) {
        return  ResponseUtils.success(pauseDeliverance.paintbox(zaneDetail));
    }
}
