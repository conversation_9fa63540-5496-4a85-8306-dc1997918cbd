package com.volvo.bff.volvoworks.wechat.common.service.vhc;

import com.volvo.bff.volvoworks.wechat.common.model.dto.vhc.VhcConfirmRepairDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.vhc.VhcQuotedDTO;

public interface VhcWorkOrderService {
    /**
     * 推送用户
     * @return
     */
    void pushCustomer(String vhcNo, String roNo, Integer flag, String itemIds);

    /**
     * 查询维修项目明细
     * @param vhcNo
     * @return
     */
    VhcQuotedDTO queryMaintenanceItems(String vhcNo);

    /**
     * 代客户反向确认维修
     * @param dto
     * @return
     */
    void confirmRepair(VhcConfirmRepairDTO dto);

}
