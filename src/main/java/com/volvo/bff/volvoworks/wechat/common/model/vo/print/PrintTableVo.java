package com.volvo.bff.volvoworks.wechat.common.model.vo.print;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("结算单打印工单明细列表Vo")
@Data
public class PrintTableVo {

    // 经销商信息
    @ApiModelProperty(value = "序号", name = "line")
    private String line;
    @ApiModelProperty(value = "单行文字行数", name = "rowNum")
    private String rowNum;
    @ApiModelProperty(value = "工单号", name = "roNo")
    private String roNo;
    @ApiModelProperty(value = "项目", name = "project")
    private String project;
    @ApiModelProperty(value = "交修项目名称", name = "jobName")
    private String jobName;
    @ApiModelProperty(value = "交修项目代码", name = "jobNo")
    private String jobNo;
    @ApiModelProperty(value = "代码", name = "code")
    private String code;
    @ApiModelProperty(value = "产品来源", name = "productFrom")
    private String productFrom;
    @ApiModelProperty(value = "详细内容", name = "detailInfo")
    private String detailInfo;
    @ApiModelProperty(value = "数量", name = "num")
    private String num;
    @ApiModelProperty(value = "单价", name = "price")
    private String price;
    @ApiModelProperty(value = "销售价", name = "salesPrice")
    private String salesPrice;
    @ApiModelProperty(value = "折扣", name = "discountRate")
    private String discountRate;
    @ApiModelProperty(value = "计算用折扣", name = "discount")
    private Double discount;
    @ApiModelProperty(value = "结算价/总价/应收金额", name = "balancePrice")
    private String balancePrice;
    @ApiModelProperty(value = "数据种类(数据种类(LJ:零件，GS:工时，FJ:附加项目)", name = "dataType")
    private String dataType;
    @ApiModelProperty(value = "帐类", name = "chargePartitionName")
    private String chargePartitionName;
    @ApiModelProperty(value = "是否交换件", name = "isExchangePart")
    private String isExchangePart;
    @ApiModelProperty(value = "是否同意返还", name = "allowedExchange")
    private String allowedExchange;
    @ApiModelProperty(value = "完整零件号", name = "partNoAll")
    private String partNoAll;
    // 江苏结算单打印专用
    @ApiModelProperty(value = "属性", name = "nature")
    private String nature;
    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;

    // 延保服务合同专用
    @ApiModelProperty(value = "延保服务种类名称", name = "extendInsuranceType")
    private String extendInsuranceType;

    // 预打印、预检单专用
    @ApiModelProperty(value = "仓库名称", name = "storage")
    private String storage;

    // 预检单专用
    @ApiModelProperty(value = "库位", name = "storagePosition")
    private String storagePosition;
    @ApiModelProperty(value = "单位", name = "unit")
    private String unit;
    @ApiModelProperty(value = "配件车型组", name = "partModelGroup")
    private String partModelGroup;

    @ApiModelProperty(value = "是否溯源属性", name = "isMainSource")
    private String isMainSource;

    @ApiModelProperty(value = "帐类", name = "accountsType")
    private String accountsType;
    @ApiModelProperty(value = "仓库代码",name = "storageNum")
    private String storageNum;
    @ApiModelProperty(value = "是否原厂件",name = "isOem")
    private Integer isOem;

    @ApiModelProperty(value = "itemId", name = "itemId")
    private Long itemId;

    @ApiModelProperty(value = "确认状态", name = "orderConfirmStatus")
    private Integer orderConfirmStatus;

    @ApiModelProperty(value = "交修项目",name = "handRepairProjectName")
    private String handRepairProjectName;

    @ApiModelProperty(value = "是否增项",name = "是否增项")
    private Integer isAddition;
}
