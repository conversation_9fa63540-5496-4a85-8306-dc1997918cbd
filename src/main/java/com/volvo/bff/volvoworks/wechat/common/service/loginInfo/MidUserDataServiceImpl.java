package com.volvo.bff.volvoworks.wechat.common.service.loginInfo;

import com.alibaba.fastjson.JSON;
import com.volvo.bff.volvoworks.wechat.common.feign.UserServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.dto.login.CurrentLoginInfoDto;
import com.volvo.bff.volvoworks.wechat.common.model.dto.login.MidUserDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.RestResultResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
@Slf4j
@Service
public class MidUserDataServiceImpl implements MidUserDataService{
    @Autowired
    private UserServiceFeign userServiceFeign;

    @Override
    public CurrentLoginInfoDto getUserInfo(String uid) {
        RestResultResponse<MidUserDto> restResultResponse = userServiceFeign.getUserInfo();
        log.info("调用中台获取用户信息：{}", JSON.toJSONString(restResultResponse));
        MidUserDto midUserDto = restResultResponse.getData();
        if (ObjectUtils.isEmpty(midUserDto)) {
            return null;
        } else {
            CurrentLoginInfoDto loginInfoDto = new CurrentLoginInfoDto();
            loginInfoDto.setAppId("volvo");
            loginInfoDto.setUserName(midUserDto.getEmployeeName());
            loginInfoDto.setUserId(midUserDto.getUserId());
            loginInfoDto.setCompanyCode(midUserDto.getCompanyCode());
            loginInfoDto.setCompanyId(midUserDto.getCompanyId());
            loginInfoDto.setDataType(midUserDto.getDataType());
            loginInfoDto.setGroupCode(midUserDto.getGroupCode());
            String loginWay = midUserDto.getLoginWay();
            if (!StringUtils.isEmpty(loginWay) && !"pc".equalsIgnoreCase(loginWay)) {
                loginInfoDto.setLoginWay("10041001");
            } else {
                loginInfoDto.setLoginWay("10041002");
            }

            loginInfoDto.setOrgId(midUserDto.getOrgId());
            loginInfoDto.setUserOrgId(midUserDto.getUserOrgId().toString());
            loginInfoDto.setOwnerCode(midUserDto.getOwnerCode());
            log.info("转译后的用户信息loginInfoDTO:{}", loginInfoDto);
            return loginInfoDto;
        }
    }
}
