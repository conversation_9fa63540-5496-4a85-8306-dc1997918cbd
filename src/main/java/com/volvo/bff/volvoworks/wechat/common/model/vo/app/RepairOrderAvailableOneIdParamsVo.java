package com.volvo.bff.volvoworks.wechat.common.model.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

@ApiModel("扫码保存会员可用卡券oneId")
public class RepairOrderAvailableOneIdParamsVo {
    @ApiModelProperty(value = "工单号", name = "roNo")
    @NotNull
    private String roNo;

    @ApiModelProperty(value = "会员码", name = "roNo")
    @NotNull
    private Long oneId;

    public String getRoNo() {
        return roNo;
    }

    public void setRoNo(String roNo) {
        this.roNo = roNo;
    }

    public Long getOneId() {
        return oneId;
    }

    public void setOneId(Long oneId) {
        this.oneId = oneId;
    }
}
