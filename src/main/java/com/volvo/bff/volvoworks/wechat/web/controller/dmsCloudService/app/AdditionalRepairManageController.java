package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.app;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.app.AdditionalRepairSuggestionService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;

import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(value = "/additionalRepairSuggestion", tags = {"增项管理接口"})
@RestController
@RequestMapping("/additionalRepairSuggestion")
public class AdditionalRepairManageController {

    @Autowired
    private AdditionalRepairSuggestionService additionalRepairSuggestionService;

    @ApiOperation(value = "查询已派工未质检且派工技师为当前登录的工单信息", notes = "查询已派工未质检当前登录技师的工单信息", httpMethod = "GET")
    @GetMapping("/queryRepairOrder")
    public ResponseDTO<PageBean<QueryRepairingOrderResultVo>> queryRepairOrder(QueryStructureCheckRrquestVo queryParams){
        return ResponseUtils.success(additionalRepairSuggestionService.queryRepairOrder(queryParams));
    }


    @ApiOperation(value = "查询工单增修项目", notes = "查询工单增修项目", httpMethod = "GET")
    @GetMapping("/queryAdditionalRepairSuggestion")
    public ResponseDTO<List<AdditionalRepairSuggestionResultVo>> queryAdditionalRepairSuggestion(@RequestParam("roNo") String roNo){
        return ResponseUtils.success(additionalRepairSuggestionService.queryAdditionalRepairSuggestion(roNo));
    }


    @ApiOperation(value = "查询工单增修项目明细", notes = "查询工单增修项目明细", httpMethod = "GET")
    @GetMapping("/queryAdditionalRepairSuggestionDetail")
    public ResponseDTO<AdditionalRepairSuggestionResultVo> queryAdditionalRepairSuggestionDetail(@RequestParam("itemId") String itemId){
        return ResponseUtils.success(additionalRepairSuggestionService.queryAdditionalRepairSuggestionDetail(itemId));
    }


    @ApiOperation(value = "保存工单增修项目", notes = "保存工单增修项目", httpMethod = "POST")
    @PostMapping("/saveAdditionalRepairSuggestion")
    public ResponseDTO<String> saveAdditionalRepairSuggestion(@RequestBody SaveAdditionalRepairSuggestionVo saveParams){
        return ResponseUtils.success(additionalRepairSuggestionService.saveAdditionalRepairSuggestion(saveParams));
    }


    @ApiOperation(value = "根据vin查询VIDA工单列表", notes = "根据vin查询VIDA工单列表", httpMethod = "GET")
    @GetMapping("/getVidaListByVin")
    public ResponseDTO<PageBean<VidaOrderListResultVo>> getVidaListByVin(@RequestParam("vin") String vin){
        return ResponseUtils.success(additionalRepairSuggestionService.getVidaListByVin(vin));
    }


    @ApiOperation(value = "删除", notes = "根据vin查询VIDA工单列表", httpMethod = "GET")
    @GetMapping("/deleteAdditionalRepairSuggestion")
    public ResponseDTO<Void> deleteAdditionalRepairSuggestion(@RequestParam("itemId") String itemId){
       return ResponseUtils.success(additionalRepairSuggestionService.deleteAdditionalRepairSuggestion(itemId));
    }

}
