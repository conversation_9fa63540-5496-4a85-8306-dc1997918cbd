package com.volvo.bff.volvoworks.wechat.common.feign;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.order.OrderServiceInfoDeleteDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.order.OrderServiceInfoQueryDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.order.OrderServiceInfoRequestDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.QueryRepairingOrderResultV2Vo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.QueryStructureCheckRrquestV3Vo;
import com.volvo.bff.volvoworks.wechat.common.model.dto.pds.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.orderService.OrderServiceInfoVO;

/**
 *domain-maintain-orders feign 调用接口
 */
@FeignClient(name = "domain-maintain-orders",
url = "${mse-in.tob.domain-maintain-orders.url}",
path = "${mse-in.tob.domain-maintain-orders.path}")
public interface DomainOrdersFeign {
    /**
     * 保存服务信息
     * @param orderServiceInfoRequestDTO
     * @return
     */
    @RequestMapping(value = "/orderServiceInfo/v1/saveServiceInfomation", method = RequestMethod.POST)
    @ResponseBody
    DmsResultDTO<Void> saveServiceInfomation(@RequestBody OrderServiceInfoRequestDTO orderServiceInfoRequestDTO);
    /**
     * 删除服务信息
     */
    @RequestMapping(value = "/orderServiceInfo/v1/deleteServiceInfomation", method = RequestMethod.POST)
    @ResponseBody
    DmsResultDTO<Void> deleteServiceInfomation(@RequestBody OrderServiceInfoDeleteDTO dto);

    /**
     * 查询服务过程信息
     * @param dto
     */
    @RequestMapping(value = "/orderServiceInfo/v1/getServiceInfomation", method = RequestMethod.POST)
    @ResponseBody
    DmsResultDTO<List<OrderServiceInfoVO>> getServiceInfomation(@RequestBody OrderServiceInfoQueryDTO dto);


    @PostMapping("/queryRepairOrder/v2/queryAllRepairOrder")
    DmsResultDTO<PageBean<QueryRepairingOrderResultV2Vo>> queryAllRepairOrder(@RequestBody QueryStructureCheckRrquestV3Vo queryParams);

    /**
     * pds app 列表查询
     */
    @PostMapping("/pds/v1/listForAPP")
    ResponseDTO<ListForAPPrspDTO> listForAPP(@RequestBody ListForAPPrsqDTO dto);

    /**
     * pds app 创建前置接口
     */
    @PostMapping("/pds/v1/addBefore")
    ResponseDTO<AddBeforerspDTO> addBefore(@RequestBody AddBeforersqDTO dto);

    /**
     * pds app 创建/编辑
     */
    @PostMapping("/pds/v1/add")
    ResponseDTO<Integer> add(@RequestBody AddRsqDTO dto);

    /**
     * pds app 详细查询-分组模式
     */
    @PostMapping("/pds/v1/itemGroupMode")
    ResponseDTO<ItemGroupModeDTO> itemGroupMode(@RequestBody ItemRsqDTO dto);
}
