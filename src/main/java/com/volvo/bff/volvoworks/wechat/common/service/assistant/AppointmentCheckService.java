package com.volvo.bff.volvoworks.wechat.common.service.assistant;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.accidentClues.AppointmentDetailResultVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.*;

/**
 * <AUTHOR>
 */
public interface AppointmentCheckService {

    /**
     * show  查询所有预约单
     *
     * @param appointmentQueryParamsVo 请求参数vo
     * @return List<AllAppointmentResultListVo>
     */
    PageBean<AllAppointmentResultListVo> queryAllAppointment(AppointmentQueryParamsVo appointmentQueryParamsVo);

    /**
     * show  查询预约单的三张状态的数量
     *@param appointmentNumQueryParamsVo 请求参数vo
     * @return AppointmentStatusResultVo
     */
    AppointmentStatusResultVo queryStatusNum(AppointmentNumQueryParamsVo appointmentNumQueryParamsVo);

    /**
     * show  查询预约单的明细
     * @param bookingOrderNo 预约单号
     * @return AppointmentDetailResultVo
     */
    AppointmentDetailResultVo queryAppointmentDetail(String bookingOrderNo);

    /**
     * show  取消预约单
     * @param bookingOrderNo 预约单号
     * @return void
     */
    void deleteAppointment(String bookingOrderNo);

    /**
     * show  修改预约进厂时间
     * @param bookingOrderNo 预约单号 bookingComeTime 预约进厂时间
     * @param  bookingComeTime 预约进厂时间
     * @return void
     */
    void updateAppointmentDate(String bookingOrderNo,String bookingComeTime);

}
