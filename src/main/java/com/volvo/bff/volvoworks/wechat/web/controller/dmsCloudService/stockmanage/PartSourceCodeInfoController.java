package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.stockmanage;

import com.volvo.bff.volvoworks.wechat.common.model.dto.stockmanage.SourcePartFlowDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.stockmanage.PartSourceCodeInfoService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

@Api(value = "/stockmanage/sourceCodeInfo", tags = {"溯源件信息"})
@RestController
@RequestMapping("/stockmanage/sourceCodeInfo")
public class PartSourceCodeInfoController {

    @Autowired
    private PartSourceCodeInfoService partSourceCodeInfoService;

    @ApiOperation(value = "校验当前的溯源码", notes = "校验当前的溯源码", httpMethod = "GET")
    @GetMapping(value = "/checkSourceCode")
    @ResponseBody
    public ResponseDTO<BigDecimal> checkSourceCode(@RequestParam Integer code, @RequestParam String jdShipNo, @RequestParam String businessNo, @RequestParam String partNo, @RequestParam String sourceCode, @RequestParam int dirDetail) {
        return ResponseUtils.success(partSourceCodeInfoService.checkSourceCode(code,jdShipNo,businessNo, partNo,sourceCode,dirDetail));
    }


    @ApiOperation(value = "无系统记录溯源件保存流水", notes = "无系统记录溯源件保存流水", httpMethod = "POST")
    @PostMapping(value = "/saveNoSystemRecordSourcePartInOutFlow")
    public void saveNoSystemRecordSourcePartInOutFlow(@RequestBody SourcePartFlowDto req) {
        partSourceCodeInfoService.saveNoSystemRecordSourcePartInOutFlow(req);
    }
}
