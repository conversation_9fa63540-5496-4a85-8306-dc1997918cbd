package com.volvo.bff.volvoworks.wechat.common.feign;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.volvo.bff.volvoworks.wechat.common.model.dto.RequestDto;
import com.volvo.bff.volvoworks.wechat.common.model.dto.midendauthcenter.RoleDealerUserInfoMidRequestDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.midendauthcenter.RoleDealerUserInfoRespDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;

@FeignClient(name = "mid-end-auth-center",
        url = "${mse-in.mid.mid-end-auth-center.url}",
        path = "${mse-in.mid.mid-end-auth-center.path}")
public interface MidEndAuthCenterFeign {
	
    /**
     * 根据角色代码查询本店员工
     */
    @GetMapping(path = "/role/dealer/user", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<List<RoleDealerUserInfoRespDTO>> queryRoleDealerUser(@RequestBody RequestDto<RoleDealerUserInfoMidRequestDTO> request);
	
}
