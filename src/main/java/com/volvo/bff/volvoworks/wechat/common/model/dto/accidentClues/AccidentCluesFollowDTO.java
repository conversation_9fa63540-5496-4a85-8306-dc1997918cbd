package com.volvo.bff.volvoworks.wechat.common.model.dto.accidentClues;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 事故线索跟进
 */
@Data
public class AccidentCluesFollowDTO {

  private String appId;

  private String ownerCode;

  private String ownerParCode;

  private Integer orgId;

  /**
   * 主键ID
   */
  private Integer followId;

  /**
   * 经销商代码
   */
  private String dealerCode;

  /**
   * tt_accident_clues
   */
  private Integer acId;

  /**
   * 是否本店承保
   */
  private Integer isInsured;

  /**
   * 单方/双方事故
   */
  private Integer doubleAccident;

  /**
   * 事故责任划分
   */
  private Integer dutyDivision;

  /**
   *  是否现场报案
   */
  private Integer isReport;

  /**
   * 是否拖车服务
   */
  private Integer isTrailer;

  /**
   * 进厂工单号
   */
  private String roNo;

  /**
   * 跟进方式
   */
  private Integer followType;

  /**
   * 跟进内容/客户反馈
   */
  private String followText;

  /**
   * 跟进状态
   */
  private Integer followStatus;

  /**
   * 跟进失败原因
   */
  private Integer followFailWhy;

  /**
   * 下次跟进日期
   */
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date nextFollowDate;

  /**
   * 是否预约进店
   */
  private Integer isAppointment;

  /**
   * 客户预约进店
   */
  private Integer customerAppointment;

  /**
   * 客户预约进店时间
   */
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
  private Date appointmentIntoDate;

  //数据来源
  private Integer dataSources;

  //是否删除，1：删除，0：未删除"
  private Integer isDeleted;

  //是否有效"
  private Integer isValid;

  //创建时间"
  private Date createdAt;

  //创建人"
  private String createdBy;

  //更新时间"
  private Date updatedAt;

  //更新人
  private String updatedBy;

  //版本号（乐观锁）
  private Integer recordVersion;

  /**
   * 工单状态
   */
  private Integer roStatus;
  
  /**
   * 跟进人员name
   */
  private String followPeopleName;

  /**
   * 进厂时间
   */
  @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  private Date intoDealerDate;

  /**
   * 进厂经销商
   */
  private String intoDealerCode;
  
  private Integer followPeople;

  @ApiModelProperty(value = "是否有人受伤",name="isBruise")
  private Integer isBruise;

  @ApiModelProperty(value = "预约单号",name="bookingOrderNo")
  private String bookingOrderNo;

  @ApiModelProperty(value = "联系方式-手机号",name="contactsInformation")
  private String contactsInformation;

}
