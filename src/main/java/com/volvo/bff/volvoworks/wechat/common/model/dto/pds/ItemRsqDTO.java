package com.volvo.bff.volvoworks.wechat.common.model.dto.pds;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@ApiModel("ItemRsqDTO")
public class ItemRsqDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	// pdsID
	@ApiModelProperty(value = "ID", required = true)
	private Integer id;
}