package com.volvo.bff.volvoworks.wechat.common.service.order;

import com.volvo.bff.volvoworks.wechat.common.model.dto.order.OrderServiceInfoDeleteDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.order.OrderServiceInfoQueryDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.order.OrderServiceInfoRequestDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.orderService.OrderServiceInfoVO;

import java.util.List;

public interface OrderServiceInfoService {
    /**
     * 保存服务过程信息
     * @param orderServiceInfoRequestDTO
     */
    void saveServiceInfomation(OrderServiceInfoRequestDTO orderServiceInfoRequestDTO);
    /**
     * 删除服务过程信息
     */
    void deleteServiceInfomation(OrderServiceInfoDeleteDTO dto);
    /**
     * 查询服务过程信息
     */
    List<OrderServiceInfoVO> getServiceInfomation(OrderServiceInfoQueryDTO dto);
}
