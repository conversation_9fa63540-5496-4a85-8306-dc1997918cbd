package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.assistant;

import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.RepairHistoryResultVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.assistant.AssistantCommonService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 公用接口
 * TODO description
 *
 * <AUTHOR>
 * @date 2020年3月30日
 */
@RestController
@Api(value = "公用接口", tags = {"服务助手-公用接口"})
@RequestMapping("/AssistantCommonController")
public class AssistantCommonController {

    @Autowired
    AssistantCommonService assistantCommonService;


    @ApiOperation(value = "维修历史", notes = "维修历史", httpMethod = "GET")
    @RequestMapping(value = "/queryRepairHistory", method = RequestMethod.GET)
    public ResponseDTO<List<RepairHistoryResultVo>> queryRepairHistory(String vin) {

        return ResponseUtils.success(assistantCommonService.queryRepairHistory(vin));
    }

}
