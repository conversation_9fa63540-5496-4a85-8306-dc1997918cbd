package com.volvo.bff.volvoworks.wechat.common.service.stockmanage.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.dto.basedata.ListPartAllocateOutItemDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import com.volvo.bff.volvoworks.wechat.common.service.stockmanage.PartAllocateOutService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.Map;

@Service
public class PartAllocateOutServiceImpl implements PartAllocateOutService {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Override
    public PageInfoDto search(Map<String, String> map){
        DmsResultDTO<PageInfoDto> response = dmscloudServiceFeign.searchPartAllocateOut(map);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public List<Map> getPartAllocateOutItems(String allocateOutNo, Map<String, String> queryParam){
        DmsResultDTO<List<Map>> response = dmscloudServiceFeign.getPartAllocateOutItems(allocateOutNo, queryParam);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public void appPartAllotOut(ListPartAllocateOutItemDto listPartAllocateOutItemDto){
        DmsResultDTO<Void> response = dmscloudServiceFeign.appPartAllotOut(listPartAllocateOutItemDto);
        ErrorConversionUtils.errorCodeConversion(response);
    }

    @Override
    public String savePartAllocateIn(ListPartAllocateOutItemDto listPartAllocateOutItemDto){
        DmsResultDTO<String> response = dmscloudServiceFeign.savePartAllocateIn(listPartAllocateOutItemDto);
        ErrorConversionUtils.errorCodeConversion(response);
        return  response.getData();
    }

    @Override
    public void accountPartAllocateOut(ListPartAllocateOutItemDto listPartAllocateOutItemDto){
        DmsResultDTO<Void> response = dmscloudServiceFeign.accountPartAllocateIn(listPartAllocateOutItemDto);
        ErrorConversionUtils.errorCodeConversion(response);
    }

}
