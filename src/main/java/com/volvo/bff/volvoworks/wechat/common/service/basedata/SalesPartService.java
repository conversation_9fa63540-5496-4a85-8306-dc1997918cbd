package com.volvo.bff.volvoworks.wechat.common.service.basedata;

import com.volvo.bff.volvoworks.wechat.common.model.dto.basedata.ListTtSalesPartItemDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

public interface SalesPartService {

    PageInfoDto findAllSalesPartInfo(Map<String, String> map);

    List<Map> QueryPartSalesItem(String salesPartNo);

    String savePartSales(ListTtSalesPartItemDTO listTtSalesPartItemDTO);

    void accountPartSales(ListTtSalesPartItemDTO listTtSalesPartItemDTO);
}
