package com.volvo.bff.volvoworks.wechat.common.model.dto.part;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 弹框显示缺料的信息
 * </p>
 */
@Data
public class ShortPartResultDTO implements Serializable{

    private static final long serialVersionUID = 1L;
    /**
     * 配件代码
     */
    private String partNo;
    /**
     * 配件名称
     */
    private String partName;
    /**
     * 本次缺件数量
     */
    private BigDecimal materialShortageNum;
    /**
     * 工单累计数量
     */
    private BigDecimal workOrderTotal;
    /**
     * 库存数量
     */
    private BigDecimal stockQuantity;
    /**
     * 在途数量
     */
    private BigDecimal deliveryQuantity;
    /**
     * 需补货数量
     */
    private BigDecimal replenishmentNum;
    /**
     * 缺料在途说明
     */
    private String shortageDescription;
    /**
     * 差异数量
     */
    private BigDecimal differenceNum;
    /**
     * 是否勾线
     */
    private Integer isSelect;
    /**
     * 需补货数量NEW
     */
    private BigDecimal newReplenishmentNum;
    /**
     * 操作人
     */
    private String updateBy;
    /**
     * 零件采购价格
     */
    private BigDecimal claimPrice;
    /**
     * 埋点标识
     */
    private String partUpdateStatus;

}
