package com.volvo.bff.volvoworks.wechat.common.feign;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.workshop.CheckGroupDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.workshop.InviteUserDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.workshop.QwJSSDKDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.workshop.QwTokenDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "application-aftersales-management",
        url = "${mse-in.tob.application-aftersales-management.url}",
        path = "${mse-in.tob.application-aftersales-management.path}")
public interface ApplicationAftersalesManagementFeign {

	/**
	 * 拉人进群
	 * <p>
	 * 默认非管理员
	 */
	@ApiOperation("拉人进群")
	@PostMapping("/im/inviteUserAddGroup/notToken")
	DmsResultDTO inviteUser(@RequestBody InviteUserDTO inviteUser);


	/**
	 * 是否在群
	 *
	 */
	@PostMapping("/im/checkGroup")
	@ApiOperation("是否在群")
	DmsResultDTO<Integer> checkGroup(@RequestBody CheckGroupDTO checkGroupDTO);

	/**
	 * 获取token
	 */
	@GetMapping(value = "/qw/getNewbieToken")
	@ApiOperation(value = "根据code获取token")
	DmsResultDTO<QwTokenDTO> getNewbieTokenAsync(@RequestParam("code") String code);

	/**
	 * 获取密钥配置信息
	 */
	@PostMapping(value = "/qw/getQYWeChatAgentJSSDKConfig")
	@ApiOperation(value = "获取 JSSDK")
	DmsResultDTO<QwJSSDKDTO> getQYWeChatAgentJSSDKConfig(@RequestBody String url);

}
