package com.volvo.bff.volvoworks.wechat.common.service.assistant.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.assistant.VehicleCheckService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class VehicleCheckServiceImpl implements VehicleCheckService {

    @Resource
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Override
    public PageBean<VehicleCheckResultListVo> getVehicleCheckList(VehicleCheckQueryParamsVo queryParamsVo) {
        DmsResultDTO<PageBean<VehicleCheckResultListVo>> res = dmscloudServiceFeign.getVehicleCheckList(queryParamsVo);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public VehicleCheckDetailListResultVo getVehicleCheckDetail(String yjNo, String roNo) {
        DmsResultDTO<VehicleCheckDetailListResultVo> res = dmscloudServiceFeign.getVehicleCheckDetail(yjNo, roNo);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public void saveVehicleCheckDetail(VehicleCheckSaveVo queryParamsVo) {
        DmsResultDTO<Void> res = dmscloudServiceFeign.saveVehicleCheckDetail(queryParamsVo);
        ErrorConversionUtils.errorCodeConversion(res);
    }

    @Override
    public AdditionalTrainingResultVo getAdditionalTraining(String yjNo, String roNo) {
        DmsResultDTO<AdditionalTrainingResultVo> res = dmscloudServiceFeign.getAdditionalTraining(yjNo, roNo);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }
}
