package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.assistant;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.accidentClues.AppointmentDetailResultVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.assistant.AppointmentCheckService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@Api(value = "预约检查操作接口", tags = {"服务助手-预约检查操作接口"})
@RequestMapping("/AppointmentCheck")
public class AppointmentCheckController {


    @Autowired
    AppointmentCheckService appointmentCheckService;

    /**
     * 查询所有预约单
     *
     * <AUTHOR>
     * @date 2020年3月12日
     */
    @ApiOperation(value = "查询所有预约单", notes = "查询所有预约单", httpMethod = "GET")
    @RequestMapping(value = "/queryAllAppointment", method = RequestMethod.GET)
    public ResponseDTO<PageBean<AllAppointmentResultListVo>> queryAllAppointment(@Valid AppointmentQueryParamsVo appointmentQueryParamsVo) {
        return ResponseUtils.success(appointmentCheckService.queryAllAppointment(appointmentQueryParamsVo));
    }

    /**
     * 查询预约单的三张状态的数量
     *
     * <AUTHOR>
     * @date 2020年3月13日
     */
    @ApiOperation(value = "查询预约单三种状态数量", notes = "查询预约单三种状态数量", httpMethod = "GET")
    @RequestMapping(value = "/queryStatusNum", method = RequestMethod.GET)
    public ResponseDTO<AppointmentStatusResultVo> queryStatusNum(@Valid AppointmentNumQueryParamsVo appointmentNumQueryParamsVo) {
        return ResponseUtils.success(appointmentCheckService.queryStatusNum(appointmentNumQueryParamsVo));
    }



    @ApiOperation(value = "查询预约单明细", notes = "查询预约单明细", httpMethod = "GET")
    @RequestMapping(value = "/queryAppointmentDetail", method = RequestMethod.GET)
    public ResponseDTO<AppointmentDetailResultVo> queryAppointmentDetail(String bookingOrderNo) {
        return ResponseUtils.success(appointmentCheckService.queryAppointmentDetail(bookingOrderNo));
    }


    @ApiOperation(value = "取消预约单", notes = "取消预约单", httpMethod = "GET")
    @RequestMapping(value = "/deleteAppointment", method = RequestMethod.GET)
    public void deleteAppointment(String bookingOrderNo) {
        appointmentCheckService.deleteAppointment(bookingOrderNo);
    }


    @ApiOperation(value = "修改预约进厂时间", notes = "修改预约单进厂时间", httpMethod = "GET")
    @RequestMapping(value = "/updateAppointmentDate", method = RequestMethod.GET)
    public void updateAppointmentDate(String bookingOrderNo,String bookingComeTime) {
        appointmentCheckService.updateAppointmentDate(bookingOrderNo,bookingComeTime);
    }

}
