package com.volvo.bff.volvoworks.wechat.common.model.vo.app;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel("VIDA维修工时Vo")
public class VidaRepairWorkHourVo {
    @ApiModelProperty(value = "类型",name = "type")
    private Integer type;
    @ApiModelProperty(value = "项目编号",name = "itemNo")
    private String itemNo;
    @ApiModelProperty(value = "项目描述/说明",name = "itemDescribe")
    private String itemDescribe;
    @ApiModelProperty(value = "维修工时",name = "laborHour")
    private BigDecimal laborHour;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getItemNo() {
        return itemNo;
    }

    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    public String getItemDescribe() {
        return itemDescribe;
    }

    public void setItemDescribe(String itemDescribe) {
        this.itemDescribe = itemDescribe;
    }

    public BigDecimal getLaborHour() {
        return laborHour;
    }

    public void setLaborHour(BigDecimal laborHour) {
        this.laborHour = laborHour;
    }
}
