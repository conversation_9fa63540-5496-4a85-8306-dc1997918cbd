package com.volvo.bff.volvoworks.wechat.common.service.stockmanage.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.dto.basedata.ListPartReturnDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageDto;
import com.volvo.bff.volvoworks.wechat.common.service.stockmanage.PartReturnService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@Service
public class PartReturnServiceImpl implements PartReturnService {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Override
    public PageDto queryPartReturn(Map<String, Object> queryParam){
        DmsResultDTO<PageDto> response = dmscloudServiceFeign.queryPartReturn(queryParam);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public List<Map> queryPartReturnDetail(Map<String, String> queryParam){
        DmsResultDTO<List<Map>> response = dmscloudServiceFeign.queryPartReturnDetail(queryParam);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public void deletePartReturn(ListPartReturnDTO listPartReturnDto){
        DmsResultDTO<Void> response = dmscloudServiceFeign.deletePartReturn(listPartReturnDto);
        ErrorConversionUtils.errorCodeConversion(response);
    }

    @Override
    public String partReturnOut(ListPartReturnDTO listPartReturnDto){
        DmsResultDTO<String> response = dmscloudServiceFeign.partReturnOut(listPartReturnDto);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }
}
