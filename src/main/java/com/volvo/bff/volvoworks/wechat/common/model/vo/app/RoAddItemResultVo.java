package com.volvo.bff.volvoworks.wechat.common.model.vo.app;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("附加项目信息")
public class RoAddItemResultVo {

    @ApiModelProperty(value = "附加项目名称", name = "addItemName")
    private String addItemName;

//    @ApiModelProperty(value = "附加项目金额", name = "addItemAmount")
//    private Double addItemAmount;
//
//    @ApiModelProperty(value = "折扣率", name = "discount",hidden = true)
//    private Double discount;

    @ApiModelProperty(value = "实收金额", name = "partSalesAmount")
    private Double receiveAmount;

    public Double getReceiveAmount() {
        return receiveAmount;
    }

    public void setReceiveAmount(Double receiveAmount) {
        this.receiveAmount = receiveAmount;
    }

    public String getAddItemName() {
        return addItemName;
    }

    public void setAddItemName(String addItemName) {
        this.addItemName = addItemName;
    }

}
