package com.volvo.bff.volvoworks.wechat.common.service.basedata.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.dto.basedata.ListTtSalesPartItemDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import com.volvo.bff.volvoworks.wechat.common.service.basedata.SalesPartService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Vector;

@Service
public class SalesPartServiceImpl implements SalesPartService {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Override
    public PageInfoDto findAllSalesPartInfo(Map<String, String> map){
        DmsResultDTO<PageInfoDto> response = dmscloudServiceFeign.SearchSalesPart(map);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public List<Map> QueryPartSalesItem(String salesPartNo){
        DmsResultDTO<List<Map>> response = dmscloudServiceFeign.queryPartSalesItem(salesPartNo);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }
    @Override
    public String savePartSales(ListTtSalesPartItemDTO listTtSalesPartItemDTO){
        DmsResultDTO<String> response = dmscloudServiceFeign.savePartSales(listTtSalesPartItemDTO);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public void accountPartSales(ListTtSalesPartItemDTO listTtSalesPartItemDTO){
        DmsResultDTO<Void> response = dmscloudServiceFeign.accountPartSales(listTtSalesPartItemDTO);
        ErrorConversionUtils.errorCodeConversion(response);
    }

}
