package com.volvo.bff.volvoworks.wechat.common.model.dto.city;

import com.volvo.bff.volvoworks.wechat.common.constants.CommonConstant;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

/**
 * im 数据返回
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("返回集")
public class MidResponse<T>  implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 返回代码，0表示成功，其他表示失败
     */
    private String returnCode;

    /**
     * 返回描述
     */
    private String returnMessage;

    private String msgId;

    private String code;

    private String error;

    /**
     * 返回数据
     */
    private T data;

    public boolean isSuccess() {
        return Objects.equals(this.getReturnCode(), CommonConstant.SUCCESS_CODE) || Objects.equals(this.code, CommonConstant.SUCCESS_NEWBIE_CODE);
    }

    public boolean isFail() {
        return !isSuccess();
    }
}
