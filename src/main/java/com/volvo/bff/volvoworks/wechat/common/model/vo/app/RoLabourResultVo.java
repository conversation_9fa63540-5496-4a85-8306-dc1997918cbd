package com.volvo.bff.volvoworks.wechat.common.model.vo.app;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("工单维修工时明细Vo")
public class RoLabourResultVo {

    @ApiModelProperty(value = "维修工时名称", name = "labourName")
    private String labourName;

    @ApiModelProperty(value = "标准工时", name = "stdLabourHour")
    private Double stdLabourHour;

//    @ApiModelProperty(value = "金额", name = "labourAmount")
//    private Double labourAmount;
//
//    @ApiModelProperty(value = "折扣率", name = "discount",hidden = true)
//    private Double discount;
    @ApiModelProperty(value = "实收金额", name = "partSalesAmount")
    private Double receiveAmount;

    public Double getReceiveAmount() {
        return receiveAmount;
    }

    public void setReceiveAmount(Double receiveAmount) {
        this.receiveAmount = receiveAmount;
    }

    public String getLabourName() {
        return labourName;
    }

    public void setLabourName(String labourName) {
        this.labourName = labourName;
    }

    public Double getStdLabourHour() {
        return stdLabourHour;
    }

    public void setStdLabourHour(Double stdLabourHour) {
        this.stdLabourHour = stdLabourHour;
    }

}
