package com.volvo.bff.volvoworks.wechat.common.enums;

import java.util.Arrays;
import java.util.Optional;

import com.volvo.bff.volvoworks.wechat.common.exception.ExceptionEnum;

public enum GroupStatusConvertEnum {
	GROUP_NOT_CREATED(-2,"GROUP_NOT_CREATED","群未创建", false,ExceptionEnum.GROUP_NOT_CREATED),
	GROUP_DISSOLUTION(-1,"GROUP_DISSOLUTION","群解散", false,ExceptionEnum.GROUP_DISSOLUTION),
	
	NOT_IN_GROUPS(0,"NOT_IN_GROUPS","不在群", true,ExceptionEnum.SYSTEM_ERROR),
	IN_GROUPS(1,"IN_GROUPS","在群", true,ExceptionEnum.SYSTEM_ERROR);

	
	private int status;
	private String name;
	private String des;
	private Boolean flag;
	private ExceptionEnum exceptionEnum;

	private GroupStatusConvertEnum(Integer status, String name, String des, Boolean flag, ExceptionEnum exceptionEnum) {
		this.status = status;
		this.name = name;
		this.des = des;
		this.flag = flag;
		this.exceptionEnum = exceptionEnum;
	}

	public static GroupStatusConvertEnum getByStatus(int status) {
        Optional<GroupStatusConvertEnum> first = Arrays.stream(GroupStatusConvertEnum.values())
                .filter(value -> value.getStatus()==status).findFirst();
        return first.isPresent() ? first.get() : null;
    }

	public Integer getStatus() {
		return status;
	}

	public String getName() {
		return name;
	}

	public String getDes() {
		return des;
	}

	public ExceptionEnum getExceptionEnum() {
		return exceptionEnum;
	}

	public Boolean getFlag() {
		return flag;
	}
}
