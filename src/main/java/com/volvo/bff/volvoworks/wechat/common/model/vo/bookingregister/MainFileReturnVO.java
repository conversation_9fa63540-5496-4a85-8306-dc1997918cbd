package com.volvo.bff.volvoworks.wechat.common.model.vo.bookingregister;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 2020.9.7
 * yixing
 */
@ApiModel(value = "保养活动-套餐信息协同出参Vo")
@Data
public class MainFileReturnVO {

    @ApiModelProperty(value = "序号")
    private Integer sort;

    /**
     * 套餐编码
     */
    @ApiModelProperty(value = "套餐编码")
    private String setCode;
}
