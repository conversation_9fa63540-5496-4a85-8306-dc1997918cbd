package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.order;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.bookingregister.BookingOrderParamsVo;
import com.volvo.bff.volvoworks.wechat.common.service.bookingregister.BookingRegisterService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "/BookingRegister", tags = {"新版预约登记相关接口"})
@RequestMapping("/BookingRegister")
@RestController
public class BookingRegisterController {

    @Autowired
    BookingRegisterService bookingRegisterService;
    @ApiOperation(value = "通过预约单查询信息", notes = "通过预约单查询信息", httpMethod = "GET")
    @GetMapping(value = "getBookingOrderInfoByCode2")
    ResponseDTO<BookingOrderParamsVo> getBookingOrderInfoByCode2(String bookingOrderNo) {
        return ResponseUtils.success(bookingRegisterService.getBookingOrderInfoByCode2(bookingOrderNo));
    }
}
