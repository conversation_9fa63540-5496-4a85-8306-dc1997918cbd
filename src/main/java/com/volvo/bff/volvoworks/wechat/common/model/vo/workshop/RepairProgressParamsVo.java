package com.volvo.bff.volvoworks.wechat.common.model.vo.workshop;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
@ApiModel("维修进度看板查询参数")
public class RepairProgressParamsVo {
    @ApiModelProperty(value = "经销商",name = "ownerCode")
    private String ownerCode;

    @ApiModelProperty(value = "维修类型",name = "roStatus")
    private String repairTypeCode;

    @ApiModelProperty(value = "维修类型",name = "roStatus")
    private String roStatus;

//    @ApiModelProperty(value = "品牌",name = "brand")
//    private String brand;

    @ApiModelProperty(value = "车牌号/VIN",name = "licenseOrVin")
    private String licenseOrVin;

    @ApiModelProperty(value = "开单日期",name = "roCreateDateBegin")
    private String roCreateDateBegin;

    @ApiModelProperty(value = "开单日期",name = "roCreateDateEnd")
    private String roCreateDateEnd;

    @ApiModelProperty(value = "工单号",name = "roNo")
    private String roNo;

    @ApiModelProperty(value = "服务顾问",name = "serviceAdviser")
    private String serviceAdvisor;

    @ApiModelProperty(value = "距离交车时间",name = "carReturnTimeBegin")
    private Integer carReturnTimeBegin;

    @ApiModelProperty(value = "距离交车时间",name = "carReturnTimeEnd")
    private Integer carReturnTimeEnd;

    @ApiModelProperty(value = "服务顾问list",name = "serviceAdvisorList")
    private List<String> serviceAdvisorList;

    @ApiModelProperty(value = "维修类型list",name = "repairTypeList")
    private List<String> repairTypeList;

    @ApiModelProperty(value = "工单状态list",name = "roStatusList")
    private List<String> roStatusList;

}
