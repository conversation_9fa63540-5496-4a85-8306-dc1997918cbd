package com.volvo.bff.volvoworks.wechat.common.model.vo.assistant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("车辆检查入参VO")
public class VehicleCheckQueryParamsVo {

    @ApiModelProperty(hidden = true)
    private String ownerCode;

    @ApiModelProperty(value = "牌照",name = "license")
    private String license;

    @ApiModelProperty(value = "车主",name = "ownerName")
    private String ownerName;

    @ApiModelProperty(value = "工单号",name = "roNo")
    private String roNo;

    @ApiModelProperty(value = "是否录入",name = "isInput")
    private String isInput;

    @ApiModelProperty(value = "服务顾问",name = "serviceAdvisor")
    private String serviceAdvisor;

        @ApiModelProperty(value = "指定技师",name = "chiefTechnician")
    private String chiefTechnician;

    @ApiModelProperty(value = "工单开单起始日期",name = "beginCreatedAt",example = "2000-11-05 19:25:14")
    private String beginCreatedAt;

    @ApiModelProperty(value = "工单开单截止日期",name = "endCreatedAt",example = "2022-11-05 19:25:14")
    private String endCreatedAt;


}
