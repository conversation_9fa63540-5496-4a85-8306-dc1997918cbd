package com.volvo.bff.volvoworks.wechat.common.service.workshop;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.LucencyWorkshop.*;
import com.volvo.bff.volvoworks.wechat.common.model.dto.LucencyWorkshop.QueryParamDto;

import java.util.List;

public interface LucencyWorkShopService {

    PageBean<DeliveryDTO> selectDeliveryList(QueryParamDto queryParamDto);

    //开单页面查询
    PageBean<BeginOrderDTO> selectBeginOrderList(QueryParamDto queryParamDto);
    //查询派工数量
    List<StatusCountDTO> selectAssignStatusCount(QueryParamDto queryParamDto);
    //维修页面查询
    PageBean<WorkShopRepairOrderDTO> selectWorkshopRepairOrder(QueryParamDto queryParamDto);
    //查询打卡数量
    List<StatusCountDTO> selectIsPunchCount(QueryParamDto queryParamDto);
    //结算页面查询
    PageBean<WorkShopBalanceOrderDTO> selectWorkShopBalanceOrder(QueryParamDto queryParamDto);
    //查询交车数量
    List<StatusCountDTO> selectDeliveryTagCount(QueryParamDto queryParamDto);
}
