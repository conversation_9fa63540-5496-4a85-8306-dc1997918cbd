package com.volvo.bff.volvoworks.wechat.common.model.vo.app;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 定损维护查询估价单结果vo
 * <AUTHOR>
 */
@Data
@ApiModel("定损维护查询估价单结果vo")
public class DamagedEstimateOrderResultVo {

    @ApiModelProperty(value = "估价时间",name = "roCreateDateBegin")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date roCreateDateBegin;
    @ApiModelProperty(value = "车主姓名",name = "ownerName")
    private String ownerName;
    @ApiModelProperty(value = "车牌号",name = "license")
    private String license;
    @ApiModelProperty(value = "车型",name = "model")
    private String model;
    @ApiModelProperty(value = "估价单号",name = "estimateNo")
    private String estimateNo;
    @ApiModelProperty(value = "服务顾问",name = "serviceAdvisor")
    private String serviceAdvisor;

}
