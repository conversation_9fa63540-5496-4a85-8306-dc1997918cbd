package com.volvo.bff.volvoworks.wechat.common.model.vo.workshop;

import com.volvo.bff.volvoworks.wechat.common.model.po.accidentClues.AssignCheckDetailPO;
import com.volvo.bff.volvoworks.wechat.common.model.po.accidentClues.RepairOrderPO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("质检页面查询带出数据查询vo")
public class AssginqualityInspectionResultVo {
    @ApiModelProperty(value = "工单",name = "unEnter",example = "10")
    private RepairOrderPO RepairOrder;

    @ApiModelProperty(value = "质检明细表tt_assign_check_detail",name = "entered",example = "10")
    private List<AssignCheckDetailPO> AssignCheckDetail;

}
