package com.volvo.bff.volvoworks.wechat.common.model.vo.print;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("结算单打印工单明细列表Vo")
@Data
public class PrintTableGroupVo {
    @ApiModelProperty(value = "表格组代码(标准版交修序号，江苏版则为GS/LJ/FJ)", name = "tableGroupCode")
    private String tableGroupCode;
    @ApiModelProperty(value = "表格组名称", name = "tableGroupName")
    private String tableGroupName;
    @ApiModelProperty(value = "表格组数据", name = "tableGroupData")
    private List<PrintTableVo> tableGroupData;
}
