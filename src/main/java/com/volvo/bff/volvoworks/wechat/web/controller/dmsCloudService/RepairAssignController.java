package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService;

import com.volvo.bff.volvoworks.wechat.common.model.dto.repairAssign.TtRoAssignDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.AssginqualityInspectionResultVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.RepairAssignWorkShopTraceDTO;
import com.volvo.bff.volvoworks.wechat.common.service.repairAssign.RepairAssignService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api("/repairAssign")
@Controller
@RequestMapping("/repairAssign")
public class RepairAssignController {

    @Autowired
    private RepairAssignService repairAssignService;
    @ApiOperation(value = "查询派工信息", notes = "查询派工信息", httpMethod = "GET")
    @GetMapping(value = "/queryRepairAssign")
    @ResponseBody
    public ResponseDTO<PageInfoDto> queryRepairAssign(@RequestParam Map<String, String> queryParam)  {
        return ResponseUtils.success(repairAssignService.queryRepairAssign(queryParam));
    }


    @ApiOperation(value = "根据工单查询维修项目", notes = "根据工单查询维修项目", httpMethod = "GET")
    @GetMapping(value = "/queryRoLabourByRoNOess/{id}")
    @ResponseBody
    public List<Map> queryRoLabourByRoNOss(@PathVariable String id) {
        return repairAssignService.queryRoLabourByRoNOss(id);
    }


    @ApiOperation(value = "根据维修项目查询派工单所有", notes = "根据维修项目查询派工单所有", httpMethod = "GET")
    @GetMapping(value = "/queryAllRoAssign/{id}")
    @ResponseBody
    public List<Map> queryAllRoAssign(@PathVariable String id) {
        return repairAssignService.queryAllRoAssign(id);
    }


    @ApiOperation(value = "分项派工保存", notes = "分项派工保存", httpMethod = "PUT")
    @PutMapping(value = "/updateAssign")
    @ResponseBody
    public void updateAssign(@RequestBody TtRoAssignDTO dto)
    {
        repairAssignService.updateAssign(dto);
    }


    @ApiOperation(value = "根据派工单查询维修项目", notes = "根据派工单查询维修项目", httpMethod = "GET")
    @GetMapping(value = "/queryLabour/{id}")
    @ResponseBody
    public PageInfoDto queryLabour(@PathVariable String id) {
        return repairAssignService.queryLabour(id);
    }


    @ApiOperation(value = "自检登记", notes = "自检登记", httpMethod = "PUT")
    @PutMapping(value = "/selfInspectionRegistration")
    @ResponseBody
    public void selfInspectionRegistration(@RequestBody TtRoAssignDTO dto) {
         repairAssignService.selfInspectionRegistration(dto);
    }


    @ApiOperation(value = "透明车间(派工上下工位数据留痕)", notes = "透明车间(派工上下工位数据留痕)", httpMethod = "POST")
    @PostMapping(value = "/workshopTrace")
    @ResponseBody
    public String workshopTrace(@RequestBody RepairAssignWorkShopTraceDTO assignWorkShopTraceDTO) {
        return repairAssignService.workshopTrace(assignWorkShopTraceDTO);
    }


    @ApiOperation(value = "质检页面查询带出数据查询", notes = "质检页面查询带出数据查询", httpMethod = "GET")
    @GetMapping(value = "/qualityInspection/{roNo}")
    @ResponseBody
    public AssginqualityInspectionResultVo qualityInspection(@PathVariable String roNo) {
        return  repairAssignService.qualityInspection(roNo);
    }
}
