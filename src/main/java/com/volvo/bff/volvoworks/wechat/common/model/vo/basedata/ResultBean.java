package com.volvo.bff.volvoworks.wechat.common.model.vo.basedata;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.http.HttpStatus;

@Data
public class ResultBean {

    /**
     * API 调用成功
     */
    public static final Integer SUCCESS_CODE = 200;
    /**
     * 验证失败
     */
    public static final Integer VALID_FAILD = 400;
    /**
     * 未知错误
     */
    public static final Integer ERROR_UNKNOWN = 900;
    public static final Integer ERROR_DB = 901;

    private Integer resultCode;

    @ApiModelProperty(value = "返回代码")
    private Integer code;

    @ApiModelProperty(value = "描述信息")
    private String msg = "";

    @ApiModelProperty(value = "错误信息")
    private String errorMessage = "";

    @ApiModelProperty(value = "返回错误信息")
    private String errMsg = "";

    @ApiModelProperty(value = "返回显示提示信息")
    private String displayMessage;

    public ResultBean() {
        super();
    }

    public ResultBean(Integer resultCode, Integer code, String msg,
                      String errorMessage, String displayMessage) {
        this.resultCode = resultCode;
        this.code = code;
        this.msg = msg;
        this.errorMessage = errorMessage;
        this.errMsg = errorMessage;
        this.displayMessage = displayMessage;
    }

    public static <T> RestResultResponse<T> fail(Integer code, String msg, String errorMessage, String displayMessage) {
        RestResultResponse<T> r = new RestResultResponse();
        r.setSuccess(false);
        r.setMsg(msg);
        r.setErrorMessage(errorMessage);
        r.setErrMsg(errorMessage);
        r.setDisplayMessage(displayMessage);
        r.setCode(code);
        return r;
    }

    public static <T> RestResultResponse<T> fail(Integer resultCode, Integer code, String msg, String errorMessage, String displayMessage) {
        RestResultResponse<T> r = new RestResultResponse();
        r.setSuccess(false);
        r.setMsg(msg);
        r.setErrorMessage(errorMessage);
        r.setDisplayMessage(displayMessage);
        r.setCode(code);
        r.setResultCode(resultCode);
        return r;
    }

    public static <T> RestResultResponse<T> fail(String errorMessage) {
        return fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "", errorMessage, errorMessage);
    }

    public static <T> RestResultResponse<T> fail(String errorMessage, String displayMessage) {
        return fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "", errorMessage, displayMessage);
    }

    public static <T> RestResultResponse<T> requestValidationError(String errorMessage) {
        RestResultResponse<T> r = new RestResultResponse<>();
        r.setSuccess(false);
        r.setMsg("");
        r.setErrorMessage(errorMessage);
        r.setErrMsg(errorMessage);
        r.setDisplayMessage("参数错误");
        r.setCode(HttpStatus.BAD_REQUEST.value());
        return r;
    }
}