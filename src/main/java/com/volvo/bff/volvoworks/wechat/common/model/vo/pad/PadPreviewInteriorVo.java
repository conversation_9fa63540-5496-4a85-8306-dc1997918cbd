package com.volvo.bff.volvoworks.wechat.common.model.vo.pad;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName PadPreviewInteriorVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/5/19 14:21
 */
@Data
@ApiModel("pad环检单内饰新增VO")
public class PadPreviewInteriorVo {

    @ApiModelProperty(value = "环检单号",name = "yiNo")
    private String yiNo;

    @ApiModelProperty(value="集合list",name = "bodyAppearanceInfoList")
    private List<BodyAppearanceVo> bodyAppearanceInfoList;

}
