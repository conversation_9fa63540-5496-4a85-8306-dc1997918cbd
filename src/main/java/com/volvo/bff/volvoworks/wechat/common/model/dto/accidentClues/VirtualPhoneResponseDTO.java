package com.volvo.bff.volvoworks.wechat.common.model.dto.accidentClues;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@ApiModel(value="VirtualPhoneResponseDTO",description="获取虚拟手机号响应")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VirtualPhoneResponseDTO implements Serializable {

  private static final long serialVersionUID = 1L;

  @ApiModelProperty(value = "虚拟手机号",name="虚拟手机号")
  private String virtualNumber;

}
