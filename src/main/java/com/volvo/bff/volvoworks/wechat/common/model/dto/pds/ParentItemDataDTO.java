package com.volvo.bff.volvoworks.wechat.common.model.dto.pds;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@ApiModel("ParentItemDataDTO")
public class ParentItemDataDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	// pds item dta id
	@ApiModelProperty(name = "pdsItemDataId")
	private Integer pdsItemDataId;
	// pds项目资料path
	@ApiModelProperty(name = "dataPath")
	private String dataPath;
	// reserve1
	@ApiModelProperty(name = "pdsItemDataReserve1")
	private String pdsItemDataReserve1;
	// reserve2
	@ApiModelProperty(name = "pdsItemDataReserve2")
	private String pdsItemDataReserve2;
	// reserve3
	@ApiModelProperty(name = "pdsItemDataReserve3")
	private String pdsItemDataReserve3;
}