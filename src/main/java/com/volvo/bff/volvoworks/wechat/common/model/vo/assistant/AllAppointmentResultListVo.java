package com.volvo.bff.volvoworks.wechat.common.model.vo.assistant;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("所有预约单结果VO")
public class AllAppointmentResultListVo {

    @ApiModelProperty(value = "牌照",name = "license",example = "苏B832NF")
    private String license;

    @ApiModelProperty(value = "车主",name = "ownerName",example = "黄辉")
    private String ownerName;

    @ApiModelProperty(value = "车型",name = "model",example = "黄辉")
    private String model;

    @ApiModelProperty(value = "预约单",name = "bookingOrderNo",example = "YO1909170008")
    private String bookingOrderNo;

    @ApiModelProperty(value = "工单",name = "roNo",example = "RO1909170008")
    private String roNo;

    @ApiModelProperty(value = "服务顾问",name = "serviceAdvisor",example = "2")
    private String serviceAdvisor;

    @ApiModelProperty(value = "车主手机号",name = "contactorPhone",example = "13455556666")
    private String contactorPhone;

    @ApiModelProperty(value = "预约进厂时间",name = "bookingComeTime",example = "2019-09-04 00:00:00")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date bookingComeTime;

}
