/*
 * Copyright (c) Volvo CAR Distribution (SHANGHAI) Co., Ltd. 2023. All rights reserved.
 */

package com.volvo.bff.volvoworks.wechat.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 功能描述： 续保客户类型
 * 续保客户类型(81761001 新保客户  81761002 新转续  81761003  续转续   81761004 在修不在保)
 *
 */
@Getter
@AllArgsConstructor
public enum InsuranceTypeEnum {
    /**
     * 新保客户
     */
    NEW_CUS("新保客户", 81761001),
    /**
     * 新转续
     */
    NEW_RENEWAL_CUS("新转续", 81761002),
    /**
     * 续转续
     */
    RENEWAL_RENEWAL_CUS("续转续", 81761003),
    /**
     * 在修不在保
     */
    REPAIR_NOT_INSURE("在修不在保", 81761004),
    /**
     * 不在修不在保
     */
    NOT_REPAIR_NOT_INSURE("不在修不在保", 81761005);

    /**
     * 初始化默认设置属性
     */
    private final String name;

    /**
     * 初始化默认设置属性
     */
    private final Integer value;

    public static InsuranceTypeEnum fromChineseName(String chineseName) {
        for (InsuranceTypeEnum number : values()) {
            if (number.getName().equals(chineseName)) {
                return number;
            }
        }
        return null; // 如果找不到匹配的中文名称，则返回null
    }
    public static InsuranceTypeEnum fromChineseName(Integer value) {
        for (InsuranceTypeEnum number : values()) {
            if (number.getValue().equals(value)) {
                return number;
            }
        }
        return null; // 如果找不到匹配的中文名称，则返回null
    }
}
