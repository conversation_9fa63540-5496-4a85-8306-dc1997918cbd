package com.volvo.bff.volvoworks.wechat.common.model.vo.pad;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("新增预检单车身外观参数Vo")
public class BodyAppearanceVo {

    @ApiModelProperty(value = "标识code",name = "contentCode")
    private String contentCode;

    @ApiModelProperty(value = "状态码",name = "statusCode")
    private String statusCode;

    @ApiModelProperty(value = "状态码名称",name = "statusCode")
    private String contentName;

    @ApiModelProperty(value = "图片地址集合",name = "fileBaseUrls")
    private List<String> fileBaseUrls;

    @ApiModelProperty(value = "录音地址",name = "recUrl")
    private String recUrl;

    @ApiModelProperty(value = "视频地址",name = "videoUrl")
    private String videoUrl;

    @ApiModelProperty(value = "数据值",name = "remark3")
    private String remark3;


}
