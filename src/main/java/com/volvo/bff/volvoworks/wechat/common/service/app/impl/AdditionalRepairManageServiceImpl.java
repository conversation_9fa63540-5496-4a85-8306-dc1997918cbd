package com.volvo.bff.volvoworks.wechat.common.service.app.impl;


import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.app.AdditionalRepairSuggestionService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class AdditionalRepairManageServiceImpl implements AdditionalRepairSuggestionService {

    @Resource
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Override
    public PageBean<QueryRepairingOrderResultVo> queryRepairOrder(QueryStructureCheckRrquestVo queryParams) {
        log.info("queryRepairOrder start queryParams:{}", queryParams);
        DmsResultDTO<PageBean<QueryRepairingOrderResultVo>> res = dmscloudServiceFeign.queryRepairOrder(queryParams);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public List<AdditionalRepairSuggestionResultVo> queryAdditionalRepairSuggestion(String roNo) {
        DmsResultDTO<List<AdditionalRepairSuggestionResultVo>> res = dmscloudServiceFeign.queryAdditionalRepairSuggestion(roNo);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public AdditionalRepairSuggestionResultVo queryAdditionalRepairSuggestionDetail(String itemId) {
        DmsResultDTO<AdditionalRepairSuggestionResultVo> res = dmscloudServiceFeign.queryAdditionalRepairSuggestionDetail(itemId);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public String saveAdditionalRepairSuggestion(SaveAdditionalRepairSuggestionVo saveParams) {
        DmsResultDTO<String> res = dmscloudServiceFeign.saveAdditionalRepairSuggestion(saveParams);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }


    @Override
    public PageBean<VidaOrderListResultVo> getVidaListByVin(String vin) {
        DmsResultDTO<PageBean<VidaOrderListResultVo>> res = dmscloudServiceFeign.getVidaListByVin(vin);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public Void deleteAdditionalRepairSuggestion(String itemId) {
        DmsResultDTO<Void> res = dmscloudServiceFeign.deleteAdditionalRepairSuggestion(itemId);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }
}
