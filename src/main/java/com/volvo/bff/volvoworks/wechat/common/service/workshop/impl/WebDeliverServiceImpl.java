package com.volvo.bff.volvoworks.wechat.common.service.workshop.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.ApplicationMaintainManagementFeign;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.workshop.WebDeliverService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class WebDeliverServiceImpl implements WebDeliverService {

    @Autowired
    private ApplicationMaintainManagementFeign applicationMaintainManagementFeign;


    /**
     * 记录异常送修人工单
     * @param roNo 工单号
     * @param traceTime 回访时间类型
     * @return map 返回结果
     */
    @Override
    public Map<String, Object> deliveryOrderExceptionRecord(String roNo, String traceTime) {
        DmsResultDTO<Map<String, Object>> result = applicationMaintainManagementFeign.deliveryOrderExceptionRecord(roNo, traceTime);
        ErrorConversionUtils.errorCodeConversion(result, result.getResultCode());
        return result.getData();
    }
}
