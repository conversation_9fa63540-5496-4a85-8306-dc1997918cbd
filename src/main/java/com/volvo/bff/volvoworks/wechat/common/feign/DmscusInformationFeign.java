package com.volvo.bff.volvoworks.wechat.common.feign;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "dmscus-information",
        url = "${mse-in.tob.dmscus-information.url}",
        path = "${mse-in.tob.dmscus-information.path}")
//        url = "http://localhost:8003",
//        path = "/")
public interface DmscusInformationFeign {
	

}
