package com.volvo.bff.volvoworks.wechat.common.exception;

public enum ExceptionEnum {
	// 异常类枚举
	QUERY_TOKEN_ERROR("888", "", "获取token错误", "获取token错误"),
	GROUP_NOT_CREATED("-2","","群聊未创建","群聊未创建"),
	GROUP_DISSOLUTION("-1","","群聊已解散","群聊已解散"),
	
	
    SYSTEM_ERROR("999", "系统异常", "系统拥堵，请稍后再试!", "系统拥堵，请稍后再试!");

	// 下游异常信息code
    private String code;
	
	// 下游异常信息描述
    private String msg;
	
	// 异常错误信息
    private String errorMessage;
    
    // 提示信息
    private String displayMessage;

    ExceptionEnum(String code, String msg, String errorMessage, String displayMessage) {
    	this.code = code;
    	this.msg = msg;
        this.errorMessage = errorMessage;
        this.displayMessage = displayMessage;
    }

	public String getCode() {
		return code;
	}

	public String getMsg() {
		return msg;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public String getDisplayMessage() {
		return displayMessage;
	}
}
