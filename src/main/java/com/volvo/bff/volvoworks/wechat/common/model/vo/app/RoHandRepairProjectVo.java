package com.volvo.bff.volvoworks.wechat.common.model.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("工单交修项目Vo")
public class RoHandRepairProjectVo {

    @ApiModelProperty(value = "jobNo",name = "jobNo")
    private String jobNo;

    @ApiModelProperty(value = "交修项目代码",name = "projectCode")
    private String projectCode;

    @ApiModelProperty(value = "交修项目名称",name = "projectName")
    private String projectName;

    public String getJobNo() {
        return jobNo;
    }

    public void setJobNo(String jobNo) {
        this.jobNo = jobNo;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

}
