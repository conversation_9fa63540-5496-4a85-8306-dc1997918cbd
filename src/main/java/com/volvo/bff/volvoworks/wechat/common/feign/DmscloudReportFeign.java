package com.volvo.bff.volvoworks.wechat.common.feign;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.workshop.VehicleEntranceCountDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.VehicleEntranceParamsVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@FeignClient(name = "dmscloud-report",
        url = "${mse-in.tob.dmscloud-report.url}", 
        path = "${mse-in.tob.dmscloud-report.path}")
public interface DmscloudReportFeign {

    /**
     * 3.12服务看板到店（企微店端）
     * @param vehicleEntranceVO 查询参数
     * @return 分页结果接
     */
    @PostMapping(value = "/replenishment/weCom/queryVehicleEntranceList", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<PageBean<VehicleEntranceParamsVO>> queryVehicleEntranceList(@RequestBody VehicleEntranceParamsVO vehicleEntranceVO);

    /**
     * 获取 未分拨，已分拨 全部数量
     * @return 数量对象
     */
    @PostMapping(value = "/replenishment/weCom/queryAllocatedCount", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResultDTO<VehicleEntranceCountDto> queryAllocatedCount(@RequestBody VehicleEntranceParamsVO vehicleEntranceVO);
}
