package com.volvo.bff.volvoworks.wechat.common.model.dto.repairAssign;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
public class TtRoAssignDTO implements Serializable {


    private String rework;
    private String remark;
    private  String IN_MILEAGE;

    private String type;

    private String technicianNext;
    private String technicianNextName;
    private String rowKey;

    private String id;

    private String roNo;

    private String technician;

    private String workHour;

    private String checker;

    private String testDriver;

    private String jun;

    private String assignLabourHour;

    private String dealerCode;

    private String outMileage;

    private String positionCode;

    private String itemId;

    private String assignId;

    private String assignLabourhour;

    private String labourPositionCode;

    private String workerTypeCode;

    private String labourFactor;

    private String itemStartTime;

    private String estimateEndTime;

    private String estimateStartTime;

    private String deleteOne;

    private String oneAssignId;

    private String assignTag;

    private String level;

    private String levelCode;

    //终检结束时间
    private String endPreckeckEndTime;

    //终检记录
    private String endPreckeckRecord;

    public String getEndPreckeckRecord() {
        return endPreckeckRecord;
    }

    public void setEndPreckeckRecord(String endPreckeckRecord) {
        this.endPreckeckRecord = endPreckeckRecord;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getLevelCode() {
        return levelCode;
    }

    public void setLevelCode(String levelCode) {
        this.levelCode = levelCode;
    }

    public void setEndPreckeckEndTime(String endPreckeckEndTime) {
        this.endPreckeckEndTime = endPreckeckEndTime;
    }

    public String getEndPreckeckEndTime() {
        return endPreckeckEndTime;
    }

    public String getEstimateStartTime() {
        return estimateStartTime;
    }

    public String getRework() {
        return rework;
    }

    public String getTechnicianNextName() {
        return technicianNextName;
    }

    public void setTechnicianNextName(String technicianNextName) {
        this.technicianNextName = technicianNextName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public void setRework(String rework) {
        this.rework = rework;
    }

    public String getIN_MILEAGE() {
        return IN_MILEAGE;
    }

    public void setIN_MILEAGE(String IN_MILEAGE) {
        this.IN_MILEAGE = IN_MILEAGE;
    }

    public void setEstimateStartTime(String estimateStartTime) {
        this.estimateStartTime = estimateStartTime;
    }

    public String getType() {
        return type;
    }

    public String getTechnicianNext() {
        return technicianNext;
    }

    public void setTechnicianNext(String technicianNext) {
        this.technicianNext = technicianNext;
    }

    public void setType(String type) {
        this.type = type;
    }
    private Map<String,String> checkItems;

    public Map<String, String> getCheckItems() {
        return checkItems;
    }

    public void setCheckItems(Map<String, String> checkItems) {
        this.checkItems = checkItems;
    }

    public String getRowKey() {
        return rowKey;
    }

    public void setRowKey(String rowKey) {
        this.rowKey = rowKey;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAssignId() {
        return assignId;
    }

    public void setAssignId(String assignId) {
        this.assignId = assignId;
    }

    public String getAssignLabourhour() {
        return assignLabourhour;
    }

    public void setAssignLabourhour(String assignLabourhour) {
        this.assignLabourhour = assignLabourhour;
    }

    public String getLabourPositionCode() {
        return labourPositionCode;
    }

    public void setLabourPositionCode(String labourPositionCode) {
        this.labourPositionCode = labourPositionCode;
    }

    public String getWorkerTypeCode() {
        return workerTypeCode;
    }

    public void setWorkerTypeCode(String workerTypeCode) {
        this.workerTypeCode = workerTypeCode;
    }

    public String getLabourFactor() {
        return labourFactor;
    }

    public void setLabourFactor(String labourFactor) {
        this.labourFactor = labourFactor;
    }

    public String getItemStartTime() {
        return itemStartTime;
    }

    public void setItemStartTime(String itemStartTime) {
        this.itemStartTime = itemStartTime;
    }

    public String getEstimateEndTime() {
        return estimateEndTime;
    }

    public void setEstimateEndTime(String estimateEndTime) {
        this.estimateEndTime = estimateEndTime;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public String getOutMileage() {
        return outMileage;
    }

    public void setOutMileage(String outMileage) {
        this.outMileage = outMileage;
    }

    public String getOwnerCode() {
        return dealerCode;
    }

    public void setDealerCode(String dealerCode) {
        this.dealerCode = dealerCode;
    }

    public String getAssignLabourHour() {
        return assignLabourHour;
    }

    public void setAssignLabourHour(String assignLabourHour) {
        this.assignLabourHour = assignLabourHour;
    }

    public String getJun() {
        return jun;
    }

    public void setJun(String jun) {
        this.jun = jun;
    }

    public List<Map> partList;

    public String getTechnician() {
        return technician;
    }

    public void setTechnician(String technician) {
        this.technician = technician;
    }

    public String getWorkHour() {
        return workHour;
    }

    public void setWorkHour(String workHour) {
        this.workHour = workHour;
    }

    public String getChecker() {
        return checker;
    }

    public void setChecker(String checker) {
        this.checker = checker;
    }

    public String getTestDriver() {
        return testDriver;
    }

    public void setTestDriver(String testDriver) {
        this.testDriver = testDriver;
    }

    public List<Map> getPartList() {
        return partList;
    }

    public void setPartList(List<Map> partList) {
        this.partList = partList;
    }

    public List<Map> tablelist;

    public List<Map> tabList;  //2.0.1修改新加的 编辑时数据

    public List<Map> table;

    public List<Map> getTable() {
        return table;
    }

    public void setTable(List<Map> table) {
        this.table = table;
    }

    public List<Map> getTablelist() {
        return tablelist;
    }

    public void setTablelist(List<Map> tablelist) {
        this.tablelist = tablelist;
    }

    public String getRoNo() {
        return roNo;
    }

    public void setRoNo(String roNo) {
        this.roNo = roNo;
    }

    public List<Map> getTables() {
        return tables;
    }

    public void setTables(List<Map> tables) {
        this.tables = tables;
    }

    public List<Map> getTablesDeatil() {
        return tablesDeatil;
    }

    public void setTablesDeatil(List<Map> tablesDeatil) {
        this.tablesDeatil = tablesDeatil;
    }

    private List<Map> tables;

    private List<Map> tablesDeatil;

    public String getDeleteOne() {
        return deleteOne;
    }

    public void setDeleteOne(String deleteOne) {
        this.deleteOne = deleteOne;
    }

    public String getOneAssignId() {
        return oneAssignId;
    }

    public void setOneAssignId(String oneAssignId) {
        this.oneAssignId = oneAssignId;
    }

    public String getAssignTag() {
        return assignTag;
    }

    public void setAssignTag(String assignTag) {
        this.assignTag = assignTag;
    }

    public List<Map> getTabList() {
        return tabList;
    }

    public void setTabList(List<Map> tabList) {
        this.tabList = tabList;
    }
}
