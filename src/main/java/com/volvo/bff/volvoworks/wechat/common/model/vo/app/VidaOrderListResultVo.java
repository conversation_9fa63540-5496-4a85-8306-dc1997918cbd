package com.volvo.bff.volvoworks.wechat.common.model.vo.app;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

@ApiModel("vida工单列表vo")
public class VidaOrderListResultVo {

    @ApiModelProperty(value = "VIDA工单号",name = "orderNo")
    private String orderNo;

    @ApiModelProperty(value = "VIDA主键",name = "id")
    private Long id;

    @ApiModelProperty(value = "VIDA_ID",name = "vidaId")
    private Long vidaId;

    @ApiModelProperty(value = "姓名",name = "name")
    private String name;

    @ApiModelProperty(value = "车牌号",name = "licensePlateNum")
    private String licensePlateNum;

    @ApiModelProperty(value = "车型",name = "model")
    private String model;

    @ApiModelProperty(value = "开单人员",name = "drawer")
    private String drawer;

    @ApiModelProperty(value = "创建时间",name = "orderTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date orderTime;

    private List<VidaOrderDetailsVo> VidaOrderDetails;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getVidaId() {
        return vidaId;
    }

    public void setVidaId(Long vidaId) {
        this.vidaId = vidaId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLicensePlateNum() {
        return licensePlateNum;
    }

    public void setLicensePlateNum(String licensePlateNum) {
        this.licensePlateNum = licensePlateNum;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getDrawer() {
        return drawer;
    }

    public void setDrawer(String drawer) {
        this.drawer = drawer;
    }

    public Date getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public List<VidaOrderDetailsVo> getVidaOrderDetails() {
        return VidaOrderDetails;
    }

    public void setVidaOrderDetails(List<VidaOrderDetailsVo> vidaOrderDetails) {
        VidaOrderDetails = vidaOrderDetails;
    }
}
