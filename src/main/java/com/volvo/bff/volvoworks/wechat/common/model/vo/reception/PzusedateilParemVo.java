package com.volvo.bff.volvoworks.wechat.common.model.vo.reception;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 维修派工新需求Vo
 * <AUTHOR>
 */
@Data
@ApiModel("维修派工新需求Vo")
public class PzusedateilParemVo {

    @ApiModelProperty(value="暂停ID ",name="pauseId")
    private String pauseId;

    @ApiModelProperty(value="总时间 ",name="times")
    private String times;

    @ApiModelProperty(value="工序 ",name="processName")
    private String processName;

    @ApiModelProperty(value="技师 ",name="technician")
    private String technician;

    @ApiModelProperty(value="派工表id,",name="assignId")
    private String assignId;

    @ApiModelProperty(value="工单号,",name="roNo")
    private String roNo;

    @ApiModelProperty(value="暂停开始时间",name="beginPause")
    private String beginPause;

    @ApiModelProperty(value="暂停结束时间",name="endPause")
    private String endPause;

    @ApiModelProperty(value="暂停总时长",name="pauseTotalTime")
    private String pauseTotalTime;

    @ApiModelProperty(value="是否是事故",name="pauseTotalTime")
    private String isSen;

    @ApiModelProperty(value="预计交车时间",name="endTimeSupposed")
    private String endTimeSupposed;

    @ApiModelProperty(value="洗车完成",name="isOk")
    private String isOk;

    @ApiModelProperty(value="有效营业时长",name="validBusinessHours")
    private String validBusinessHours;
}
