package com.volvo.bff.volvoworks.wechat.web.controller.bffAftersales;

import com.volvo.bff.volvoworks.wechat.common.exception.ServiceException;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.workshop.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.BookingOrderVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.ReminderShortPartItemVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.VehicleEntranceParamsVO;
import com.volvo.bff.volvoworks.wechat.common.service.workshop.TransparentWorkshopManageService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api("/workshop")
@RestController
@RequestMapping("/workshop")
@Slf4j
public class TransparentWorkshopManageController {

    @Autowired
    private TransparentWorkshopManageService transparentWorkshopManageService;


    @ApiOperation(value = "查询预约 数字", notes = "查询预约 数字", httpMethod = "POST")
    @PostMapping("/weCome/queryBookingStatusCount")
    public ResponseDTO<BookingStatusDto> queryBookingStatus(@RequestBody BookingOrderVo bookingOrderVo) {
        return ResponseUtils.success(transparentWorkshopManageService.queryBookingStatus(bookingOrderVo));
    }


    @ApiOperation(value = "服务看板预约查询（企微店端）", notes = "服务看板预约查询（企微店端）", httpMethod = "POST")
    @PostMapping(value = "/weCom/queryBookingOrder")
    @ResponseBody
    public ResponseDTO<PageBean<BookingOrderDto>> queryBookingWeCom(@RequestBody BookingOrderVo bookingOrderVo) {
        if(StringUtils.isEmpty(bookingOrderVo.getOwnerCode())){
            log.info("queryBookingWeCom ownerCode is null");
            return ResponseUtils.success(new PageBean<>());
        }
        return ResponseUtils.success(transparentWorkshopManageService.queryBookingWeCom(bookingOrderVo));
    }

    /**
     * 3.12服务看板到店（企微店端）
     * @param vehicleEntranceVO 查询参数
     * @return 分页结果接
     */

    @ApiOperation(value = "3.12服务看板到店（企微店端）", notes = "3.12服务看板到店（企微店端）", httpMethod = "POST")
    @PostMapping("/weCom/queryVehicleEntranceList")
    public ResponseDTO<PageBean<VehicleEntranceParamsVO>> queryVehicleEntranceList(@RequestBody VehicleEntranceParamsVO vehicleEntranceVO) {
        if(StringUtils.isEmpty(vehicleEntranceVO.getDealerCode())){
            log.info("queryVehicleEntranceList dealerCode is null");
            return ResponseUtils.success(new PageBean<>());
        }
        return ResponseUtils.success(transparentWorkshopManageService.queryVehicleEntranceList(vehicleEntranceVO));
    }

    /**
     * 获取 未分拨，已分拨 全部数量
     * @return 数量对象
     */

    @ApiOperation(value = "获取 未分拨，已分拨 全部数量", notes = "获取 未分拨，已分拨 全部数量", httpMethod = "POST")
    @PostMapping("/weCom/queryAllocatedCount")
    public ResponseDTO<VehicleEntranceCountDto> queryAllocatedCount(@RequestBody VehicleEntranceParamsVO vehicleEntranceVO) {
        return ResponseUtils.success(transparentWorkshopManageService.queryAllocatedCount(vehicleEntranceVO));
    }

    /**
     * 服务看板缺件查询（企微店端）
     */

    @ApiOperation(value = "服务看板缺件查询（企微店端）", notes = "服务看板缺件查询（企微店端）", httpMethod = "POST")
    @PostMapping(value = "/weCom/queryShortPart")
    @ResponseBody
    public ResponseDTO<PageBean<ShortPartItemWeComDto>> queryShortPartWeCom(@RequestBody ReminderShortPartItemVo shortPartItemVo) {
        if(StringUtils.isEmpty(shortPartItemVo.getOwnerCode())){
            log.info("queryShortPartWeCom ownerCode is null");
            return ResponseUtils.success(new PageBean<>());
        }
        return ResponseUtils.success(transparentWorkshopManageService.queryShortPartWeCom(shortPartItemVo));
    }

    /**
     * 查询未到货，已到货，部分到货数量
     *
     * @return 返回对象（包含数量）
     */

    @ApiOperation(value = "查询未到货，已到货，部分到货数量", notes = "查询未到货，已到货，部分到货数量", httpMethod = "POST")
    @PostMapping(value = "/weCom/getShortPartStatus")
    @ResponseBody
    public ResponseDTO<MissingPartsStatusDto> getShortPartStatus(@RequestBody ReminderShortPartItemVo shortPartItemVo) {
        return ResponseUtils.success(transparentWorkshopManageService.getShortPartStatus(shortPartItemVo));
    }

    /**
     * 根据缺料主键查询 明细
     *
     * @param shortPartItemVo 明细数据
     * @return 明细数据
     */

    @ApiOperation(value = "根据缺料主键查询 明细", notes = "根据缺料主键查询 明细", httpMethod = "POST")
    @PostMapping("/weCom/queryShortPartItem")
    public ResponseDTO<ReminderShortPartItemDto> queryShortPartItem(@RequestBody ReminderShortPartItemVo shortPartItemVo) {
        return ResponseUtils.success(transparentWorkshopManageService.queryShortPartItem(shortPartItemVo));
    }

    /**
     * 记录通话记录
     * @param workshopCallRecordDto 通话记录详情
     * @return true false
     */

    @ApiOperation(value = "记录通话记录", notes = "记录通话记录", httpMethod = "POST")
    @PostMapping("/weCom/addCallLog")
    public ResponseDTO<Boolean> addCallLog(@RequestBody WorkshopCallRecordDto workshopCallRecordDto) {
        if (StringUtils.isEmpty(workshopCallRecordDto.getOwnerCode())
                || StringUtils.isEmpty(workshopCallRecordDto.getRoNo())
                || StringUtils.isEmpty(workshopCallRecordDto.getServiceAdvisor())) {
            throw new ServiceException("销商编码,工单号，服务顾问为必要参数，请检查参数!");
        }
        return ResponseUtils.success(transparentWorkshopManageService.addCallLog(workshopCallRecordDto));
    }

    /**
     * 查询通话记录
     * @param ownerCode 经销商
     * @param roNo 工单号
     * @param serviceAdvisor 服务顾问
     * @return 通话记录
     */

    @ApiOperation(value = "查询通话记录", notes = "查询通话记录", httpMethod = "GET")
    @GetMapping("/weCom/callItem")
    public ResponseDTO<PageBean<WorkshopCallRecordDto>> queryCallItem(@RequestParam String ownerCode,
                                                         @RequestParam String roNo,
                                                         @RequestParam String serviceAdvisor,
                                                         @RequestParam Integer pageNum,
                                                         @RequestParam Integer pageSize) {
        if (StringUtils.isEmpty(ownerCode)
                || StringUtils.isEmpty(roNo)
                || StringUtils.isEmpty(serviceAdvisor)) {
            throw new ServiceException("销商编码,工单号，服务顾问为必要参数，请检查参数!");
        }
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        return ResponseUtils.success(transparentWorkshopManageService.queryCallItem(ownerCode, roNo, serviceAdvisor, pageNum, pageSize));
    }

    /**
     * 修改预计交车时间
     * @param roNo 工单号
     * @param endTimeSupposed 预交车时间
     */

    @ApiOperation(value = "修改预计交车时间", notes = "修改预计交车时间", httpMethod = "GET")
    @GetMapping("/roStatus/roNo")
    public void updateRepairOrderStatus(@RequestParam("roNo") String roNo, @RequestParam("endTimeSupposed") String endTimeSupposed) {
        if (StringUtils.isEmpty(roNo)) {
            log.error("roNo is null");
            return;
        }
        transparentWorkshopManageService.updateRepairOrderStatus(roNo, endTimeSupposed);
    }
}
