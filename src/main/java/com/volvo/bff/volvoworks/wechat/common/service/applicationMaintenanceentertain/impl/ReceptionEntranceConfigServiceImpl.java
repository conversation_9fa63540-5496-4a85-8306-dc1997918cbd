package com.volvo.bff.volvoworks.wechat.common.service.applicationMaintenanceentertain.impl;

import com.alibaba.fastjson.JSONObject;
import com.volvo.bff.volvoworks.wechat.common.feign.ApplicationMaintenanceentertainFeign;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.applicationMaintenanceentertain.ReceptionEntrancePageConfigReqDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.applicationMaintenanceentertain.VehicleEntranceVO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.applicationMaintenanceentertain.IReceptionEntranceConfigService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ReceptionEntranceConfigServiceImpl implements IReceptionEntranceConfigService {
    @Resource
    private ApplicationMaintenanceentertainFeign applicationMaintenanceentertainFeign;
    @Override
    public List<JSONObject> queryReceptionEntranceConfigByType(ReceptionEntrancePageConfigReqDTO pageConfigReq) {
        DmsResultDTO<List<JSONObject>> res = applicationMaintenanceentertainFeign.queryReceptionEntranceConfigByType(pageConfigReq);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

}
