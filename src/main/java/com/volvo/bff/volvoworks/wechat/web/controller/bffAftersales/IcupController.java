package com.volvo.bff.volvoworks.wechat.web.controller.bffAftersales;

import com.volvo.bff.volvoworks.wechat.common.model.dto.icup.IcupMileageDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.Icup.IcupService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/icup/v1")
@Api(value = "icup车型", tags = {"icup车型"})
public class IcupController {

    @Autowired
    private IcupService icupService;

    @ApiOperation("查询icup车型里程")
    @GetMapping("/getIcupMileageByVin")
    public ResponseDTO<IcupMileageDto> getIcupMileageByVin(@RequestParam("vin") String vin) {
        return ResponseUtils.success(icupService.getIcupMileageByVin(vin));
    }
}
