package com.volvo.bff.volvoworks.wechat.web.feign;

import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.volvo.bff.volvoworks.wechat.common.config.FeignHttpsConfig;

@Component
@FeignClient(name = "customerbusiness-service",
        contextId = "test",
        url = "${mse-in.tob.customerbusiness-service.url}",
        path = "${mse-in.tob.customerbusiness-service.path:/}",
        configuration = {FeignHttpsConfig.class})
public interface CustomerBusinessTestFeign {


    @GetMapping(value = "/mock/test", produces = "application/json")
    Map<String, String> test(@RequestParam("ceshi") String test);
    
}
