package com.volvo.bff.volvoworks.wechat.common.model.vo.assistant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("查询预约单三种状态数量参数VO")
public class AppointmentNumQueryParamsVo {

    @ApiModelProperty(hidden = true)
    private String ownerCode;

    @ApiModelProperty(value = "牌照",name = "license",example = "苏B832NF")
    private String license;

    @ApiModelProperty(value = "服务顾问",name = "serviceAdvisor",example = "123")
    private String serviceAdvisor;

    @ApiModelProperty(value = "起始开单日期",name = "beginCreatedAt",example = "2019-11-05 19:25:14")
    private String beginCreatedAt;

    @ApiModelProperty(value = "截止开单日期",name = "endCreatedAt",example = "2019-11-05 19:25:14")
    private String endCreatedAt;

}
