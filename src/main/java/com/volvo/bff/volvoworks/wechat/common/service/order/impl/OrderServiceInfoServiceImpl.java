package com.volvo.bff.volvoworks.wechat.common.service.order.impl;

import com.volvo.bff.volvoworks.wechat.common.exception.ServiceException;
import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.feign.DomainOrdersFeign;
import com.volvo.bff.volvoworks.wechat.common.model.dto.order.OrderServiceInfoDeleteDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.order.OrderServiceInfoQueryDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.order.OrderServiceInfoRequestDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.orderService.OrderServiceInfoVO;
import com.volvo.bff.volvoworks.wechat.common.service.order.OrderServiceInfoService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OrderServiceInfoServiceImpl implements OrderServiceInfoService {

    @Autowired
    private DomainOrdersFeign domainOrdersFeign;

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Override
    public void saveServiceInfomation(OrderServiceInfoRequestDTO orderServiceInfoRequestDTO) {
        // 查询白名单
        DmsResultDTO<Boolean> booleanDmsResultDTO = dmscloudServiceFeign.checkWhitelist(orderServiceInfoRequestDTO.getOwnerCode(), 91111053, 0, "");
        Boolean white = booleanDmsResultDTO.getData();
        if(!white){
            throw new ServiceException("非白名单,不允许保存服务过信息","非白名单,不允许保存服务过信息");
        }
        DmsResultDTO<Void> voidDmsResultDTO = domainOrdersFeign.saveServiceInfomation(orderServiceInfoRequestDTO);
        ErrorConversionUtils.errorCodeConversion(voidDmsResultDTO);
    }

    @Override
    public void deleteServiceInfomation(OrderServiceInfoDeleteDTO dto) {
        DmsResultDTO<Void> voidResultDTO = domainOrdersFeign.deleteServiceInfomation(dto);
        ErrorConversionUtils.errorCodeConversion(voidResultDTO);
    }

    @Override
    public List<OrderServiceInfoVO> getServiceInfomation(OrderServiceInfoQueryDTO dto) {
        DmsResultDTO<List<OrderServiceInfoVO>> serviceInfomation = domainOrdersFeign.getServiceInfomation(dto);
        ErrorConversionUtils.errorCodeConversion(serviceInfomation);
        return serviceInfomation.getData();
    }
}
