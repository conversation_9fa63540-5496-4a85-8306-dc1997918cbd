package com.volvo.bff.volvoworks.wechat.common.utils;

import com.volvo.bff.volvoworks.wechat.common.exception.DmsException;
import com.volvo.bff.volvoworks.wechat.common.exception.ExceptionEnum;
import com.volvo.bff.volvoworks.wechat.common.exception.ServiceException;
import com.volvo.bff.volvoworks.wechat.common.exception.UtilException;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

/**
 * 下游异常统一判断
 *
 * <AUTHOR>
 * @date 2017年1月13日
 */
@Slf4j
public class ErrorConversionUtils {

	private ErrorConversionUtils() {
		super();
	}

	/**
	 * 错误码转换
	 */
	public static void errorCodeConversion(DmsResultDTO<?> result) {
		if(result==null) {
			throw new UtilException(ExceptionEnum.QUERY_TOKEN_ERROR);
		}
		String msg = StringUtils.isNotBlank(result.getErrMsg())?result.getErrMsg():result.getReturnMessage();
		if(!result.getSuccess()) {
			throw new ServiceException(msg, msg);
		}
	}

	/**
	 * 错误码转换
	 */
	public static void errorCodeConversion(DmsResultDTO<?> result, String code) {
		if (result == null) {
			throw new UtilException(ExceptionEnum.QUERY_TOKEN_ERROR);
		}
		String msg = StringUtils.isNotBlank(result.getErrMsg()) ? result.getErrMsg() : result.getReturnMessage();
		if (!result.getSuccess()) {
			Integer codeDef = 500;
			try {
				codeDef = Integer.valueOf(code);
			} catch (Exception e) {
				log.info("errorCodeConversion e:{}", e);
			}
			throw new DmsException(codeDef, msg, msg, msg);
		}
	}
}
