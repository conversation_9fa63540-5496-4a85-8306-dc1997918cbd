package com.volvo.bff.volvoworks.wechat.web.controller.bffAftersales;


import com.volvo.bff.volvoworks.wechat.common.model.dto.workshop.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.workshop.WechatEnterpriseService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "企微群聊app(带token校验)")
@RestController
@RequestMapping("/api/v1/app/wechat-enterprise")
@Validated
public class AppWechatEnterpriseController {
	
	@Autowired
	private WechatEnterpriseService wechatEnterpriseService;

    @ApiOperation(value = "拉人进群", notes = "", httpMethod = "POST")
    @PostMapping(value = "/group/inviteUserAddGroup")
    public ResponseDTO<Boolean> inviteUserAddGroup(@RequestBody @Validated InviteUserDTO inviteUser) {
        return ResponseUtils.success(wechatEnterpriseService.inviteUserAddGroup(inviteUser));
    }
    
    

    @ApiOperation(value = "是否在群", notes = "是否在群", httpMethod = "POST")
    @PostMapping(value = "/group/checkGroup")
    public ResponseDTO<Integer> checkGroup(@RequestBody @Validated CheckGroupDTO checkGroupDTO) {
        return ResponseUtils.success(wechatEnterpriseService.checkGroup(checkGroupDTO));
    }
    
}
