package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.basedata;

import com.volvo.bff.volvoworks.wechat.common.model.dto.stockmanage.InventoryItemDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.stockmanage.PartInventoryDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.stockmanage.PartInventorySourceCodeDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.inventoryCheck.PartInventoryCheckService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Api(value = "/basedata/inventoryCheck", tags = {""})
@RestController
@RequestMapping("/basedata/inventoryCheck")
public class PartInventoryCheckController {

    @Autowired
    private PartInventoryCheckService inventoryCheckService;

    @ApiOperation(value = "查询盘点单信息", notes = "查询盘点单信息", httpMethod = "GET")
    @RequestMapping(method = RequestMethod.GET)
    @ResponseBody
    public ResponseDTO<PageInfoDto> findAllInventoryInfo(@RequestParam Map<String, String> map){
        return ResponseUtils.success(inventoryCheckService.findAllInventoryInfo(map));
    }


    @ApiOperation(value = "根据盘点单号查询盘点单明细", notes = "根据盘点单号查询盘点单明细", httpMethod = "GET")
    @RequestMapping(value="/findItemById/{id}/{num}",method = RequestMethod.GET)
    @ResponseBody
    public ResponseDTO<Map<String, Object>> findAllInventoryItemInfoById(@PathVariable String id, @PathVariable String num,@RequestParam Map<String, String> queryParam){
        return ResponseUtils.success(inventoryCheckService.findAllInventoryItemInfoById(id,num,queryParam));
    }


    @ApiOperation(value = "盘点确认按钮", notes = "盘点确认按钮", httpMethod = "POST")
    @PostMapping(value = "/btnConfirm")
    public ResponseDTO<PartInventoryDTO> btnConfirm(@RequestBody InventoryItemDTO dto){
        return ResponseUtils.success(inventoryCheckService.btnConfirm(dto));
    }


    @ApiOperation(value = "保存按钮", notes = "保存按钮", httpMethod = "POST")
    @PostMapping(value = "/saveInventoryInfo")
    public ResponseDTO<String> saveInventoryInfo(@RequestBody InventoryItemDTO dto){
        return  ResponseUtils.success(inventoryCheckService.saveInventoryInfo(dto));
    }


    @ApiOperation(value = "盘点扫码登记溯源零件溯源码", notes = "盘点扫码登记溯源零件溯源码", httpMethod = "POST")
    @PostMapping(value = "/savePartInventorySourceCode")
    public ResponseDTO<Map<String, Object>> savePartInventorySourceCode(@RequestBody PartInventorySourceCodeDTO dto) {
        return ResponseUtils.success(inventoryCheckService.savePartInventorySourceCode(dto));
    }
}
