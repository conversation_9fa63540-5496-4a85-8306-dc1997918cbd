package com.volvo.bff.volvoworks.wechat.common.service.workshop.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.RepairProgressParamsVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.RepairProgressResultVo;
import com.volvo.bff.volvoworks.wechat.common.service.workshop.RepairProgressService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class RepairProgressServiceImpl  implements RepairProgressService {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Override
    public PageBean<RepairProgressResultVo> getRepairProgress(RepairProgressParamsVo paramsVo){
        DmsResultDTO<PageBean<RepairProgressResultVo>> response = dmscloudServiceFeign.getRepairProgress(paramsVo);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }
}
