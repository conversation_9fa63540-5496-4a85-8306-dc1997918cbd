package com.volvo.bff.volvoworks.wechat.common.service.common.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.dto.common.CommonConfigDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.common.CommonService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CommonServiceImpl  implements CommonService {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Override
    public CommonConfigDto getConfigByKey(String configKey, String groupType) {
        DmsResultDTO<CommonConfigDto> res = dmscloudServiceFeign.getConfigByKey(configKey, groupType);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }
}
