package com.volvo.bff.volvoworks.wechat.common.service.order.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.order.OrderConfirmCportService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class OrderConfirmCportServiceImpl implements OrderConfirmCportService {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Override
    public boolean selectWhitelistByOwnerCode(String ownerCode) {
        DmsResultDTO<Boolean> response = dmscloudServiceFeign.selectWhitelistByOwnerCode(ownerCode);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

}
