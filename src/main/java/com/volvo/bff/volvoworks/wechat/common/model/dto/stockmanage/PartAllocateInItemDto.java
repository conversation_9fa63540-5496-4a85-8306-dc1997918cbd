package com.volvo.bff.volvoworks.wechat.common.model.dto.stockmanage;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DataImportDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 *PartAllocateInItemDto
 * <AUTHOR>
 * @date 2016年8月10日
 */
@Data
public class PartAllocateInItemDto extends DataImportDto {
    private String itemId;//ITEM_ID id
    private String dealerCode;
    private String storageCode;//STORAGE_CODE 仓库
    private String storagePositionCode;//库位
    private String partNo; //配件代码
    private String partName;//配件名称
    private String unitCode;//单位代码
    private Double inPrice;//入库不含税单价
    private Double inAmount;//入库不含税金额 IN_AMOUNT
    private String otherPartCostPrice; //
    private String otherPartCostAmount;//
    private Double costPrice;//成本价
    private Double costAmount;//
    private Float inQuantity;//入库数量
    private String allocateInNo;//
    private String partBatchNo;
    private String statusItem;
    private String recordId;
    private String flag;
    private Integer downTag;
    private Object isBattery;  //the battery part's flag
    private String batterySequence;
    /**
     * 剩余数量-传入-（新增时pc端就是总数量app是直接查出来的）
     * */
    private BigDecimal residualQuantity;


    /**
     * 溯源件数量-传入-（新增时根据接口写入app是直接查出来的）
     * */
    private BigDecimal sourceInQuantity;

    /**
     * 溯源件剩余数量-传入
     * */
    private BigDecimal sourceResidualQuantity;

    /**
     * 入库的溯源码
     * */
    private List<String> sourceCodes;
    /*
     * app传入，总数量
     * */
    private BigDecimal practicalQuantity;
    @Override
    public String toString() {
        return "PartAllocateInItemDto [itemId=" + itemId + ", dealerCode=" + dealerCode + ", storageCode=" + storageCode
                + ", storagePositionCode=" + storagePositionCode + ", partNo=" + partNo + ", partName=" + partName
                + ", unitCode=" + unitCode + ", inPrice=" + inPrice + ", inAmount=" + inAmount + ", otherPartCostPrice="
                + otherPartCostPrice + ", otherPartCostAmount=" + otherPartCostAmount + ", costPrice=" + costPrice
                + ", costAmount=" + costAmount + ", inQuantity=" + inQuantity + ", allocateInNo=" + allocateInNo
                + ", partBatchNo=" + partBatchNo + ", statusItem=" + statusItem + ", recordId=" + recordId + ", flag=" + flag
                + ", downTag=" + downTag + ", isBattery=" + isBattery + ", batterySequence=" + batterySequence + "]";
    }
}
