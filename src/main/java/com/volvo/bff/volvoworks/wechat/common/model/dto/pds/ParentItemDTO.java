package com.volvo.bff.volvoworks.wechat.common.model.dto.pds;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@ApiModel("ParentItemDTO")
public class ParentItemDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	// 检查项code
	@ApiModelProperty(name = "pdsCode")
	private String pdsCode;
	// 检查项name
	@ApiModelProperty(name = "pdsName")
	private String pdsName;
	// 检查项描述
	@ApiModelProperty(name = "pdsDescription")
	private String pdsDescription;
	// 检查数量
	@ApiModelProperty(name = "checkNum")
	private Integer checkNum;
	// 检查数据子集合
	@ApiModelProperty(name = "checkData")
	private List<PdsItemDTO> childCheckData;
}