package com.volvo.bff.volvoworks.wechat.common.model.dto.order;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("服务信息删除")
@Data
public class OrderServiceInfoDeleteDTO {

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号",name = "roNo",required = true)
    private String roNo;

    /**
     * 经销商
     */
    @ApiModelProperty(value = "经销商",name = "ownerCode",required = true)
    private String ownerCode;

    /**
     * 图片或视频主键id
     */
    @ApiModelProperty(value = "主键id",name = "ids", required = true)
    private List<Long> ids;



}
