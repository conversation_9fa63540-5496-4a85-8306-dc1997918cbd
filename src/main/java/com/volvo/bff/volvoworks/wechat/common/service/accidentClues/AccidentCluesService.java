package com.volvo.bff.volvoworks.wechat.common.service.accidentClues;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.accidentClues.*;
import com.volvo.bff.volvoworks.wechat.common.model.po.accidentClues.AccidentCluesPO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.accidentClues.*;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface AccidentCluesService {

    /*
     * 根据短信内容获取线索信息
     * */
    AccidentClueVo getInfoByPic(AccidentCluesDTO dto);


    /**
     * 修改事故线索
     * @param clue
     */
    Void updateAccidentClues(AccidentCluesDTO clue);

    /**
     * 保存事故线索
     * @param clue
     */
    Integer saveAccidentClues(AccidentCluesDTO clue);

    /**
     * 查看员工
     * @param getDealerUserDTO
     * @return
     */
    List<EmpByRoleCodeVO> getDealerUser(GetDealerUserDataDTO getDealerUserDTO);

    /**
     * 查看员工
     * @param getDealerUserDTO
     * @return
     */
    List<EmpByRoleCodeVO> getDealerUserTwo(GetDealerUserDataDTO getDealerUserDTO);

    /**
     * 根据id查询线索主单信息
     */
    AccidentCluesDTO selectById(Integer acId);

    /**
     * 根据图片得到事故线索信息
     * @param dto
     * @return
     */
    AccidentClueInfoVO getInfoByPicV2(AccidentCluesDTO dto);

    /**
     * 事故线索-预约单保存
     * @param dto
     * @return
     */
    BookingOrderReturnVo saveAppointmentOrder(AccidentCluesDTO dto);

    /**
     *查询全网经销商
     * @param params
     * @return
     */
    DealerVO getDealerList(DealerDTO params);

    /**
     * 获取事故线索列表
     * @param params
     * @return
     */
    PageBean<AccidentClueListVO> getList(AccidentClueListVO params);

    /**
     * 查询跟进历史（不分页）
     * @param acId
     * @return
     */
    List<AccidentCluesFollowDTO> getFollowList(Integer acId);

    /**
     * 线索分配
     * @param dto
     * @return
     */
    int insertttCluesAllot(AccidentCluesAllotDTO dto);

    /**
     * 线索跟进
     * @param dto
     * @return
     */
    int insertCluesFollow(AccidentCluesFollowDTO dto);

    /**
     * 呼叫登记
     * @param saCustomerNumberDTO
     * @return
     */
    String saveWorkNumber(AccidentCluesSaNumberDTO saCustomerNumberDTO);

    /**
     * 线索需求查询工单
     * @param paramsVo
     * @return
     */
    List<ClueRepairOrderResultVO> queryClueRepairOrder(RepairOrderHistoryParamsVO paramsVo);


    /**
     * 联系人修改
     * @param contact
     */
    Void updateContactInfo(AccidentClueContactDTO contact);

    /**
     * 白名单
     * @param ownerCode
     * @param modType
     * @param rosterType
     * @param vin
     * @return
     */
    Boolean checkWhitelist(String ownerCode, Integer modType, Integer rosterType, String vin);

    /**
     * 事故线索看板
     * @param dto
     * @return
     */
    AccidentClueDashBoardVO queryAccidentClueDashboard(DashBoardQueryDTO dto);

    /**
     * 事故线索集合
     */
    List<AccidentClueListVO> list(AccidentCluesListQueryDto params);

    /**
     * 根据线索id查询虚拟号
     */
    String selectVirtualNumberById(Long acId);

    List<AccidentClueFollowVo> followCount(AccidentClueListVO params);

    /**
     * 事故线索分配经销商
     */
    Void allotDealer(List<AllotDealerDTO> params);

    /**
     * 线索池列表统计数据查询
     */
    AccidentCluesSumInfoDTO getSumAccidentInfo(AccidentCluesExportQueryDto dto);

    /**
     * 查询预约单明细
     * @param bookingOrderNo  预约单号
     * @return
     */
    AppointmentDetailResultVo queryAppointmentDetail(String bookingOrderNo);

    /**
     * 查询服务顾问
     * @param dto
     * @return
     */
    List<EmpByRoleCodeVO> queryServiceAdvisors(GetDealerUserDataDTO dto);

    PageBean<AccidentCluesPO> selectPageBysql(AccidentCluesDTO dto, Long currentPage,  Long pageSize);
}
