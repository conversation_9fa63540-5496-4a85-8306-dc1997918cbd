package com.volvo.bff.volvoworks.wechat.common.service.accidentClues.impl;

import com.alibaba.fastjson.JSON;
import com.volvo.bff.volvoworks.wechat.common.exception.ServiceException;
import com.volvo.bff.volvoworks.wechat.common.feign.ApplicationMaintainManagementFeign;
import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.feign.DmscusCustomerFeign;
import com.volvo.bff.volvoworks.wechat.common.feign.DmscusIFserviceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.accidentClues.*;
import com.volvo.bff.volvoworks.wechat.common.model.po.accidentClues.AccidentCluesPO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.accidentClues.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.accidentClues.AccidentCluesService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import com.volvo.bff.volvoworks.wechat.common.utils.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
@Slf4j
public class AccidentCluesServiceImpl implements AccidentCluesService {

    @Autowired
    private DmscusCustomerFeign dmscusCustomerFeign;

    @Autowired
    private DmscusIFserviceFeign dmscusIFserviceFeign;

    @Resource
    private ApplicationMaintainManagementFeign maintainManagementFeign;

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Override
    public AccidentClueVo getInfoByPic(AccidentCluesDTO dto){
        DmsResultDTO<AccidentClueVo> response = dmscusCustomerFeign.getInfoByPic(dto);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public Void updateAccidentClues(AccidentCluesDTO clue) {
        DmsResultDTO<Void> updateAccidentClues = dmscusCustomerFeign.updateAccidentClues(clue);
        ErrorConversionUtils.errorCodeConversion(updateAccidentClues);
        return updateAccidentClues.getData();
    }

    @Override
    public Integer saveAccidentClues(AccidentCluesDTO clue) {
        DmsResultDTO<Integer> saveAccidentClues = dmscusCustomerFeign.saveAccidentClues(clue);
        ErrorConversionUtils.errorCodeConversion(saveAccidentClues);
        return  saveAccidentClues.getData();
    }

    @Override
    public List<EmpByRoleCodeVO> getDealerUser(GetDealerUserDataDTO getDealerUserDTO) {
        DmsResultDTO<List<EmpByRoleCodeVO>> dealerUser = dmscusCustomerFeign.getDealerUser(getDealerUserDTO);
        ErrorConversionUtils.errorCodeConversion(dealerUser);
        return dealerUser.getData();
    }

    @Override
    public List<EmpByRoleCodeVO> getDealerUserTwo(GetDealerUserDataDTO getDealerUserDTO) {
        DmsResultDTO<List<EmpByRoleCodeVO>> dealerUser = dmscusIFserviceFeign.getDealerUserTwo(getDealerUserDTO);
        ErrorConversionUtils.errorCodeConversion(dealerUser);
        return dealerUser.getData();
    }

    @Override
    public AccidentCluesDTO selectById(Integer acId) {
        DmsResultDTO<AccidentCluesDTO> selectById = dmscusCustomerFeign.selectAccidentCluesById(acId);
        ErrorConversionUtils.errorCodeConversion(selectById);
        return selectById.getData();
    }
    @Override
    public AccidentClueInfoVO getInfoByPicV2(AccidentCluesDTO dto) {
        DmsResultDTO<AccidentClueInfoVO> infoByPic = dmscusCustomerFeign.getInfoByPicV2(dto);
        ErrorConversionUtils.errorCodeConversion(infoByPic);
        return infoByPic.getData();
    }

    /**
     * 事故线索-预约单保存
     *
     * @param dto
     * @return
     */
    @Override
    public BookingOrderReturnVo saveAppointmentOrder(AccidentCluesDTO dto) {

        DmsResultDTO<BookingOrderReturnVo> returnVoDmsResultDTO = dmscusCustomerFeign.saveAppointmentOrder(dto);
        ErrorConversionUtils.errorCodeConversion(returnVoDmsResultDTO);
        BookingOrderReturnVo data = returnVoDmsResultDTO.getData();
        log.info("bff-volvoworks-aftersales服务->事故线索-预约单保存，入参：【{}】，出参外层包装数据：【{}】，出参内层数据：【{}】", JSONUtil.objectToJson(dto), JSONUtil.objectToJson(returnVoDmsResultDTO), null == data ? null : JSONUtil.objectToJson(data));

        return data;
    }

    @Override
    public DealerVO getDealerList(DealerDTO params) {
        DmsResultDTO<DealerVO> dealerList = maintainManagementFeign.getDealerList(params);
        ErrorConversionUtils.errorCodeConversion(dealerList);
        return dealerList.getData();
    }

    @Override
    public PageBean<AccidentClueListVO> getList(AccidentClueListVO params) {
        DmsResultDTO<PageBean<AccidentClueListVO>> list = dmscusCustomerFeign.getList(params);
        ErrorConversionUtils.errorCodeConversion(list);
        return list.getData();
    }
    @Override
    public List<AccidentCluesFollowDTO> getFollowList(Integer acId) {
        DmsResultDTO<List<AccidentCluesFollowDTO>> followList = dmscusCustomerFeign.getFollowList(acId);
        ErrorConversionUtils.errorCodeConversion(followList);
        return followList.getData();
    }

    @Override
    public int insertttCluesAllot(AccidentCluesAllotDTO dto) {
        DmsResultDTO<Integer> insertttCluesAllot = dmscusCustomerFeign.insertttCluesAllot(dto);
        ErrorConversionUtils.errorCodeConversion(insertttCluesAllot);
        return insertttCluesAllot.getData();
    }

    @Override
    public int insertCluesFollow(AccidentCluesFollowDTO dto) {
        DmsResultDTO<Integer> insertCluesFollow = dmscusCustomerFeign.insertCluesFollow(dto);
        ErrorConversionUtils.errorCodeConversion(insertCluesFollow);
        return insertCluesFollow.getData();
    }

    @Override
    public String saveWorkNumber(AccidentCluesSaNumberDTO saCustomerNumberDTO) {
        DmsResultDTO<String> saveWorkNumber = dmscusCustomerFeign.saveWorkNumber(saCustomerNumberDTO);
        ErrorConversionUtils.errorCodeConversion(saveWorkNumber);
        return saveWorkNumber.getData();
    }

    @Override
    public List<ClueRepairOrderResultVO> queryClueRepairOrder(RepairOrderHistoryParamsVO paramsVo) {
        DmsResultDTO<List<ClueRepairOrderResultVO>> queryClueRepairOrder = dmscloudServiceFeign.getClueRepairOrder(paramsVo);
        ErrorConversionUtils.errorCodeConversion(queryClueRepairOrder);
        return queryClueRepairOrder.getData();
    }

    @Override
    public Void updateContactInfo(AccidentClueContactDTO contact) {

        DmsResultDTO<Void> result = dmscusCustomerFeign.updateContactInfo(contact);
        ErrorConversionUtils.errorCodeConversion(result);
        return result.getData();
    }

    @Override
    public Boolean checkWhitelist(String ownerCode, Integer modType, Integer rosterType, String vin) {
        DmsResultDTO<Boolean> resultDto = dmscloudServiceFeign.checkWhitelist(ownerCode, modType, rosterType, vin);
        ErrorConversionUtils.errorCodeConversion(resultDto);
        return resultDto.getData();
    }

    @Override
    public AccidentClueDashBoardVO queryAccidentClueDashboard(DashBoardQueryDTO dto) {

        DmsResultDTO<AccidentClueDashBoardVO> resultDto = dmscusCustomerFeign.getDashBoard(dto);
        ErrorConversionUtils.errorCodeConversion(resultDto);
        return resultDto.getData();
    }

    /**
     * 事故线索集合
     */
    @Override
    public List<AccidentClueListVO> list(AccidentCluesListQueryDto params) {
        if (Objects.isNull(params)){
            throw new ServiceException("缺少参数");
        }
        DmsResultDTO<List<AccidentClueListVO>> list = maintainManagementFeign.accidentClueList(params);
        ErrorConversionUtils.errorCodeConversion(list);
        return list.getData();
    }

    @Override
    public String selectVirtualNumberById(Long acId) {
        DmsResultDTO<String> virtualNumber = maintainManagementFeign.getVirtualNumber(acId);
        ErrorConversionUtils.errorCodeConversion(virtualNumber);
        return virtualNumber.getData();
    }

    @Override
    public List<AccidentClueFollowVo> followCount(AccidentClueListVO params) {
        DmsResultDTO<List<AccidentClueFollowVo>> res = dmscusCustomerFeign.followCount(params);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public Void allotDealer(List<AllotDealerDTO> params) {
        List<AllotDealerDTO> newbie = params.stream().filter(Objects::nonNull).filter(item -> Objects.nonNull(item.getSourceChannel()) && "NEWBIE".equalsIgnoreCase(item.getSourceChannel())).collect(Collectors.toList());
        if(Objects.nonNull(newbie) && newbie.size() > BigDecimal.ZERO.intValue()){
            throw new ServiceException("仅支持分配非门店自建线索","仅支持分配非门店自建线索");
        }
        DmsResultDTO<Void> allotDealer = maintainManagementFeign.allotDealer(params.stream().filter(Objects::nonNull).collect(Collectors.toList()));
        ErrorConversionUtils.errorCodeConversion(allotDealer);
        return allotDealer.getData();
    }

    @Override
    public AccidentCluesSumInfoDTO getSumAccidentInfo(AccidentCluesExportQueryDto dto) {
        log.info("bff getSumAccidentInfo start:{}", JSON.toJSONString(dto));
        DmsResultDTO<AccidentCluesSumInfoDTO> sumAccidentInfo = maintainManagementFeign.getSumAccidentInfo(dto);
        ErrorConversionUtils.errorCodeConversion(sumAccidentInfo);
        return sumAccidentInfo.getData();
    }

    /**
     * 查询预约单明细
     *
     * @param bookingOrderNo 预约单号
     * @return
     */
    @Override
    public AppointmentDetailResultVo queryAppointmentDetail(String bookingOrderNo) {

        DmsResultDTO<AppointmentDetailResultVo> res = dmscloudServiceFeign.queryAppointmentDetail(bookingOrderNo);
        ErrorConversionUtils.errorCodeConversion(res);
        AppointmentDetailResultVo data = res.getData();

        log.info("bff-volvoworks-aftersales服务->查询预约单明细，入参：【{}】,出参外层包装数据：【{}】，出参内层数据：【{}】", bookingOrderNo, JSONUtil.objectToJson(res), JSONUtil.objectToJson(data));

        return data;
    }

    /**
     * 查询服务顾问
     *
     * @param dto
     * @return
     */
    @Override
    public List<EmpByRoleCodeVO> queryServiceAdvisors(GetDealerUserDataDTO dto) {

        DmsResultDTO<List<EmpByRoleCodeVO>> res = dmscusIFserviceFeign.getDealerUser(dto);
        ErrorConversionUtils.errorCodeConversion(res);
        List<EmpByRoleCodeVO> data = res.getData();

        log.info("bff-volvoworks-aftersales服务->查询服务顾问，入参：【{}】,出参外层包装数据：【{}】，出参内层数据：【{}】", JSONUtil.objectToJson(dto), JSONUtil.objectToJson(res), JSONUtil.objectToJson(data));

        return data;
    }

    @Override
    public PageBean<AccidentCluesPO> selectPageBysql(AccidentCluesDTO dto, Long currentPage, Long pageSize) {
        log.info("selectPageBysql start:{}", JSON.toJSONString(dto));
        DmsResultDTO<PageBean<AccidentCluesPO>> res = dmscusCustomerFeign.selectPageBysql(dto, currentPage, pageSize);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }
}
