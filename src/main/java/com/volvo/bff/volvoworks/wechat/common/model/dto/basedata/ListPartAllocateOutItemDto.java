package com.volvo.bff.volvoworks.wechat.common.model.dto.basedata;

import lombok.Data;

import java.util.List;

@Data
public class ListPartAllocateOutItemDto {
    private String allocateOutNo;
    private String customerCode;
    private String customerName;
    private String orderDate;
    private Integer orderStatus;
    private Integer orderType;
    private String originalOrder;
    private String applicant;
    private String applicantPhone;
    private String applyRemark;
    private Integer verifyStatus;
    private String verifyDate;
    private String verifyRemark;
    private String remark;
    private String items;
    private List<PartAllocateOutItemDto> dms_table;
    private Double costAmountTotal;
    private Double outAmountTotal;
    private String  recordShortPart;
    // 调拨原因
    private Integer allocateReason;

    /**
     * 原因选择
     * */
    private String reasonType;

    /**
     * 原因
     * */
    private String reason;

    /**
     * 出入库类型（APP/PC）
     * */
    private String inOutSource;

    /**
     * 异常溯源码信息
     * */
    private List<String> abnormalSourceCodes;
}