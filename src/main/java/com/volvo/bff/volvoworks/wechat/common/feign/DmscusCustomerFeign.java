package com.volvo.bff.volvoworks.wechat.common.feign;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.accidentClues.*;
import com.volvo.bff.volvoworks.wechat.common.model.po.accidentClues.AccidentCluesPO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.accidentClues.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@FeignClient(name = "dmscus-customer",
        url = "${mse-in.tob.dmscus-customer.url}",
        path = "${mse-in.tob.dmscus-customer.path}")
public interface DmscusCustomerFeign {

    @PostMapping(value = "/accidentClues/getInfoByPic")
    @ApiOperation(value = "根据短信得到事故线索信息", notes = "根据短信得到事故线索信息")
    DmsResultDTO<AccidentClueVo> getInfoByPic(@RequestBody AccidentCluesDTO dto);

    @ApiOperation(value = "保存事故线索")
    @PostMapping(value = "/accidentClues/save")
    DmsResultDTO<Integer> saveAccidentClues(@RequestBody AccidentCluesDTO clue);

    @ApiOperation(value = "修改事故线索")
    @PostMapping(value = "/accidentClues/update")
    DmsResultDTO<Void> updateAccidentClues(@RequestBody AccidentCluesDTO clue);

    @ApiOperation(value = "查看员工")
    @PostMapping(value="/accidentClues/dealer/user")
    DmsResultDTO<List<EmpByRoleCodeVO>> getDealerUser(@RequestBody GetDealerUserDataDTO getDealerUserDTO);

    @ApiOperation(value = "查看员工")
    @PostMapping(value="/dealer/userTwo")
    DmsResultDTO<List<EmpByRoleCodeVO>> getDealerUserTwo(@RequestBody GetDealerUserDataDTO getDealerUserDTO);

    /**
     * 根据id查询线索主单信息
     */
    @GetMapping(value = "/accidentClues/{acId}")
    DmsResultDTO<AccidentCluesDTO> selectAccidentCluesById(@PathVariable("acId") Integer acId);

    @ApiOperation("根据图片得到事故线索信息")
    @PostMapping("/accidentClues/getInfoByPic")
    DmsResultDTO<AccidentClueInfoVO> getInfoByPicV2(@RequestBody AccidentCluesDTO dto);

    /**
     * 事故线索-预约单保存
     * @param dto
     * @return
     */
    @ApiOperation(value = "事故线索-预约单保存")
    @PostMapping(value = "/accidentClues/saveAppointmentOrder")
    DmsResultDTO<BookingOrderReturnVo> saveAppointmentOrder(@RequestBody AccidentCluesDTO dto);

    @ApiOperation(value = "获取事故线索列表")
    @PostMapping(value = "/accidentClues/getList")
    DmsResultDTO<PageBean<AccidentClueListVO>> getList(@RequestBody AccidentClueListVO params);

    @ApiOperation(value = "修改线索联系人")
    @PostMapping(value = "/accidentClues/updateContactInfo")
    DmsResultDTO<Void> updateContactInfo(@RequestBody AccidentClueContactDTO contact);

    /**
     * 呼叫登记
     */
    @ApiOperation(value = "呼叫登记")
    @PostMapping(value = "/accidentClues/saveCluesSaNumber")
    DmsResultDTO<String> saveWorkNumber(@RequestBody AccidentCluesSaNumberDTO saCustomerNumberDTO);

    /**
     * 线索跟进
     */
    @ApiOperation("线索跟进")
    @PostMapping(value = "/accidentClues/follow")
    DmsResultDTO<Integer> insertCluesFollow(@RequestBody AccidentCluesFollowDTO dto);

    /**
     * 线索分配
     */
    @ApiOperation("线索分配")
    @PostMapping(value = "/accidentClues/allot")
    DmsResultDTO<Integer> insertttCluesAllot(@RequestBody AccidentCluesAllotDTO dto);

    @ApiOperation(value = "查询跟进历史不分页")
    @GetMapping(value = "/accidentClues/getFollowList")
    DmsResultDTO<List<AccidentCluesFollowDTO>> getFollowList(@RequestParam("acId") Integer acId);

    @ApiOperation(value = "事故线索看板")
    @PostMapping(value = "/accidentClues/getDashBoard")
    DmsResultDTO<AccidentClueDashBoardVO> getDashBoard(@RequestBody DashBoardQueryDTO dto);

    @PostMapping("accidentClues/follow/count")
    DmsResultDTO<List<AccidentClueFollowVo>> followCount(@RequestBody AccidentClueListVO params);

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "/accidentClues")
    DmsResultDTO<PageBean<AccidentCluesPO>> selectPageBysql(@SpringQueryMap AccidentCluesDTO dto, @RequestParam("currentPage") Long currentPage, @RequestParam("pageSize") Long pageSize);
}
