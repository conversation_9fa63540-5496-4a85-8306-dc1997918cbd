package com.volvo.bff.volvoworks.wechat.common.model.dto.pds;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@ApiModel("GroupDTO")
public class GroupDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	// 检查数量
	@ApiModelProperty(name = "checkNum")
	private Integer checkNum;
	// 检查父项
	@ApiModelProperty(name = "checkData")
	private List<ParentItemDTO> checkData;
}