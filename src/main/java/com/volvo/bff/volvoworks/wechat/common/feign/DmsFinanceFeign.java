package com.volvo.bff.volvoworks.wechat.common.feign;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@FeignClient(name = "dms-finance-service",
        url = "${mse-in.tob.dms-finance-service.url}",
        path = "${mse-in.tob.dms-finance-service.path}")
public interface DmsFinanceFeign {

}
