package com.volvo.bff.volvoworks.wechat.common.feign;

import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

@FeignClient(name = "mid-end-org-center",
        url = "${mse-in.mid.mid-end-org-center.url}",
        path = "${mse-in.mid.mid-end-org-center.path}")
public interface MidEndOrgCenterFeign {

}
