package com.volvo.bff.volvoworks.wechat.common.model.dto.stockmanage;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 *
 * TODO description
 * <AUTHOR>
 * @date 2017年4月26日
 */
@Data
public class ListPartAllocateInItemDto {
    private String items;
    private  List<PartAllocateInItemDto> dms_part_allocate_in;
    private String allocateInNo;//调拨入库单号
    private String allocateInID;//调拨入库主单ID
    private String customerCode;//供应商代码
    private String customerName;//供应商名称
    private String stockInDate;//入库日期
    private String stockInVoucher;//入库凭证
    private String handler;//经手人
    private String lockUser;//锁定人
    private String isIdleAllocation;//是否呆滞调拨
    private String isNetTransfer;//是否网内调拨
    private String remark;//备注
    private String status;
    private String allocateOutNo;//此字段未使用
    private String netAllocateNo;//网内调拨单号
    private String oldAllocateNo;//分厂调拨出库单号
    private String credence;
    private String fromEntiy;
    private Double befoeTaxAmount;
    private Double totalAmount;
    private String isPayoff;

    private String reasonType; //原因选择

    private String reason; //原因

    private String inOutSource; //出入库类型（APP/手工）

    /*调拨单号*/
    private String originalOrder;

    private Date orderDate;//开单日期

    @Override
    public String toString() {
        return "ListPartAllocateInItemDto [items=" + items + ", dms_part_allocate_in=" + dms_part_allocate_in
                + ", allocateInNo=" + allocateInNo + ", customerCode=" + customerCode + ", customerName=" + customerName
                + ", stockInDate=" + stockInDate + ", stockInVoucher=" + stockInVoucher + ", handler=" + handler
                + ", lockUser=" + lockUser + ", isIdleAllocation=" + isIdleAllocation + ", isNetTransfer=" + isNetTransfer
                + ", remark=" + remark + ", status=" + status + ", allocateOutNo=" + allocateOutNo + ", netAllocateNo="
                + netAllocateNo + ", oldAllocateNo=" + oldAllocateNo + ", credence=" + credence + ", fromEntiy=" + fromEntiy
                + ", befoeTaxAmount=" + befoeTaxAmount + ", totalAmount=" + totalAmount + ", isPayoff=" + isPayoff + "]";
    }
}
