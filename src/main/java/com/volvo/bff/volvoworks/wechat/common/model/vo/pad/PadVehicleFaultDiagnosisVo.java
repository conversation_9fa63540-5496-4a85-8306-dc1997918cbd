package com.volvo.bff.volvoworks.wechat.common.model.vo.pad;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName PadVehicleFaultDiagnosisVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/5/20 19:06
 */
@Data
@ApiModel("pad车辆故障问诊值的存放")
public class PadVehicleFaultDiagnosisVo {

    @ApiModelProperty(value = "检查项代码",name = "contentCode")
    private String contentCode;

    @ApiModelProperty(value = "描述值",name = "failValue")
    private String failValue;

}
