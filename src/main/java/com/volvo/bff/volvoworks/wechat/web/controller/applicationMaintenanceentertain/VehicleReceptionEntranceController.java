package com.volvo.bff.volvoworks.wechat.web.controller.applicationMaintenanceentertain;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.applicationMaintenanceentertain.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.applicationMaintenanceentertain.IVehicleReceptionEntranceService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/vehicle-reception-entrance/api/v1")
@Api(tags = "车辆进场接待", description = "车辆进场接待", produces = "application/json")
public class VehicleReceptionEntranceController {

    @Autowired
    private IVehicleReceptionEntranceService vehicleReceptionEntrance;

    @ApiOperation(value = "查询进场车辆列表", notes = "查询进场车辆列表", httpMethod = "GET")
    @GetMapping("/queryVehicleEntranceList")
    @CrossOrigin
    public ResponseDTO<PageBean<VehicleEntranceVO>> queryVehicleEntranceList(
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize,
            @RequestParam(value = "pageIndex", defaultValue = "1") Integer pageIndex,
            @RequestParam(value = "dealerCode" , required = true) String dealerCode){
        DmsResultDTO<PageBean<VehicleEntranceVO>> pageBeanDmsResultDTO = vehicleReceptionEntrance.queryVehicleEntranceList(pageSize, pageIndex, dealerCode);
        return  ResponseUtils.success(pageBeanDmsResultDTO.getData());
    }


    @ApiOperation(value = "校验当前人员是否可以接待当前车辆", notes = "校验当前人员是否可以接待当前车辆", httpMethod = "POST")
    @PostMapping("/checkVehicleReception")
    @CrossOrigin
    public ResponseDTO<Boolean> checkVehicleReception(@RequestBody CheckVehicleEntranceReqVO checkVehicleEntranceReq){
        DmsResultDTO<Boolean> booleanDmsResultDTO = vehicleReceptionEntrance.checkVehicleReception(checkVehicleEntranceReq);
        return  ResponseUtils.success(booleanDmsResultDTO.getData());
    }


    @ApiOperation(value = "更新进场车辆信息", notes = "更新进场车辆信息", httpMethod = "POST")
    @PostMapping("/updateVehicleReception")
    @CrossOrigin
    public ResponseDTO<Boolean> updateVehicleReception(@RequestBody VehicleEntranceVO vehicleEntrance){
        DmsResultDTO<Boolean> booleanDmsResultDTO = vehicleReceptionEntrance.updateVehicleReception(vehicleEntrance);
        return ResponseUtils.success(booleanDmsResultDTO.getData());
    }


    @ApiOperation(value = "根据车牌号查询进场数据", notes = "根据车牌号查询进场数据", httpMethod = "GET")
    @GetMapping("/queryVehicleEntranceByLicensePlate")
    @CrossOrigin
    public ResponseDTO<VehicleEntranceVO> queryVehicleEntranceByLicensePlate(@RequestParam(value = "licensePlate" , required = true) String licensePlate){
        DmsResultDTO<VehicleEntranceVO> vehicleEntranceVODmsResultDTO = vehicleReceptionEntrance.queryVehicleEntranceByLicensePlate(licensePlate);
        return ResponseUtils.success(vehicleEntranceVODmsResultDTO.getData());
    }


    @ApiOperation(value = "根据车牌号查询两天内快速接待数据", notes = "根据车牌号查询两天内快速接待数据", httpMethod = "GET")
    @GetMapping("/queryReceptionEntranceByLicensePlate")
    @CrossOrigin
    public ResponseDTO<SubmitEntranceInfoReqVO> queryReceptionEntranceByLicensePlate(@RequestParam(value = "licensePlate" , required = true) String licensePlate){
        DmsResultDTO<SubmitEntranceInfoReqVO> submitEntranceInfoReqVODmsResultDTO = vehicleReceptionEntrance.queryReceptionEntranceByLicensePlate(licensePlate);
        return ResponseUtils.success(submitEntranceInfoReqVODmsResultDTO.getData());
    }


    @ApiOperation(value = "提交建单信息", notes = "提交建单信息", httpMethod = "POST")
    @PostMapping("/submitEntranceInfo")
    @CrossOrigin
    public ResponseDTO<Boolean> submitEntranceInfo(@RequestBody SubmitEntranceInfoReqVO submitEntranceInfoReq){
        DmsResultDTO<Boolean> booleanDmsResultDTO = vehicleReceptionEntrance.submitEntranceInfo(submitEntranceInfoReq);
        return ResponseUtils.success(booleanDmsResultDTO.getData());
    }

    @ApiOperation(value = "查询白名单", notes = "查询白名单", httpMethod = "GET")
    @GetMapping("/queryWhiteList")
    @CrossOrigin
    public ResponseDTO<Boolean> queryWhiteList(@RequestParam(value = "dealerCode" , required = true) String dealerCode){
        DmsResultDTO<Boolean> booleanDmsResultDTO = vehicleReceptionEntrance.queryWhiteList(dealerCode);
        return ResponseUtils.success(booleanDmsResultDTO.getData());
    }
}
