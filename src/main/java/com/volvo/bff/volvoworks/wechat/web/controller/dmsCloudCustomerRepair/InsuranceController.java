package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudCustomerRepair;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.insuranceBusiness.InsuranceService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(value = "保险公司接口", tags = { "保险公司接口" })
@RestController
@RequestMapping("/basedata/insuranceCo")
public class InsuranceController {

    @Autowired
    private InsuranceService insuranceService;

    @ApiOperation(value = "保险公司下拉框加载", notes = "保险公司下拉框加载", httpMethod = "GET")
    @SuppressWarnings("rawtypes")
    @GetMapping(value = "/insurance/dicts")
    public ResponseDTO<List<Map>> selectEmployees(@RequestParam(value = "isValid" ,required = false) String isValid) {
        return ResponseUtils.success(insuranceService.selectInsurance(isValid));
    }

}
