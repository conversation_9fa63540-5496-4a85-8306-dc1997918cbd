package com.volvo.bff.volvoworks.wechat.common.model.vo.pad;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @ClassName PadVehicleDetailsVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/5/20 21:03
 */
@Data
@ApiModel("pad问诊详情VO")
public class PadVehicleDetailsVo {
    @ApiModelProperty(value="问诊录音地址",name = "failRecurl")
    private String failRecurl;

    @ApiModelProperty(value="客户故障描述",name = "failDesc")
    private String failDesc;

    @ApiModelProperty(value="车辆故障问诊名称",name = "failName")
    private String failName;

    @ApiModelProperty(value="车辆故障排序",name = "failOrder")
    private String failOrder;

    @ApiModelProperty(value="故障问诊列表",name = "failName")
    private List<Map<String,PadVehicleFaultDiagnosisVo>> padVehicleFaultDiagnosisList;

}
