package com.volvo.bff.volvoworks.wechat.common.service.pad.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.pad.PadCustomerRequireResultAllVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.pad.PadVehiclePreviewResultVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.pad.PadVehiclePreviewVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.pad.RepairResultVo;
import com.volvo.bff.volvoworks.wechat.common.service.pad.PadVehiclePreviewService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class PadVehiclePreviewServiceImpl implements PadVehiclePreviewService {

    @Resource
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Override
    public PadCustomerRequireResultAllVo queryOwnerVehicleResultAll(String yjNo) {
        DmsResultDTO<PadCustomerRequireResultAllVo> response = dmscloudServiceFeign.queryOwnerVehicleResultAll(yjNo);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public PadVehiclePreviewResultVo queryOwnerVehicle(PadVehiclePreviewVo padVehiclePreviewVo) {
        DmsResultDTO<PadVehiclePreviewResultVo> response = dmscloudServiceFeign.queryOwnerVehicle(padVehiclePreviewVo);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public String checkOwnerVehicleVin(PadVehiclePreviewVo padVehiclePreviewVo) {
        DmsResultDTO<String> response = dmscloudServiceFeign.checkOwnerVehicleVin(padVehiclePreviewVo);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public String precheckFinshed(String yjNo) {
        DmsResultDTO<String> response = dmscloudServiceFeign.precheckFinshed(yjNo);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public RepairResultVo savePreviewTransferOrder(String yjNo, String ownerCode, String signImgUrl) {
        DmsResultDTO<RepairResultVo> response = dmscloudServiceFeign.savePreviewTransferOrder(yjNo,ownerCode,signImgUrl);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

}
