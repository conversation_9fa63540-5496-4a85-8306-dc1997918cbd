package com.volvo.bff.volvoworks.wechat.common.model.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName QueryStructureCheckRrquestVo
 * <AUTHOR>
 * @Date 2020/6/2 16:32
 */
@Data
@ApiModel("QueryStructureCheckRrquestV3Vo")
public class QueryStructureCheckRrquestV3Vo extends QueryStructureCheckRrquestV2Vo {
	
    @ApiModelProperty(value = "所有者代码",name = "ownerCode",required = true)
    private String ownerCode;
}
