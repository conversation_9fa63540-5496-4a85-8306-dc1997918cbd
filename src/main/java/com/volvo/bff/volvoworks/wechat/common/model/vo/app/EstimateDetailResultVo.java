package com.volvo.bff.volvoworks.wechat.common.model.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("定损维护明细结果vo")
public class EstimateDetailResultVo implements Serializable {

    @ApiModelProperty(value = "撞击类型(一级名称)",name = "oneName")
    private String oneName;

    @ApiModelProperty(value = "故障部位",name = "localName")
    private String localName;

    @ApiModelProperty(value = "是否更换",name = "isReturn")
    private Integer isReturn;

    @ApiModelProperty(value = "故障描述",name = "remark")
    private String remark;

    @ApiModelProperty(value = "故障部位ID",name = "locId")
    private String locId;

    @ApiModelProperty(value = "图片list",name = "picList")
    private List<String> picList;
}
