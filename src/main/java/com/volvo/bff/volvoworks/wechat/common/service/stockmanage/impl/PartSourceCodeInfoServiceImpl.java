package com.volvo.bff.volvoworks.wechat.common.service.stockmanage.impl;

import com.alibaba.fastjson.JSON;
import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.dto.stockmanage.SourcePartFlowDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.stockmanage.PartSourceCodeInfoService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Slf4j
@Service
public class PartSourceCodeInfoServiceImpl implements PartSourceCodeInfoService {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Override
    public BigDecimal checkSourceCode(Integer code, String jdShipNo, String businessNo, String partNo, String sourceCode, int dirDetail){
        log.info("checkSourceCode: code={}, jdShipNo={}, businessNo={}, partNo={}, sourceCode={}, dirDetail={}", code, jdShipNo, businessNo, partNo, sourceCode, dirDetail);
        DmsResultDTO<BigDecimal> response = dmscloudServiceFeign.checkSourceCode(code, jdShipNo, businessNo, partNo, sourceCode, dirDetail);
        log.info("checkSourceCode response: {}", JSON.toJSONString(response));
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public void saveNoSystemRecordSourcePartInOutFlow(SourcePartFlowDto req){
        DmsResultDTO<Void> response = dmscloudServiceFeign.saveNoSystemRecordSourcePartInOutFlow(req);
        ErrorConversionUtils.errorCodeConversion(response);
    }
}
