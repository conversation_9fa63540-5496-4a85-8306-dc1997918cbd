package com.volvo.bff.volvoworks.wechat.common.service.app;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.DamagedEstimateOrderResultVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.DamagedPartResultDto;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 定损维护
 *
 * <AUTHOR>
 * @date 2020年5月30日
 */
public interface DamagedPartService {

    /**
     * 查询估价单
     * @param damagedEstimateOrderParamVo
     * @return
     */
    PageBean<DamagedEstimateOrderResultVo> queryEstimateForDamaged(DamagedEstimateOrderParamVo damagedEstimateOrderParamVo);

    /**
     * 查询定损维护级联部件
     *
     * <AUTHOR>
     * @date 2020年5月30日
     * @return
     */
    List<DamagedPartResultDto> queryDamagedPartList(String estimateNo, String locId);

    /**
     * 保存定损维护级联部件
     *
     * <AUTHOR>
     * @date 2020年6月05日
     * @return
     */
    String saveDamaged(SaveDamagedPartVo saveDamagedPartV);

    /**
     * 查询保存的部件
     * @param saveDamagedPartVo
     * @return
     */
    SaveDamagedPartVo queryDamagedPart(SaveDamagedPartVo saveDamagedPartVo);

    /**
     * 查询定损维护明细
     * @param estimateNo
     * @return
     */
    PageBean<EstimateDetailResultVo> queryEstimateDetail(@RequestParam("estimateNo") String estimateNo);


    String deleteEstimate(String estimateNo,String locId);

}
