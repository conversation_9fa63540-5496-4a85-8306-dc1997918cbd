package com.volvo.bff.volvoworks.wechat.common.model.dto.applicationMaintenanceentertain;

import lombok.Data;

import java.io.Serializable;

@Data
public class ReceptionEntrancePageConfigReqDTO implements Serializable {

	private static final long serialVersionUID = -7666899984265887622L;

    /*
     * 业务类型  手动建单原因/进场接待详情页
     */
    private String businessType;
    
    
    //=============================进场接待详情页=============================
	/*
	 * 建单类型   车辆登记/快速建单/环检接车
	 */
	private String type;
	/*
	 * 建单来源  0/1 快速建单/手动建单
	 */
    private String serviceSource;

    
}
