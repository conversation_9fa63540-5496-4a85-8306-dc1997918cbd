package com.volvo.bff.volvoworks.wechat.common.model.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName StructureWorkHourOneRequestVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/6/4 16:53
 */
@Data
@ApiModel("结构件保存VO")
public class StructureWorkHourOneRequestVo {

    @ApiModelProperty(value = "工时编号",name = "labourCode")
    private String labourCode;

    @ApiModelProperty(value = "车牌号",name = "license")
    private String license;

    @ApiModelProperty(value = "工单号",name = "roNo")
    private String roNo;

    @ApiModelProperty(value = "备注",name = "remark")
    private String remark;

    @ApiModelProperty(value = "工时唯一标识",name = "itemId")
    private Integer itemId;

    @ApiModelProperty(value = "图片链接",name = "structureWorkHourPicList")
    private List<String> structureWorkHourPicList;
}
