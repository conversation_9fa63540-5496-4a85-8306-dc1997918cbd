package com.volvo.bff.volvoworks.wechat.common.model.vo.assistant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("根据车牌查询客户信息Vo")
public class CustomerInfoResultVo {

    @ApiModelProperty(value = "车牌号",name = "license")
    private String license;

    @ApiModelProperty(value = "vin",name = "vin")
    private String vin;

    @ApiModelProperty(value = "车型",name = "model")
    private String model;

    @ApiModelProperty(value = "车主",name = "ownerName")
    private String ownerName;

    @ApiModelProperty(value = "车主手机号",name = "phone")
    private String phone;

    @ApiModelProperty(value = "邮箱",name = "eMail")
    private String eMail;

    @ApiModelProperty(value = "送修人",name = "contactorName")
    private String contactorName;

    @ApiModelProperty(value = "送修人手机号",name = "contactorPhone")
    private String contactorPhone;

    @ApiModelProperty(value = "里程",name = "mileage")
    private String mileage;

    @ApiModelProperty(value = "进厂时间",name = "firstInDate")
    private String firstInDate;

    @ApiModelProperty(value = "销售日期",name = "salesDate")
    private String salesDate;

    @ApiModelProperty(value = "地址",name = "address")
    private String address;
}
