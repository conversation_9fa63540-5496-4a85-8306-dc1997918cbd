package com.volvo.bff.volvoworks.wechat.common.service.Icup;

import com.volvo.bff.volvoworks.wechat.common.feign.ApplicationMaintainManagementFeign;
import com.volvo.bff.volvoworks.wechat.common.model.dto.icup.IcupMileageDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class IcupServiceImpl implements IcupService{

    @Autowired
    private ApplicationMaintainManagementFeign applicationMaintainManagementFeign;

    @Override
    public IcupMileageDto getIcupMileageByVin(String vin) {
        DmsResultDTO<IcupMileageDto> icupMileageByVin = applicationMaintainManagementFeign.getIcupMileageByVin(vin);
        return icupMileageByVin.getData();
    }



}
