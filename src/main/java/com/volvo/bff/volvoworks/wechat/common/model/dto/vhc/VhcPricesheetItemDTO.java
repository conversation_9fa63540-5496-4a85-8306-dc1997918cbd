package com.volvo.bff.volvoworks.wechat.common.model.dto.vhc;

import lombok.Data;

import java.io.Serializable;

/**
 * vhc报价-检查项二级类目-报价明细DTO
 */
@Data
public class VhcPricesheetItemDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private Integer id;
    /**
     * 种类
     */
    private String pricesheetType;
    /**
     * 代码
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 销售单价
     */
    private String price;
    /**
     * 数量
     */
    private String quantity;
    /**
     * 是否原厂
     */
    private String isOemk;
    /**
     * 缺货状态
     */
    private String lackState;
    /**
     * 在修状态
     */
    private String isRepair;
    /**
     * 删除标志
     */
    private boolean deleteFlag;

}
