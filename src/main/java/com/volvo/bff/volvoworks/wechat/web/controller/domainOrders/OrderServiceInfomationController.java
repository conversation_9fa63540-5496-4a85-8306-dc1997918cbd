package com.volvo.bff.volvoworks.wechat.web.controller.domainOrders;

import com.alibaba.fastjson.JSON;
import com.volvo.bff.volvoworks.wechat.common.exception.ServiceException;
import com.volvo.bff.volvoworks.wechat.common.model.dto.order.OrderServiceInfoDeleteDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.order.OrderServiceInfoQueryDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.order.OrderServiceInfoRequestDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.orderService.OrderServiceInfoVO;
import com.volvo.bff.volvoworks.wechat.common.service.order.OrderServiceInfoService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api("/orderServiceInfo/v1")
@Slf4j
@RestController
@RequestMapping("/orderServiceInfo/v1")
public class OrderServiceInfomationController {

    @Autowired
    private OrderServiceInfoService orderServiceInfoService;

    @ApiOperation(value = "保存服务过程信息", notes = "保存服务过程信息", httpMethod = "POST")
    @PostMapping(value = "/saveServiceInfomation")
    public void saveServiceInfomation(@RequestBody OrderServiceInfoRequestDTO dto) {
        log.info("saveServiceInfomation:{}", JSON.toJSONString(dto));
        if(StringUtils.isEmpty(dto.getOwnerCode())
                || StringUtils.isEmpty(dto.getRoNo())
                || CollectionUtils.isEmpty(dto.getImageInfoList())){
            throw new ServiceException("经销商code、工单号、多媒体信息不能为空","经销商code、工单号、多媒体信息不能为空");
        }
        orderServiceInfoService.saveServiceInfomation(dto);
    }

    @ApiOperation(value = "删除服务过程信息", notes = "删除服务过程信息", httpMethod = "POST")
    @PostMapping(value = "/deleteServiceInfomation")
    public void deleteServiceInfomation(@RequestBody OrderServiceInfoDeleteDTO dto) {
        log.info("saveServiceInfomation:{}", JSON.toJSONString(dto));
        if(StringUtils.isEmpty(dto.getOwnerCode())
                || StringUtils.isEmpty(dto.getRoNo())
                || CollectionUtils.isEmpty(dto.getIds())){
            throw new ServiceException("经销商code、工单号、多媒体信息不能为空","经销商code、工单号、多媒体信息不能为空");
        }
        orderServiceInfoService.deleteServiceInfomation(dto);
    }

    @ApiOperation(value = "", notes = "查询服务过程信息", httpMethod = "POST")
    @PostMapping(value = "/getServiceInfomation")
    public ResponseDTO<List<OrderServiceInfoVO>> getServiceInfomation(@RequestBody OrderServiceInfoQueryDTO dto) {
        log.info("saveServiceInfomation:{}", JSON.toJSONString(dto));
        if(StringUtils.isEmpty(dto.getOwnerCode())
                || StringUtils.isEmpty(dto.getRoNo())){
            throw new ServiceException("经销商code、工单号不能为空","经销商code、工单号不能为空");
        }
        List<OrderServiceInfoVO> serviceInfomation = orderServiceInfoService.getServiceInfomation(dto);
        return ResponseUtils.success(serviceInfomation);
    }
}
