package com.volvo.bff.volvoworks.wechat.common.model.vo.workshop;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BookingOrderVo {

    private Integer currentPage;

    private Integer pageSize;

    /**
     * 服务顾问
     */
    private String ad;


    private String license;

    /**
     * 维修类型
     */
    private List<String> repairCategoryCode;

    /**
     * 预约进场时间(开始)
     */
    private String bookingComeTimeBegin;

    /**
     * 预约进场时间(结束)
     */
    private String bookingComeTimeEnd;

    /**
     * 服务顾问
     */
    private String serviceAdvisor;

    /**
     * 预约单状态 标签
     */
    private List<Integer> bookingOrderStatus;

    private String ownerCode;


}
