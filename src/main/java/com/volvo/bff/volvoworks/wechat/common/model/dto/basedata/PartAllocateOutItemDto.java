package com.volvo.bff.volvoworks.wechat.common.model.dto.basedata;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DataImportDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 调拨出库明细
 * TODO description
 *
 * <AUTHOR>
 * @date 2017年5月20日
 */
@Data
public class PartAllocateOutItemDto extends DataImportDto {

    private String dealerCode;
    private String itemId;
    private String allocateOutNo;
    private String storageCode;
    private String storagePositionCode;
    private String unitCode;
    private String partNo;
    private String partName;
    private String receiveRemark;
    private String partBatchNo;

    private Float outQuantity;
    private Double priceRate;
    private Double costPrice;
    private Double costAmount;
    private Double outPrice;
    private Double outAmount;
    private Double otherPartCostPrice;
    private Double otherPartCostAmount;
    private Double dnpPrice;
    private Double msrpPrice;
    private String isShort;
    private String flag;

    /**
     * 溯源件数量-传入-（新增时根据接口写入app是直接查出来的）
     * */
    private BigDecimal sourceInQuantity;

    /**
     * 入库的溯源码
     * */
    private List<String> sourceCodes;
    /*
     * app传入，总数量
     * */
    private BigDecimal practicalQuantity;

}
