package com.volvo.bff.volvoworks.wechat.common.model.vo.applicationMaintenanceentertain;

import lombok.Data;

import java.io.Serializable;

/**
 * tt_vehicle_entrance
 * 
 * <AUTHOR>
 * @version 1.0.0 2023-04-17
 */
@Data
public class CheckVehicleEntranceReqVO implements Serializable {
	
    private static final long serialVersionUID = -8739684651915893874L;
    
    private String id;
    
    /** 进场车辆上报流水id */
    private String entranceId;

    /** 车牌号 */
    private String licensePlate;

    /** 经销商code */
    private String dealerCode;
    
    /** 经销商code */
    private Integer index;
}