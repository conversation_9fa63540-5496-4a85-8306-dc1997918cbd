package com.volvo.bff.volvoworks.wechat.common.service.pds.impl;


import com.volvo.bff.volvoworks.wechat.common.feign.DomainOrdersFeign;
import com.volvo.bff.volvoworks.wechat.common.model.dto.pds.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.pds.PdsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PdsServiceImpl implements PdsService {
	@Autowired
	private DomainOrdersFeign domainOrdersFeign;

	@Override
	public ResponseDTO<ListForAPPrspDTO> listForAPP(ListForAPPrsqDTO dto) {
		return domainOrdersFeign.listForAPP(dto);
	}

	@Override
	public ResponseDTO<AddBeforerspDTO> addBefore(AddBeforersqDTO dto) {
		return domainOrdersFeign.addBefore(dto);
	}

	@Override
	public ResponseDTO<Integer> add(AddRsqDTO dto) {
		return domainOrdersFeign.add(dto);
	}

	@Override
	public ResponseDTO<ItemGroupModeDTO> itemGroupMode(ItemRsqDTO dto) {
		return domainOrdersFeign.itemGroupMode(dto);
	}
}
