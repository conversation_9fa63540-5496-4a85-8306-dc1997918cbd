package com.volvo.bff.volvoworks.wechat.common.service.pad;

import com.volvo.bff.volvoworks.wechat.common.model.vo.pad.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface PadVehiclePreviewService {

    /**
     * 根据vin或license查询结果返回所有
     * <AUTHOR>
     * @date 2019年11月26日
     * @return Map
     */
    PadCustomerRequireResultAllVo queryOwnerVehicleResultAll(@RequestParam("yjNo") String yjNo);

    PadVehiclePreviewResultVo queryOwnerVehicle(@RequestBody PadVehiclePreviewVo padVehiclePreviewVo);

    String checkOwnerVehicleVin(@RequestBody PadVehiclePreviewVo padVehiclePreviewVo);

    String precheckFinshed(String yjNo);

    RepairResultVo savePreviewTransferOrder(String yjNo, String ownerCode, String signImgUrl);
}
