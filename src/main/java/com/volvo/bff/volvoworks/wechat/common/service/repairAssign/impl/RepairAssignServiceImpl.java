package com.volvo.bff.volvoworks.wechat.common.service.repairAssign.impl;

import com.lop.open.api.sdk.internal.fastjson.JSON;
import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.repairAssign.TtRoAssignDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.workshop.BookingOrderDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.AssginqualityInspectionResultVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.BookingOrderVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.RepairAssignWorkShopTraceDTO;
import com.volvo.bff.volvoworks.wechat.common.service.repairAssign.RepairAssignService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class RepairAssignServiceImpl implements RepairAssignService {

    @Resource
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Override
    public PageInfoDto queryRepairAssign(Map<String, String> queryParam) {
        DmsResultDTO<PageInfoDto> response = dmscloudServiceFeign.queryRepairAssign(queryParam);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public List<Map> queryRoLabourByRoNOss(String id) {
        DmsResultDTO<List<Map>> response = dmscloudServiceFeign.queryRoLabourByRoNOss(id);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public List<Map> queryAllRoAssign(String id) {
        DmsResultDTO<List<Map>> response = dmscloudServiceFeign.queryAllRoAssign(id);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public void updateAssign(TtRoAssignDTO dto) {
        DmsResultDTO<Void> response = dmscloudServiceFeign.updateAssign(dto);
        ErrorConversionUtils.errorCodeConversion(response);
    }

    @Override
    public PageInfoDto queryLabour(String id) {
        DmsResultDTO<PageInfoDto> response = dmscloudServiceFeign.queryLabour(id);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public void selfInspectionRegistration(TtRoAssignDTO dto) {
        DmsResultDTO<Void> response = dmscloudServiceFeign.selfInspectionRegistration(dto);
        ErrorConversionUtils.errorCodeConversion(response);
    }

    @Override
    public String workshopTrace(RepairAssignWorkShopTraceDTO assignWorkShopTraceDTO) {
        DmsResultDTO<String> response = dmscloudServiceFeign.workshopTrace(assignWorkShopTraceDTO);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public AssginqualityInspectionResultVo qualityInspection(String roNo) {
        log.info("qualityInspection roNo:{}", roNo);
        DmsResultDTO<AssginqualityInspectionResultVo> response = dmscloudServiceFeign.qualityInspection(roNo);
        log.info("qualityInspection response:{}", JSON.toJSONString(response));
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }
}
