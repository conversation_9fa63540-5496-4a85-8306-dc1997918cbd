package com.volvo.bff.volvoworks.wechat.common.model.vo.pad;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName PadCustomerRequireResultVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/5/25 12:30
 */
@Data
@ApiModel("客户需求总查询返回Vo")
public class PadCustomerRequireResultVo {

    @ApiModelProperty(value = "机构编码",name = "ownerCode")
    private String ownerCode;

    @ApiModelProperty(value = "环检单号",name = "yiNo")
    private String yjNo;

    @ApiModelProperty(value = "车主手机",name = "mobile")
    private String mobile;

    @ApiModelProperty(value = "车主姓名",name = "ownerName")
    private String ownerName;

    @ApiModelProperty(value = "车型",name = "model")
    private String model;

    @ApiModelProperty(value = "车架号",name = "vin")
    private String vin;

    @ApiModelProperty(value = "车牌号",name = "license")
    private String license;

    @ApiModelProperty(value = "送车联系人",name = "deliverer")
    private String deliverer;

    @ApiModelProperty(value = "送车联系人手机",name = "delivererMobile")
    private String delivererMobile;

    @ApiModelProperty(value = "预约单号",name = "bookingOrderNo")
    private String bookingOrderNo;

    @ApiModelProperty(value = "预约来源",name = "bookingSource")
    private String bookingSource;

    @ApiModelProperty(value = "维修类型",name = "maintType")
    private String maintType;

    @ApiModelProperty(value = "常规保养",name = "routeMaint")
    private String routeMaint;

    @ApiModelProperty(value = "更换后刹片",name = "replaceAfter")
    private String replaceAfter;

    @ApiModelProperty(value = "雨刮",name = "wiper")
    private String wiper;

    @ApiModelProperty(value = "其它待定",name = "otherPend")
    private String otherPend;

    @ApiModelProperty(value = "旧件是否带走",name = "oldGoway")
    private String oldGoway;

    @ApiModelProperty(value = "洗车方式",name = "watherCarType")
    private String watherCarType;

    @ApiModelProperty(value = "是否在店内休息区等待",name = "instoreWait")
    private String instoreWait;

    @ApiModelProperty(value = "是否在店内享用午餐",name = "eatLunch")
    private String eatLunch;

    @ApiModelProperty(value = "是否上门送车",name = "isSendCar")
    private String isSendCar;

    @ApiModelProperty(value = "进厂行驶里程",name = "inMileage")
    private Double inMileage;

    @ApiModelProperty(value = "服务顾问",name = "serviceAdvisor")
    private String serviceAdvisor;

    @ApiModelProperty(value = "品牌",name = "brand")
    private String brand;

    @ApiModelProperty(value = "销售日期",name = "salesDate")
    private String salesDate;

    @ApiModelProperty(value = "车主编号",name = "ownerNo")
    private String ownerNo;

    @ApiModelProperty(value = "进厂油料",name = "oilRemain")
    private Integer oilRemain;

    @ApiModelProperty(value = "工单号",name = "roNo")
    private String roNo;

    @ApiModelProperty(value = "指定技师",name = "chiefTechnician")
    private String chiefTechnician;

    @ApiModelProperty(value = "工时单价",name = "labourPrice")
    private BigDecimal labourPrice;

    @ApiModelProperty(value = "车系",name = "series")
    private String series;

    @ApiModelProperty(value = "预估金额",name = "estimateAmount")
    private BigDecimal estimateAmount;

    @ApiModelProperty(value = "故障描述",name = "roTroubleDesc")
    private String roTroubleDesc;

    @ApiModelProperty(value = "顾客描述",name = "customerDesc")
    private String customerDesc;

    @ApiModelProperty(value = "上次进厂日期",name = "lastComeDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date lastComeDate;

    @ApiModelProperty(value = "车龄",name = "lastComeDate")
    private Integer autoAge;

    @ApiModelProperty(value = "预交车时间",name = "endTimeSupposed")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date endTimeSupposed;

    @ApiModelProperty(value = "发动机号",name = "engineNo")
    private String engineNo;

    @ApiModelProperty(value = "电机1",name = "motorNO1")
    private String motorNO1;

    @ApiModelProperty(value = "电机2",name = "motorNO2")
    private String motorNO2;

    @ApiModelProperty(value = "是否三天内到访",name = "isTrace")
    private Integer isTrace;

    @ApiModelProperty(value = "三天内到访时间",name = "traceTime")
    private Integer traceTime;

    @ApiModelProperty(value = "环检单开单时间",name = "preOrderStartTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date preOrderStartTime;

    @ApiModelProperty(value = "环检单状态",name = "preOrderStatus")
    private String preOrderStatus;

    @ApiModelProperty(value = "是否预约",name = "isBook")
    private String isBook;

    @ApiModelProperty(value = "车型名称",name = "modelName")
    private String modelName;

    @ApiModelProperty(value = "卡券ID",name = "oneId")
    private String oneId;

    @ApiModelProperty(value = "入场时间",name = "entryTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date entryTime;

    @ApiModelProperty(value = "进厂时间类型  1:摄像头  2:人工录入",name = "entryTimeType")
    private String entryTimeType;

    @ApiModelProperty(value = "车辆症状",name = "vehicleSymptom")
    private String vehicleSymptom;

    @ApiModelProperty(value = "环检单开单时间str",name = "preOrderStartTimeStr")
    private String preOrderStartTimeStr;

    /**
     * 结算单确认渠道
     */
    @ApiModelProperty(value = "结算单确认渠道",name = "operateChannel")
    private String operateChannel;

    /**
     * 操作用户
     */
    @ApiModelProperty(value = "确认人",name = "userName")
    private String userName;

    /**
     * 用户类型(客户/服务顾问)
     */
    @ApiModelProperty(value = "确认人类型",name = "userType")
    private String userType;

    /**
     * 最终确认时间
     */
    @ApiModelProperty(value = "最终确认时间",name = "endDate")
    private Date endDate;

    @ApiModelProperty(value = "是否电车 是10041001/ 否10041002",name = "isElectric")
    private String isElectric;

    @ApiModelProperty(value = "电量或者油量",name = "number")
    private String number;
}
