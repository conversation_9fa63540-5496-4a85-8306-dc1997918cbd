package com.volvo.bff.volvoworks.wechat.common.model.dto.pds;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@ApiModel("AddBeforersqDTO")
public class AddBeforersqDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "VIN", required = true)
	private String vin;
}