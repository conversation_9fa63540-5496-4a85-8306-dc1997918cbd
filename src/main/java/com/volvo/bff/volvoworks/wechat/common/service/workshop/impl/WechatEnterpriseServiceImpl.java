package com.volvo.bff.volvoworks.wechat.common.service.workshop.impl;

import com.volvo.bff.volvoworks.wechat.common.enums.GroupStatusConvertEnum;
import com.volvo.bff.volvoworks.wechat.common.exception.ServiceException;
import com.volvo.bff.volvoworks.wechat.common.feign.ApplicationAftersalesManagementFeign;
import com.volvo.bff.volvoworks.wechat.common.model.dto.workshop.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.workshop.WechatEnterpriseService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class WechatEnterpriseServiceImpl implements WechatEnterpriseService {

    @Autowired
    private ApplicationAftersalesManagementFeign applicationAftersalesManagementFeign;
    @Override
    public Boolean inviteUserAddGroup(InviteUserDTO inviteUser) {
        DmsResultDTO result = applicationAftersalesManagementFeign.inviteUser(inviteUser);
        ErrorConversionUtils.errorCodeConversion(result);
        return true;
    }

    @Override
    public Integer checkGroup(CheckGroupDTO checkGroupDTO) {
        DmsResultDTO<Integer> result = applicationAftersalesManagementFeign.checkGroup(checkGroupDTO);
        ErrorConversionUtils.errorCodeConversion(result);
        Integer data = result.getData();
        GroupStatusConvertEnum byStatus = GroupStatusConvertEnum.getByStatus(data);
        if(!byStatus.getFlag()) {
            throw new ServiceException(byStatus.getExceptionEnum());
        }

        return byStatus.getStatus();
    }

    @Override
    public QwTokenDTO getNewbieTokenAsync(String code) {
        DmsResultDTO<QwTokenDTO> result = applicationAftersalesManagementFeign.getNewbieTokenAsync(code);
        ErrorConversionUtils.errorCodeConversion(result);
        return result.getData();
    }

    @Override
    public QwJSSDKDTO getQYWeChatAgentJSSDKConfig(String url) {
        DmsResultDTO<QwJSSDKDTO> result = applicationAftersalesManagementFeign.getQYWeChatAgentJSSDKConfig(url);
        ErrorConversionUtils.errorCodeConversion(result);
        return result.getData();
    }
}
