package com.volvo.bff.volvoworks.wechat.common.model.vo.app;


import java.io.Serializable;
import java.util.List;


public class DamagedPartResultDto  implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 部件ID
     */
    private String locId;

    /**
     * 父级部件ID
     */
    private String parLocId;

    /**
     * 是否需要拍照
     */
    private String takePicture;

    /**
     * 部件名称
     */
    private String locName;

    /**
     * 关联级别
     */
    private Integer level;

    /**
     * 是否删除
     */
    private String isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 是否显示红点
     */
    private Integer redPoint;

    /**
     * 子部件树
     */
    private List<DamagedPartResultDto> childTree;

    public String getLocId() {
        return locId;
    }

    public void setLocId(String locId) {
        this.locId = locId;
    }

    public String getParLocId() {
        return parLocId;
    }

    public void setParLocId(String parLocId) {
        this.parLocId = parLocId;
    }

    public String getTakePicture() {
        return takePicture;
    }

    public void setTakePicture(String takePicture) {
        this.takePicture = takePicture;
    }

    public String getLocName() {
        return locName;
    }

    public void setLocName(String locName) {
        this.locName = locName;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(String isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Integer getRedPoint() {
        return redPoint;
    }

    public void setRedPoint(Integer redPoint) {
        this.redPoint = redPoint;
    }

    public List<DamagedPartResultDto> getChildTree() {
        return childTree;
    }

    public void setChildTree(List<DamagedPartResultDto> childTree) {
        this.childTree = childTree;
    }

    public DamagedPartResultDto() {
        super();
    }

    @Override
    public String toString() {
        return "DamagedPartResultDto{" +
                "locId='" + locId + '\'' +
                ", parLocId='" + parLocId + '\'' +
                ", takePicture='" + takePicture + '\'' +
                ", locName='" + locName + '\'' +
                ", level=" + level +
                ", isDeleted='" + isDeleted + '\'' +
                ", isValid=" + isValid +
                ", redPoint=" + redPoint +
                ", childTree=" + childTree +
                '}';
    }

}
