package com.volvo.bff.volvoworks.wechat.common.model.dto.vhc;

import lombok.Data;

import java.util.List;

/**
 * 车辆健康检查详情dto
 */
@Data
public class VhcDetailsDTO {
    //工单号
    private String roNo;
    //车牌
    private String license;
    //检查完成时间
    private String vhcTime;
    //报价完成时间
    private String vhcPriceTime;
    //VIN
    private String vin;
    //检查状态
    private String vhcState;
    //维修类型
    private String repairTypeCode;
    //派工状态
    private String assignState;
    //检查单号
    private String vhcNo;
    //服务顾问
    private String serviceAdvisor;
    //工单开单日期
    private String roCreateDate;
    //指定技师
    private String technician;
    //预计交车时间
    private String endTimeSupposed;
    //车主
    private String ownerName;
    //送修人
    private String deliverer;
    //送修人手机
    private String delivererMobile;
    //车辆类型
    private String vhcType;
    //车型
    private String model;
    //检查单创建时间
    private String createdAt;
    //检查状态各个数量
    private List<VhcStateCountDto> vhcStateCountDtoList;
    //报告推送时间
    private String vhcFeedbackTime;
    //客户反馈时间
    private String pushUserTime;


}
