package com.volvo.bff.volvoworks.wechat.common.model.dto.accidentClues;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("预约单明细结果VO")
public class AppointmentDetailResultVo {

    @ApiModelProperty(value = "车主",name = "ownerName",example = "黄辉")
    private String ownerName;

    @ApiModelProperty(value = "送修人",name = "contactorName",example = "黄辉")
    private String contactorName;

    @ApiModelProperty(value = "联系人电话",name = "contactorPhone",example = "18701888158")
    private String contactorPhone;

    @ApiModelProperty(value = "牌照",name = "license",example = "苏B832NF")
    private String license;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "预约进厂时间",name = "bookingComeTime",example = "2019-09-11 00:00:00")
    private Date bookingComeTime;

    @ApiModelProperty(value = "预约单号",name = "bookingOrderNo",example = "YO1909170008")
    private String bookingOrderNo;

    @ApiModelProperty(value = "vin",name = "vin",example = "1LN6L9S97H5602034")
    private String vin;

    @ApiModelProperty(value = "车型",name = "model",example = "50")
    private String model;

    @ApiModelProperty(value = "里程",name = "inMileage",example = "1000")
    private Double inMileage;

    @ApiModelProperty(value = "服务顾问",name = "serviceAdvisor",example = "306")
    private String serviceAdvisor;

    @ApiModelProperty(value = "维修技师",name = "chiefTechnician",example = "266")
    private String chiefTechnician;

    @ApiModelProperty(value = "客户需求",name = "customerDesc",example = "客户需求")
    private String customerDesc;

    @ApiModelProperty(value = "备注",name = "remark",example = "备注")
    private String remark;

    @ApiModelProperty(value = "预约维修类型",name = "repairTypeCode",example = "预约维修类型")
    private String repairTypeCode;

    @ApiModelProperty(value = "预约来源",name = "repairTypeCode",example = "")
    private String bookingSource;

    @ApiModelProperty(value = "下单日期",name = "createdAt",example = "2023-01-01 00:00:00")
    private String createdAt;

    @ApiModelProperty(value = "预约单状态",name = "bookingOrderStatus")
    private Integer bookingOrderStatus;

}
