package com.volvo.bff.volvoworks.wechat.common.feign.interceptor;


import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import com.volvo.bff.volvoworks.wechat.common.utils.TransmittableThreadLocalUtils;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
@RefreshScope
public class FeignInterceptor implements RequestInterceptor {
	
    @Value("${request.header.forwarding.list:authToken}")
    private String requestHeaderForwardingList;

    @Override
    public void apply(RequestTemplate requestTemplate) {
    	// 请求头转发转发
    	requestForward(requestTemplate);
    	
    }

    /**
     * 请求头 转发
     * @param requestTemplate
     * 
     */
	private void requestForward(RequestTemplate requestTemplate) {
		// 异步情况下通过线程共享实现数据共享
        Map<String, String> headerMap = TransmittableThreadLocalUtils.getValue();
        log.info("headerMap:{}", headerMap);
        String[] split = requestHeaderForwardingList.split(",");
        List<String> asList = Arrays.asList(split);
        if(headerMap!=null) {        	
        	for (Map.Entry<String,String> entry : headerMap.entrySet()) {
        		String key = entry.getKey();
        		String value = entry.getValue();
        		if(asList.contains(key)) {
        			log.info("请求头转发 headerName==> {} =====headerValue==> {}",key ,value);
        			requestTemplate.header(key, value);
        		}
        	}
        }
	}
}
