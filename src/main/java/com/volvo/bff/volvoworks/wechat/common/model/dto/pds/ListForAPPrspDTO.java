package com.volvo.bff.volvoworks.wechat.common.model.dto.pds;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@ApiModel("ListForAPPrspDTO")
public class ListForAPPrspDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	// 汇总数据
	@ApiModelProperty(name = "summary")
	private SummaryDTO summary;
	// 分页数据
	@ApiModelProperty(name = "pageInfo")
	private PageDTO<ListPdsInfoDTO> pageInfo;
}