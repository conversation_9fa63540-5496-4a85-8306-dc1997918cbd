package com.volvo.bff.volvoworks.wechat.common.model.dto.basedata;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DataImportDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 采购入库明细
 * TODO description
 * <AUTHOR>
 * @date 2017年6月1日
 */
@Data
public class PartBuyItemDTO extends DataImportDto {

    private  String storageCode;	//仓库名称
    private String storagePositionCode;	//库位
    private String partNo;	//零件代码
    private String partName;	//零件名称
    private String unitCode;	//单位
    private BigDecimal inQuantity;	//入库数量
    private BigDecimal inPrice; //入库不含税单价
    private BigDecimal inPriceTaxed;	//入库单价
    private BigDecimal inAmountTaxed;	//入库金额
    private Integer isFinished;	//是否入库
    private Integer vvStatus;	//状态
    private String itemId;  //明细ITEM_ID
    private String flag;    //前台记录变更标志N:新增/U:更新
    /**
     * 溯源件数量-传入-（新增时根据接口写入app是直接查出来的）
     * */
    private BigDecimal sourceInQuantity;
    /*
    * app传入，总数量
    * */
    private BigDecimal practicalQuantity;

    private List<String> sourceCodes; //入库的溯源码

    public void setDefaultVal() {
        this.inQuantity= inQuantity==null?new BigDecimal(0):inQuantity;
        this.sourceInQuantity=sourceInQuantity==null?new BigDecimal(0):sourceInQuantity;
    }
}
