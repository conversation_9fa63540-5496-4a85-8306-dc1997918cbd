package com.volvo.bff.volvoworks.wechat.common.model;

import java.util.Collections;
import java.util.List;

import lombok.Data;

@Data
public class PageBean<T> {

    /**
     * 查询数据列表
     */
    private List<T> records = Collections.emptyList();

    /**
     * 总数
     */
    private long total = 0;
    /**
     * 每页显示条数，默认 10
     */
    private long size = 10;

    /**
     * 当前页
     */
    private long current = 1;

    /**
     * 自动优化 COUNT SQL
     */
    private boolean optimizeCountSql = true;
    /**
     * 是否进行 count 查询
     */
    private boolean isSearchCount = true;
    /**
     * 总页数
     */
    private long pages = 1;
}
