package com.volvo.bff.volvoworks.wechat.common.model.dto.order;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("服务信息保存")
@Data
public class OrderServiceInfoRequestDTO {

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号",name = "roNo",required = true)
    private String roNo;

    /**
     * 经销商
     */
    @ApiModelProperty(value = "经销商",name = "ownerCode",required = true)
    private String ownerCode;


    /**
     * 车架号
     */
    @ApiModelProperty(value = "vin",name = "vin")
    private String vin;


    /**
     * 图片或视频UID
     */
    @ApiModelProperty(value = "图片或视频UID",name = "imageInfoList", required = true)
    private List<ImageRequestDTO> imageInfoList;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注",name = "remark")
    private String remark;

    /**
     * 信息来源
     */
    @ApiModelProperty(value = "信息来源",name = "infoSource")
    private String infoSource;
}
