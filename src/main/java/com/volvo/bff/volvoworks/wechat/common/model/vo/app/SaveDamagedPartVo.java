package com.volvo.bff.volvoworks.wechat.common.model.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description 定损维护保存Vo
 * <AUTHOR>
 * @Date 2020-06-05
 */
@Data
@ApiModel("定损维护保存Vo")
public class SaveDamagedPartVo {

    @ApiModelProperty(value = "故障部位id",name = "locId")
    private String locId;

    @ApiModelProperty(value = "故障部位名称",name = "locName")
    private String locName;

    @ApiModelProperty(value = "估价单号",name = "estimateNo")
    private String estimateNo;

    @ApiModelProperty(value = "图片地址集合",name = "pictures")
    private List<String> pictures;

    @ApiModelProperty(value = "备注",name = "remark")
    private String remark;

    @ApiModelProperty(value = "估价时间",name = "time")
    private String time;

    @ApiModelProperty(value = "服务顾问",name = "serviceAdvisor")
    private String serviceAdvisor;

    @ApiModelProperty(value = "是否更换",name = "isReturn")
    private Integer isReturn;
}
