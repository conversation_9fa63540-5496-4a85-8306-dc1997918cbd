package com.volvo.bff.volvoworks.wechat.common.service.stockmanage;

import com.volvo.bff.volvoworks.wechat.common.model.dto.stockmanage.ListPartAllocateInItemDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;

import java.util.List;
import java.util.Map;

public interface PartAllocataAllService {
    PageInfoDto queryAllocateInfoPage(Map<String, String> query);
    List<Map> queryAllocateInfoById(String id);
    void feeSettlement(ListPartAllocateInItemDto listPartAllocateInItemDto);
}
