package com.volvo.bff.volvoworks.wechat.common.model.vo.pad;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName PadQueryOwnerVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/5/27 15:41
 */
@Data
@ApiModel("查询车主信息VO")
public class PadQueryOwnerVo {

    @ApiModelProperty(value = "vin",name = "vin")
    private String vin;

    @ApiModelProperty(value = "车牌号",name = "license")
    private String license;

    @ApiModelProperty(value = "车主",name = "ownerName")
    private String ownerName;

    @ApiModelProperty(value = "车主手机号",name = "phone")
    private String phone;
}
