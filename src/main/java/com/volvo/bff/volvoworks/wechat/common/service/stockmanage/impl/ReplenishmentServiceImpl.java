package com.volvo.bff.volvoworks.wechat.common.service.stockmanage.impl;

import com.lop.open.api.sdk.internal.fastjson.JSON;
import com.volvo.bff.volvoworks.wechat.common.feign.ApplicationMaintainManagementFeign;
import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.dto.part.ListPartBuyItemDto;
import com.volvo.bff.volvoworks.wechat.common.model.dto.part.ShortPartResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.stockmanage.ReplenishmentService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class ReplenishmentServiceImpl implements ReplenishmentService {


    @Resource
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Resource
    private ApplicationMaintainManagementFeign maintainManagementFeign;

    @Override
    public String accountStockBuy(ListPartBuyItemDto listPartBuyItemDTO) {
        log.info("ReplenishmentServiceImpl-accountStockBuy:{},", JSON.toJSONString(listPartBuyItemDTO));
        DmsResultDTO<String> accountStockBuy = dmscloudServiceFeign.accountStockBuy(listPartBuyItemDTO);
        ErrorConversionUtils.errorCodeConversion(accountStockBuy);
        syncPartStatusV2(listPartBuyItemDTO);
        return accountStockBuy.getData();
    }

    private void syncPartStatusV2(ListPartBuyItemDto listPartBuyItemDTO) {
        log.info("syncPartStatusV2：{}", listPartBuyItemDTO.getStockInNo());
        try {
            DmsResultDTO<List<ShortPartResultDTO>> response = maintainManagementFeign.syncPartStatus(listPartBuyItemDTO);
            ErrorConversionUtils.errorCodeConversion(response);
        } catch (Exception e) {
            log.info("回调采购数据失败：stockInNo：{}, ", listPartBuyItemDTO.getStockInNo(), e);
        }
    }
}
