package com.volvo.bff.volvoworks.wechat.common.model.dto.order;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("图片信息")
@Data
public class ImageRequestDTO {

    /**
     * 工单号
     */
    @ApiModelProperty(value = "图片信息",name = "uid",required = true)
    private String uid;

    /**
     * 经销商
     */
    @ApiModelProperty(value = "0图片1视频",name = "type",required = true)
    private Integer type;
    
    /**
     * 服务信息名称
     */
    @ApiModelProperty(value = "服务信息名称",name = "serviceInformationName")
    private String serviceInformationName;

}
