package com.volvo.bff.volvoworks.wechat.common.model.dto.pds;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@ApiModel("ListForAPPrsqDTO")
public class ListForAPPrsqDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	// 当前页码
	@ApiModelProperty(name = "pageNum")
	private Integer pageNum;
	// 每页大小
	@ApiModelProperty(name = "pageSize")
	private Integer pageSize;
	// 创建人技师ID
	@ApiModelProperty(name = "createSaId")
	private Integer createSaId;
	// 创建结束时间
	@ApiModelProperty(name = "endTime")
	private String endTime;
	// pds单号
	@ApiModelProperty(name = "pdsNo")
	private String pdsNo;
	// pds状态
	@ApiModelProperty(name = "pdsStatus")
	private Integer pdsStatus;
	// 创建开始时间
	@ApiModelProperty(name = "startTime")
	private String startTime;
	// 车架号
	@ApiModelProperty(name = "vin")
	private String vin;
}