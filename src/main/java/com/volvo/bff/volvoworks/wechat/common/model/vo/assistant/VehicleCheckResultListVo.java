package com.volvo.bff.volvoworks.wechat.common.model.vo.assistant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("查询所有车辆检查结果VO")
public class VehicleCheckResultListVo {

    @ApiModelProperty(value = "工单状态",name = "roStatus")
    private String roStatus;

    @ApiModelProperty(value = "车主",name = "ownerName")
    private String ownerName;

    @ApiModelProperty(value = "预交车时间",name = "endTimeSupposed")
    private String endTimeSupposed;

    @ApiModelProperty(value = "牌照",name = "license")
    private String license;

    @ApiModelProperty(value = "工单号",name = "roNo")
    private String roNo;

    @ApiModelProperty(value = "预约单号",name = "yjNo")
    private String yjNo;

    @ApiModelProperty(value = "服务顾问",name = "serviceAdvisor")
    private String serviceAdvisor;

    @ApiModelProperty(value = "指定技师",name = "chiefTechnician")
    private String chiefTechnician;

    @ApiModelProperty(value = "是否录入",name = "isInput")
    private String isInput;
}
