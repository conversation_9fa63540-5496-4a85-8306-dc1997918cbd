package com.volvo.bff.volvoworks.wechat.web.controller.pds;

import com.volvo.bff.volvoworks.wechat.common.model.dto.pds.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.pds.PdsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api("pds检查单")
@RestController
@RequestMapping("/pds")
@Slf4j
public class PdsController {
	@Resource
	private PdsService pdsService;

	/**
	 * app 列表查询
	 */
	@ApiOperation(value = "pds检查单app列表查询", httpMethod = "POST")
	@PostMapping("/v1/list")
	public ResponseDTO<ListForAPPrspDTO> listForAPP(@RequestBody ListForAPPrsqDTO dto) {
		return pdsService.listForAPP(dto);
	}

	/**
	 * app pds创建前置接口
	 */
	@ApiOperation(value = "pds检查单-校验是否已经存在pds检查单", httpMethod = "POST")
	@PostMapping("/v1/addBefore")
	public ResponseDTO<AddBeforerspDTO> addBefore(@RequestBody AddBeforersqDTO dto) {
		return pdsService.addBefore(dto);
	}

	/**
	 * app pds创建/编辑
	 */
	@ApiOperation(value = "pds检查单app创建/编辑", httpMethod = "POST")
	@PostMapping("/v1/add")
	public ResponseDTO<Integer> add(@RequestBody AddRsqDTO dto) {
		return pdsService.add(dto);
	}

	/**
	 * app 详细查询-分组模式
	 */
	@ApiOperation(value = "pds检查单app详细查询", httpMethod = "POST")
	@PostMapping("/v1/item")
	public ResponseDTO<ItemGroupModeDTO> itemGroupMode(@RequestBody ItemRsqDTO dto) {
		return pdsService.itemGroupMode(dto);
	}
}