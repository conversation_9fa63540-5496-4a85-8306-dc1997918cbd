package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.pad;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.pad.LogTransparentWorkshopVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.pad.PadQueryOwnerVo;
import com.volvo.bff.volvoworks.wechat.common.service.pad.PadVehicleInfoService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "查询车主车辆信息接口", tags = {"查询车主车辆信息接口"})
@RequestMapping("/padQueryOwnerVehicleApi")
@RestController
public class PadQueryOwnerVehicleController {

    @Autowired
    private PadVehicleInfoService padVehicleInfoService;

    @ApiOperation(value = "探针接口", notes = "探针接口", httpMethod = "POST")
    @PostMapping(value = "/log_transparent_workshop")
    public ResponseDTO<String> logTransparentWorkshop(@RequestBody LogTransparentWorkshopVo logTransparentWorkshopVo) {
        return ResponseUtils.success(padVehicleInfoService.logTransparentWorkshop(logTransparentWorkshopVo));
    }


    @ApiOperation(value = "创建环检单时确定入场时间", notes = "创建环检单时确定入场时间", httpMethod = "POST")
    @PostMapping(value = "/queryVehicleEntryTime")
    public ResponseDTO<String> queryVehicleEntryTime(@RequestBody PadQueryOwnerVo padQueryOwnerVo) {
        return ResponseUtils.success(padVehicleInfoService.queryVehicleEntryTime(padQueryOwnerVo));
    }
}
