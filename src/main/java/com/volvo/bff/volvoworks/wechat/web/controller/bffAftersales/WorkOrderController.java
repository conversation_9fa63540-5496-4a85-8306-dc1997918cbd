package com.volvo.bff.volvoworks.wechat.web.controller.bffAftersales;

import com.volvo.bff.volvoworks.wechat.common.model.dto.vhc.VhcConfirmRepairDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.vhc.VhcQuotedDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.vhc.VhcWorkOrderService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api("/web-workOrder/v1")
@Slf4j
@RestController
@RequestMapping("/web-workOrder/v1")
public class WorkOrderController {

    @Autowired
    private VhcWorkOrderService vhcWorkOrderService;
    @ApiOperation(value = "获取报价单维修项目信息", notes = "", httpMethod = "GET")
    @GetMapping(value = "/queryMaintenanceItems")
    public ResponseDTO<VhcQuotedDTO> queryMaintenanceItems(@RequestParam(value = "vhcNo") String vhcNo) {
        return ResponseUtils.success(vhcWorkOrderService.queryMaintenanceItems(vhcNo));
    }


    @ApiOperation(value = "代客户反向确认维修", notes = "", httpMethod = "POST")
    @PostMapping(value = "/confirmRepair")
    public void confirmRepair(@RequestBody VhcConfirmRepairDTO dto) {
        vhcWorkOrderService.confirmRepair(dto);
    }

    @ApiOperation(value = "推送用户", notes = "", httpMethod = "POST")
    @PostMapping(value = "/pushCustomer")
    public void pushCustomer(@RequestParam(value = "vhcNo") String vhcNo, @RequestParam(value = "roNo") String roNo, @RequestParam(value = "flag") Integer flag, @RequestParam(value = "itemIds", required = false) String itemIds) {
        vhcWorkOrderService.pushCustomer(vhcNo, roNo, flag, itemIds);
    }
}
