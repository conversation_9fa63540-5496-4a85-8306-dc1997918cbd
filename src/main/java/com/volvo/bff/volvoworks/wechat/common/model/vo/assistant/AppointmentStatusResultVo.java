package com.volvo.bff.volvoworks.wechat.common.model.vo.assistant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("查询三种预约单数量结果VO")
public class AppointmentStatusResultVo {

    @ApiModelProperty(value = "未进厂",name = "unEnter",example = "10")
    private String unEnter;

    @ApiModelProperty(value = "已进厂",name = "entered",example = "10")
    private String  entered;

    @ApiModelProperty(value = "已取消",name = "cancelled",example = "10")
    private String cancelled;
}
