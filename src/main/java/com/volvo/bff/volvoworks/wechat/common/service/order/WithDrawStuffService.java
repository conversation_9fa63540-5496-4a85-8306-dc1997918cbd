package com.volvo.bff.volvoworks.wechat.common.service.order;

import com.volvo.bff.volvoworks.wechat.common.model.dto.order.ListAdMaintainDTO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Map;

public interface WithDrawStuffService {

    Map<String, Object> queryMaintainPicking(Map<String,String> queryParam);

    Integer saveMaintainPart(ListAdMaintainDTO listAdMaintainDTO);

    String account(@RequestBody ListAdMaintainDTO listAdMaintainDTO);
}
