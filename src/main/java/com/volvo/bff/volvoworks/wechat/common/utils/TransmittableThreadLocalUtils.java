package com.volvo.bff.volvoworks.wechat.common.utils;

import java.util.HashMap;
import java.util.Map;

import com.alibaba.ttl.TransmittableThreadLocal;

public class TransmittableThreadLocalUtils {
	
    static TransmittableThreadLocal<Map<String, String>> threadLocal = new TransmittableThreadLocal<>();
    
    private TransmittableThreadLocalUtils() {
		super();
	}
    
	//设置线程需要保存的值
    public static void setValue (Map<String, String> map) {
    	Map<String, String> oldMap = TransmittableThreadLocalUtils.threadLocal.get();
    	Map<String,String> newMap = new HashMap<>();
    	if(oldMap!=null) {
    		newMap.putAll(oldMap);
    	}
    	newMap.putAll(map);
    	TransmittableThreadLocalUtils.threadLocal.set(newMap);
    }
    //获取线程中保存的值
    public static Map<String, String> getValue() {
        return TransmittableThreadLocalUtils.threadLocal.get();
    }
    //移除线程中保存的值
    public static void remove() {
    	TransmittableThreadLocalUtils.threadLocal.remove();
    }
    
    
    public static void setMapValue(String key, String value){
    	Map<String,String> newMap = new HashMap<>();
    	newMap.put(key, value);
    	setValue(newMap);
    }
    public static String getValueBykey(String key){
    	Map<String, String> map = getValue();
    	if(map==null) {
    		return null;
    	}
    	return map.get(key);
    }
    public static void removeValueBykey(String key){
    	if(getValueBykey(key)==null) {
    		return;
    	}
    	Map<String, String> value = getValue();
    	value.remove(key);
    	setValue(value);
    }
    
    
}
