package com.volvo.bff.volvoworks.wechat.common.model.dto.order;



import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2017年4月14日
 */
@Data
public class ListAdMaintainDTO {

    private List<TtMaintainTableDTO> maintainPickingTbl;
    private String[] oldPartPrice;
    private String roNo;
    private String license;
    private String roType;
    private String repairTypeCode;
    private String model;
    private String serviceAdvisor;
    private String vin;
    private String ownerName;
    private String ownerCode;
    private Date roCreateDate;
    private String chiefTechnician;
    private Double edtMoney;
    private Double cxAddRate;
    private Double edtFactMoney;
    private Double edtCount;
    private Double cxCurrencyEditDiscount;
    private String sendTime;
    private String updateStatus;
    private String deleteList;//保存在界面删除的记录的itemId

    private String isPickcar;// 取车服务
    private String waitCustomerConfirm;// 客户待确认

    private Double tax; //一进一出 税率
    private String providerCode; //一进一出 供应商代码
    private String providerName; //一进一出 供应商名称
    private String recordShortPart;   //是否记录缺料
    private String recordVersion; //单据变更校验用锁

    private String serviceCode;

    /**
     * 原因选择
     */
    private String reasonType;

    /**
     * 原因
     */
    private String reason;

    /**
     * 出入库类型（APP/PC）
     */
    private String inOutSource;

    /**
     * 异常溯源码信息
     */
    private List<String> abnormalSourceCodes;


    private List<RepairAppendItemQueryiParamsVo> addItemList; //附加项目

    /*京东单号 一用于键出库对应溯源码*/
    private String jdNo;

}

