package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.finance;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.finance.FreeSettlementService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api("/basedata/freeSettlement")
@RestController
@RequestMapping("/basedata/freeSettlement")
public class FreeSettlementController {

    @Autowired
    private FreeSettlementService freeSettlementService;

    @ApiOperation(value = "车辆燃料类型查询", notes = "车辆燃料类型查询", httpMethod = "GET")
    @RequestMapping(value = "/getFuelTypeByVin", method = RequestMethod.GET)
    public ResponseDTO<String> getFuelTypeByVin(@RequestParam("vin") String vin){
        return ResponseUtils.success(freeSettlementService.getFuelTypeByVin(vin));
    }
}
