package com.volvo.bff.volvoworks.wechat.common.service.app.impl;

import com.alibaba.fastjson.JSONObject;
import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.app.StructureCheckService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class StructureCheckServiceImpl implements StructureCheckService {

    @Resource
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Override
    public PageBean<QueryStructureCheckResultVo> queryStructureCheckByCondition(QueryStructureCheckRrquestVo queryStructureCheckRrquestVo) {
        DmsResultDTO<PageBean<QueryStructureCheckResultVo>> res = dmscloudServiceFeign.queryStructureCheckByCondition(queryStructureCheckRrquestVo);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public PageBean<QueryStructureWorkHourResultVo> queryStructureCheckWorkHourByLienceAndRoNO(String license, String roNo) {
        DmsResultDTO<PageBean<QueryStructureWorkHourResultVo>> res = dmscloudServiceFeign.queryStructureCheckWorkHourByLienceAndRoNO(license, roNo);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public JSONObject queryOneStructureCheckWorkHour(Integer itemId, String roNo, String whCode) {
        DmsResultDTO<JSONObject> res = dmscloudServiceFeign.queryOneStructureCheckWorkHour(itemId, roNo,whCode);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public String saveOrUpdateStructureOneWorkHour(StructureWorkHourOneRequestVo structureWorkHourOneRequestVo) {
        DmsResultDTO<String> res = dmscloudServiceFeign.saveOrUpdateStructureOneWorkHour(structureWorkHourOneRequestVo);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }
}
