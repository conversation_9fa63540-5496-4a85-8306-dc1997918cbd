package com.volvo.bff.volvoworks.wechat.common.model.vo.assistant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("新增预检单参数Vo")
public class VehiclePreviewSaveVo {

    @ApiModelProperty(value = "车牌号",name = "license")
    private String license;

    @ApiModelProperty(value = "vin",name = "vin")
    private String vin;

    @ApiModelProperty(value = "车型",name = "model")
    private String model;

    @ApiModelProperty(value = "车主",name = "ownerName")
    private String ownerName;

    @ApiModelProperty(value = "车主手机号",name = "phone")
    private String phone;

    @ApiModelProperty(value = "送修人邮箱",name = "contactorEMail")
    private String contactorEMail;

    @ApiModelProperty(value = "送修人",name = "contactorName")
    private String contactorName;

    @ApiModelProperty(value = "送修人手机号",name = "contactorPhone")
    private String contactorPhone;

    @ApiModelProperty(value = "里程",name = "inMileage")
    private String inMileage;

    @ApiModelProperty(value = "进厂时间",name = "firstInDate")
    private String firstInDate;

    @ApiModelProperty(value = "销售日期",name = "salesDate")
    private String salesDate;

    @ApiModelProperty(value = "地址",name = "address")
    private String address;

    @ApiModelProperty(value = "选择类型:进厂原因",name = "inReason")
    private String inReason;

    @ApiModelProperty(value = "故障描述",name = "remark2")
    private String remark2;

    @ApiModelProperty(value="集合list",name = "bodyAppearanceInfoList")
    private List<BodyAppearanceInfo> bodyAppearanceInfoList;

    @ApiModelProperty(value = "服务顾问",name = "serviceAdvisor")
    private String serviceAdvisor;

    @ApiModelProperty(value = "物品清单集合",name = "contentCodes")
    private List<Integer> contentCodes;


}
