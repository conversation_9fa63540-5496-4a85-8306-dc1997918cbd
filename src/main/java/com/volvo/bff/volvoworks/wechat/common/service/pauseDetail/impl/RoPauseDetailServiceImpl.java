package com.volvo.bff.volvoworks.wechat.common.service.pauseDetail.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.reception.PzusedateilParemVo;
import com.volvo.bff.volvoworks.wechat.common.service.pauseDetail.RoPauseDetailservice;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * <p>
 * 维修派工新需求 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-12
 */
@Service
public class RoPauseDetailServiceImpl implements RoPauseDetailservice {

    @Resource
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Override
    public String paintball(List<PzusedateilParemVo> zaneDetail) {
        DmsResultDTO<String> response = dmscloudServiceFeign.queryZaneDetail(zaneDetail);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public String paintbox(List<PzusedateilParemVo> zaneDetail) {
        DmsResultDTO<String> response = dmscloudServiceFeign.pauseDaelList(zaneDetail);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }
}
