package com.volvo.bff.volvoworks.wechat.common.feign.interceptor;

import static feign.Util.decodeOrDefault;
import static feign.form.util.CharsetUtil.UTF_8;

import java.io.IOException;
import java.util.Collection;
import java.util.Map;

import com.volvo.bff.volvoworks.wechat.common.exception.UtilException;

import feign.Logger;
import feign.Request;
import feign.Response;
import feign.Util;
import lombok.extern.slf4j.Slf4j;

/**
 * 重新日志打印
 * <AUTHOR>
 *
 */
@Slf4j
public class MyFeignLogger extends Logger {
	
    @Override
    protected void logRequest(String configKey, Level logLevel, Request request) {
        byte[] arrBody = request.body();
        String body = arrBody == null ? "" : new String(arrBody);
        log.info("[log request started]\n{} {}\nbody: {}\n{}",
                request.httpMethod(),
                request.url(),
                body,
                combineHeaders(request.headers()));
    }

    @Override
    protected Response logAndRebufferResponse(String configKey,
                                              Level logLevel,
                                              Response response,
                                              long elapsedTime) {
        int status = response.status();

        String content = "";
        if (response.body() != null && !(status == 204 || status == 205)) {
            byte[] bodyData;
            try {
                bodyData = Util.toByteArray(response.body().asInputStream());
            } catch (IOException e) {
                throw new UtilException(e);
            }
            if (bodyData.length > 0) {
                content = decodeOrDefault(bodyData, UTF_8, "Binary data");
            }
            response = response.toBuilder().body(bodyData).build();
        }

        log.info("[log request ended]\ncost time(ms): {} status:{} from {} {}\n{}\nBody:\n{}",
                elapsedTime,
                status,
                response.request().httpMethod(),
                response.request().url(),
                combineHeaders(response.headers()),
                content);

        return response;
    }

    @Override
    protected void log(String configKey, String format, Object... args) {
      // 不需要打印日志
    }

    private static String combineHeaders(Map<String, Collection<String>> headers) {
        StringBuilder sb = new StringBuilder();
        if (headers != null && !headers.isEmpty()) {
            sb.append("Headers:\r\n");
            for (Map.Entry<String, Collection<String>> ob : headers.entrySet()) {
                for (String val : ob.getValue()) {
                    sb.append("  ").append(ob.getKey()).append(": ").append(val).append("\r\n");
                }
            }
        }
        return sb.toString();
    }
}
