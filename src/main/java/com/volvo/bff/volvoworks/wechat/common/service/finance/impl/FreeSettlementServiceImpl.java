package com.volvo.bff.volvoworks.wechat.common.service.finance.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.finance.FreeSettlementService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class FreeSettlementServiceImpl implements FreeSettlementService {
    @Resource
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Override
    public String getFuelTypeByVin(String vin) {
        DmsResultDTO<String> response = dmscloudServiceFeign.getFuelTypeByVin(vin);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }
}
