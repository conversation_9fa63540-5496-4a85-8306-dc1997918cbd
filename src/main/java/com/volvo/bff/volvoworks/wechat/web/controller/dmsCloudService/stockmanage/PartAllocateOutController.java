package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.stockmanage;

import com.volvo.bff.volvoworks.wechat.common.model.dto.basedata.ListPartAllocateOutItemDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.stockmanage.PartAllocateOutService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.Map;

@Api(value = "/stockmanage/partallocateouts", tags = {"调拨出库"})
@RestController
@RequestMapping("/stockmanage/partallocateouts")
public class PartAllocateOutController {

    @Autowired
    private PartAllocateOutService partAllocateOutService;
    @ApiOperation(value = "配件调拨出库/调拨退回单", notes = "根据必要条件调拨出库单号(要放在map中)查询调拨出库单/调拨退回单", httpMethod = "GET")
    @RequestMapping(method= RequestMethod.GET)
    @ResponseBody
    public ResponseDTO<PageInfoDto> searchPartAllocateOut(@RequestParam Map<String, String> param){
        return ResponseUtils.success(partAllocateOutService.search(param));
    }

    @ApiOperation(value = "配件调拨出库/退回,通过主表 ALLOCATE_OUT_NO 查询调拨出库/调拨退回单明细表数据", notes = "根据必要条件调拨出库单号查询出库/调拨退回单明细表数据", httpMethod = "GET")
    @RequestMapping(value = "/Items/{ALLOCATE_OUT_NO}",method = RequestMethod.GET)
    @ResponseBody
    public ResponseDTO<List<Map>> getPartAllocateOutItems(@PathVariable(value = "ALLOCATE_OUT_NO") String allocateOutNo, @RequestParam Map<String, String> queryParam) {
        List<Map> pageInfoDto = partAllocateOutService.getPartAllocateOutItems(allocateOutNo,queryParam);
        return ResponseUtils.success(pageInfoDto);
    }

    @ApiOperation(value = "APP配件调拨出库", notes = "APP配件调拨出库", httpMethod = "POST")
    @RequestMapping(value="/appPartAllotOut",method = RequestMethod.POST)
    @ResponseBody
    public void appPartAllotOut( @RequestBody ListPartAllocateOutItemDto listPartAllocateOutItemDto) {
        partAllocateOutService.appPartAllotOut(listPartAllocateOutItemDto);
    }


    @ApiOperation(value = "配件调拨出库/调拨退回", notes = "保存（调拨出库/调拨退回）/审核调拨单信息", httpMethod = "POST")
    @RequestMapping(value="/save",method = RequestMethod.POST)
    @ResponseBody
    public ResponseDTO<String>   savePartAllocateIn(@RequestBody ListPartAllocateOutItemDto listPartAllocateOutItemDto, UriComponentsBuilder uriCB)throws Exception{
        return ResponseUtils.success(partAllocateOutService.savePartAllocateIn(listPartAllocateOutItemDto));
    }

    @ApiOperation(value = "配件调拨出库/调拨退回提交（出库）", notes = "根据调拨出库单退回单出库入账", httpMethod = "POST")
    @RequestMapping(value="/enterRecord",method = RequestMethod.POST)
    @ResponseBody
    public void accountPartAllocateIn(@RequestBody ListPartAllocateOutItemDto listPartAllocateOutItemDto){
        partAllocateOutService.accountPartAllocateOut(listPartAllocateOutItemDto);
    }
}
