package com.volvo.bff.volvoworks.wechat.common.model.vo.app;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("vida工单维修零件Vo")
public class VidaRepairPartVo {

    @ApiModelProperty(value = "类型",name = "type")
    private Integer type;
    @ApiModelProperty(value = "项目编号",name = "itemNo")
    private String itemNo;
    @ApiModelProperty(value = "项目描述/说明",name = "itemDescribe")
    private String itemDescribe;
    @ApiModelProperty(value = "数量",name = "quantity")
    private Integer quantity;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getItemNo() {
        return itemNo;
    }

    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    public String getItemDescribe() {
        return itemDescribe;
    }

    public void setItemDescribe(String itemDescribe) {
        this.itemDescribe = itemDescribe;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }
}
