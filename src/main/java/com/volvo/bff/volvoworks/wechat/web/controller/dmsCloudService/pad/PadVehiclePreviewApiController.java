package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.pad;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.pad.*;
import com.volvo.bff.volvoworks.wechat.common.service.pad.PadVehiclePreviewService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(value = "环检单查询车主信息接口", tags = {"环检单查询车主信息接口"})
@RequestMapping("/padVehiclePreviewApi")
@RestController
public class PadVehiclePreviewApiController {

    @Autowired
    private PadVehiclePreviewService padVehiclePreviewService;

    @ApiOperation(value = "根据环检单查询返回所有", notes = "根据环检单查询返回所有", httpMethod = "GET")
    @GetMapping(value = "/queryOwnerVehicleResultAll")
    public ResponseDTO<PadCustomerRequireResultAllVo> queryOwnerVehicleResultAll(@RequestParam("yjNo") String yjNo) {
        return ResponseUtils.success(padVehiclePreviewService.queryOwnerVehicleResultAll(yjNo));
    }

    @ApiOperation(value = "车主信息查询", notes = "车主信息查询", httpMethod = "POST")
    @PostMapping(value = "/queryOwnerVehicle")
    public ResponseDTO<PadVehiclePreviewResultVo> queryOwnerVehicle(@RequestBody PadVehiclePreviewVo padVehiclePreviewVo) {
        return ResponseUtils.success(padVehiclePreviewService.queryOwnerVehicle(padVehiclePreviewVo));
    }

    @ApiOperation(value = "车主信息查询", notes = "车主信息查询", httpMethod = "POST")
    @PostMapping(value = "/checkOwnerVehicleVin")
    public ResponseDTO<String> checkOwnerVehicleVin(@RequestBody PadVehiclePreviewVo padVehiclePreviewVo) {
       return ResponseUtils.success(padVehiclePreviewService.checkOwnerVehicleVin(padVehiclePreviewVo));
    }

    @ApiOperation(value = "环检单完成按钮", notes = "环检单完成按钮", httpMethod = "GET")
    @GetMapping(value = "/precheckFinshed")
    public ResponseDTO<String> precheckFinshed(@RequestParam("yjNo") String yjNo) {
        return ResponseUtils.success(padVehiclePreviewService.precheckFinshed(yjNo));
    }

    @ApiOperation(value = "环检单-转工单", notes = "环检单-转工单", httpMethod = "GET")
    @GetMapping(value = "/savePreviewTransferOrder")
    public ResponseDTO<RepairResultVo> savePreviewTransferOrder(@RequestParam("yjNo") String yjNo,
                                                   @RequestParam(value = "ownerCode",required = false) String ownerCode,
                                                   @RequestParam(value = "signImgUrl",required = false) String signImgUrl) {
        return ResponseUtils.success(padVehiclePreviewService.savePreviewTransferOrder(yjNo,ownerCode,signImgUrl));
    }
}
