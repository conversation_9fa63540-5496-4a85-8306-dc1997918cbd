package com.volvo.bff.volvoworks.wechat.common.model.dto.accidentClues;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@ApiModel(value="VirtualPhoneDTO",description="获取虚拟手机号")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VirtualPhoneDTO implements Serializable {

  private static final long serialVersionUID = 1L;

  @ApiModelProperty(value = "线索ID",name="acId")
  @NotBlank(message = "线索id不能为空")
  private Long acId;

}
