package com.volvo.bff.volvoworks.wechat.common.service.assistant.impl;


import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.accidentClues.AppointmentDetailResultVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.QueryRepairingOrderResultVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.AllAppointmentResultListVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.assistant.AppointmentCheckService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class AppointmentCheckServiceImpl implements AppointmentCheckService {


    @Resource
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Override
    public PageBean<AllAppointmentResultListVo> queryAllAppointment(AppointmentQueryParamsVo appointmentQueryParamsVo) {
        DmsResultDTO<PageBean<AllAppointmentResultListVo>> res = dmscloudServiceFeign.queryAllAppointment(appointmentQueryParamsVo);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public AppointmentStatusResultVo queryStatusNum(AppointmentNumQueryParamsVo appointmentNumQueryParamsVo) {
        DmsResultDTO<AppointmentStatusResultVo> res = dmscloudServiceFeign.queryStatusNum(appointmentNumQueryParamsVo);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public AppointmentDetailResultVo queryAppointmentDetail(String bookingOrderNo) {
        DmsResultDTO<AppointmentDetailResultVo> res = dmscloudServiceFeign.queryAppointmentDetail(bookingOrderNo);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public void deleteAppointment(String bookingOrderNo) {
        DmsResultDTO<Void> res = dmscloudServiceFeign.deleteAppointment(bookingOrderNo);
        ErrorConversionUtils.errorCodeConversion(res);
    }

    @Override
    public void updateAppointmentDate(String bookingOrderNo, String bookingComeTime) {
        DmsResultDTO<Void> res = dmscloudServiceFeign.updateAppointmentDate(bookingOrderNo,bookingComeTime);
        ErrorConversionUtils.errorCodeConversion(res);
    }
}
