package com.volvo.bff.volvoworks.wechat.common.model.vo.assistant;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("车辆检查保存参数Vo")
public class VehicleCheckSaveVo {

    @ApiModelProperty(value = "预检单号",name = "yjNo")
    private String yjNo;

    @ApiModelProperty(value = "工单号",name = "roNo")
    private String roNo;

    @ApiModelProperty(value="集合list",name = "bodyAppearanceInfoList")
    private List<BodyAppearanceInfo> bodyAppearanceInfoList;
}
