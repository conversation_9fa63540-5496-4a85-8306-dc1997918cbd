package com.volvo.bff.volvoworks.wechat.common.model.vo.app;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName QueryStructureCheckResultVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/6/2 10:40
 */
@Data
@ApiModel("结构件在修检查结果集VO")
public class QueryStructureCheckResultVo {

    @ApiModelProperty(value = "工单号",name = "roNo")
    private String roNo;

    @ApiModelProperty(value = "服务专员",name = "serviceAdvisor")
    private String serviceAdvisor;

    @ApiModelProperty(value = "指定技师",name = "chiefTechnician")
    private String chiefTechnician;

    @ApiModelProperty(value = "车主",name = "ownerName")
    private String ownerName;

    @ApiModelProperty(value = "车牌号",name = "license")
    private String license;

    @ApiModelProperty(value = "车型",name = "model")
    private String model;

    @ApiModelProperty(value = "预交车时间",name = "endTimeSupposed")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endTimeSupposed;

    @ApiModelProperty(value = "工单状态",name = "roStatus")
    private Long roStatus;

}
