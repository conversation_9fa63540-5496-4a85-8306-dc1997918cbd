package com.volvo.bff.volvoworks.wechat.common.model.vo.app;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName QueryStructureCheckRrquestVo
 * <AUTHOR>
 * @Date 2020/6/2 16:32
 */
@Data
@ApiModel("QueryStructureCheckRrquestV2Vo")
public class QueryStructureCheckRrquestV2Vo {

    @ApiModelProperty(value = "车主",name = "ownerName")
    private String ownerName;

    @ApiModelProperty(value = "车牌号",name = "license")
    private String license;

    @ApiModelProperty(value = "服务专员",name = "serviceAdvisor")
    private String serviceAdvisor;

    @ApiModelProperty(value = "工单号",name = "roNo")
    private String roNo;

    @ApiModelProperty(value = "工单开单时间",name = "roCreateDate", required = true)
    private String roCreateDate;

    @ApiModelProperty(value = "工单结束时间",name = "roEndDate", required = true)
    private String roEndDate;

    @ApiModelProperty(value = "是否质检",name = "isSeasonCheck")
    private String isSeasonCheck;

    @ApiModelProperty(value = "是否派工",name = "assignTag")
    private Integer assignTag;

    @ApiModelProperty(value = "当前登录人",name = "chiefTechnician")
    private String chiefTechnician;

    @ApiModelProperty(value = "工单状态",name = "roStatus")
    private String roStatus;

    @ApiModelProperty(value = "search",name = "search")
    private String search;
    
    @ApiModelProperty(value = "当前页",name = "limit", dataType = "java.lang.String")
    private Long limit;
    
    @ApiModelProperty(value = "每页大小",name = "pageNum")
    private Long pageNum;
    
    @ApiModelProperty(value = "工单状态集",name = "roStatusList")
    private List<String> roStatusList;
}
