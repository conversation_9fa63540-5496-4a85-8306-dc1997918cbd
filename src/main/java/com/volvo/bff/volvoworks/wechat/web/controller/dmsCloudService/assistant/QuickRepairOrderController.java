package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.assistant;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.QuickRepairOrderQueryParamsVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.QuickRepairOrderQueryResultVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.RepairOrderProgressQueryDetailResultVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.SubmitCarStatusResultVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.assistant.QuickRepairOrderService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "/assistant/repair/order", tags = {"服务助手-快速工单"})
@RestController
@RequestMapping("/assistant/repair/order")
public class QuickRepairOrderController {

    @Autowired
    QuickRepairOrderService repairOrderService;

    /**
     * 查询所有满足条件的工单
     * <AUTHOR>
     * @param queryParamsVo 查询参数
     * @return List<QuickRepairOrderQueryResultVo>
     */
    @ApiOperation(value = "查询所有工单", notes = "获取系统中工单信息", httpMethod = "GET")
    @GetMapping("/findAll")
    public ResponseDTO<PageBean<QuickRepairOrderQueryResultVo>> findAllRepairOrder(QuickRepairOrderQueryParamsVo queryParamsVo){
        return ResponseUtils.success(repairOrderService.findAllRepairOrderForAssistant(queryParamsVo));
    }

    /**
     * 查询工单交车状态相关数据
     * @param paramsVo
     * @return SubmitCarStatusResultVo
     */
    @ApiOperation(value = "查询工单交车状态数量数据", notes = "获取工单交车状态数据", httpMethod = "GET")
    @GetMapping("/findSubmitCarStatus")
    public ResponseDTO<SubmitCarStatusResultVo> findSubmitCarStatus(QuickRepairOrderQueryParamsVo paramsVo){
        return ResponseUtils.success(repairOrderService.findSubmitCarStatus(paramsVo));
    }

    @ApiOperation(value = "根据工单号查询工单明细", notes = "根据工单号查询工单明细", httpMethod = "GET")
    @GetMapping("/findRepairOrderInfoByRoNo/{orderNum}")
    public ResponseDTO<RepairOrderProgressQueryDetailResultVo> findRepairOrderInfoByRoNo(@PathVariable("orderNum") String roNo){
        RepairOrderProgressQueryDetailResultVo result = repairOrderService.findOrderInfoByNum(roNo);
        return ResponseUtils.success(result);
    }
}
