package com.volvo.bff.volvoworks.wechat.common.model.vo.workshop;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReminderShortPartItemVo {

    private String ownerCode;

    private String license;

    private String partNo;

    private Integer closeStatus;

    private Integer shortType;

    /**
     * 仓库代码
     */
    private String storageCode;

    /**
     *  年月日
     */
    private String bookBeginDate;

    /**
     *  年月日
     */
    private String bookEndDate;

    private Integer currentPage;

    private Integer pageSize;

    private List<Long> purchaseOrderDetailIds;

    private String sheetNo;

    /**
     * 缺件状态 （8118 已签收，部分签收，）
     */
    private Long missingPartsStatus;

    /**
     * 服务顾问
     */
    private String ad;

    /**
     * 开单日期
     */
    private String roCreateDate;

    /**
     * 缺料记录ID
     */
    private Integer shortId;

    private String roCreateDateBegin;

    private String roCreateDateEnd;

    /**
     * 状态区分部分到货 未到货，已到货。（'delivered', 'undelivered', 或 'partiallyDelivered'）
     */
    private String missingPartsType;

    /**
     * 缺件多选
     */
    private List<Long> missingPartsStatusList;
}
