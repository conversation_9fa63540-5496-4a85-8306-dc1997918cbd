package com.volvo.bff.volvoworks.wechat.common.service.assistant.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.assistant.VehiclePreviewService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class VehiclePreviewServiceImpl implements VehiclePreviewService {

    @Resource
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Override
    public PageBean<VehiclePreviewResultVo> queryAllPreview(VehiclePreviewQueryParamsVo vehiclePreviewQueryParamsVo) {
        DmsResultDTO<PageBean<VehiclePreviewResultVo>> res = dmscloudServiceFeign.queryAllPreview(vehiclePreviewQueryParamsVo);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public List<CustomerInfoResultVo> queryCusInfoByLicense(String flag, String license) {
        DmsResultDTO<List<CustomerInfoResultVo>> res = dmscloudServiceFeign.queryCusInfoByLicense(flag, license);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public String savePreview(VehiclePreviewSaveVo vehiclePreviewSaveVo) {
        DmsResultDTO<String> res = dmscloudServiceFeign.savePreview(vehiclePreviewSaveVo);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public VehiclePreviewDetailResultVo queryPreviewDetail(String yjNo) {
        DmsResultDTO<VehiclePreviewDetailResultVo> res = dmscloudServiceFeign.queryPreviewDetail(yjNo);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public void deletePreview(String yjNo) {
        DmsResultDTO<Void> res = dmscloudServiceFeign.deletePreview(yjNo);
        ErrorConversionUtils.errorCodeConversion(res);
    }
}
