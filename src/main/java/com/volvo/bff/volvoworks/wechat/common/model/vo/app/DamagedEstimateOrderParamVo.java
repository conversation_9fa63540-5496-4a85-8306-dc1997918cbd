package com.volvo.bff.volvoworks.wechat.common.model.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 定损维护查询估价单vo
 * <AUTHOR>
 */
@Data
@ApiModel("定损维护查询估价单vo")
public class DamagedEstimateOrderParamVo {

    @ApiModelProperty(value = "所有者代码",name = "ownerCode")
    private String ownerCode;
    @ApiModelProperty(value = "车牌号",name = "license")
    private String license;
    @ApiModelProperty(value = "服务顾问",name = "serviceAdvisor")
    private String serviceAdvisor;
    @ApiModelProperty(value = "车主姓名",name = "ownerName")
    private String ownerName;
    @ApiModelProperty(value = "估价单号",name = "estimateNo")
    private String estimateNo;
    @ApiModelProperty(value = "估价时间开始",name = "roCreateDateBegin")
    private String roCreateDateBegin;
    @ApiModelProperty(value = "估价时间结束",name = "roCreateDateEnd")
    private String roCreateDateEnd;


}
