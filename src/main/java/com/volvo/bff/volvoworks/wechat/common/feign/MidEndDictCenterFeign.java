package com.volvo.bff.volvoworks.wechat.common.feign;


import com.volvo.bff.volvoworks.wechat.common.model.dto.city.MidResponse;
import com.volvo.bff.volvoworks.wechat.common.model.dto.city.RegionAllDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

@FeignClient(name = "mid-end-dict-center",
        url = "${mse-in.mid.mid-end-dict-center.url}",
        path = "${mse-in.mid.mid-end-dict-center.path}")
public interface MidEndDictCenterFeign {

    /**
     * 获取全量省市区信息
     */
    @GetMapping(path = "/region/city", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<List<RegionAllDto>> getProvinceAndCity();

    /**
     * 根据id获取省信息
     */
    @GetMapping(path = "/region/queryByRegionId/{id}", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<List<RegionAllDto>> getProvinceById(@PathVariable("id") Integer id);

}
