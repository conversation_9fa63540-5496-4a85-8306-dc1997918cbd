package com.volvo.bff.volvoworks.wechat.common.model.vo.assistant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 交车状态查询结果VO
 * <AUTHOR>
 * @since 2020-03-24
 */
@Data
@ApiModel("车辆检查明细结果VO")
public class VehicleCheckDetailResultVo {

    @ApiModelProperty(value = "标识code",name = "contentCode")
    private String contentCode;

    @ApiModelProperty(value = "状态码",name = "statusCode")
    private String statusCode;

    @ApiModelProperty(value = "图片地址",name = "fileBaseUrl")
    private String fileBaseUrl;

    @ApiModelProperty(value = "视频地址",name = "videoUrl")
    private String videoUrl;

    @ApiModelProperty(value = "备注",name = "remark")
    private String remark;

}
