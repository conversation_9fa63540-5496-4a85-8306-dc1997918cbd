package com.volvo.bff.volvoworks.wechat.common.model.dto.workshop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023/10/18
 */
@Data
@ApiModel("拉人进群")
public class InviteUserDTO {
    @ApiModelProperty("用户ID")
    private Integer userId;
    @ApiModelProperty("工单号")
    @NotBlank(message = "工单号不能为空！")
    private String roNo;
    @ApiModelProperty("IM交互动作")
    private String tag;
    @NotBlank(message = "群聊id不能为空！")
    @ApiModelProperty("群ID")
    private String groupId;
    @ApiModelProperty("经销商")
    private String ownerCode;
}
