package com.volvo.bff.volvoworks.wechat.common.service.workshop;


import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.workshop.BookingOrderDto;
import com.volvo.bff.volvoworks.wechat.common.model.dto.workshop.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.*;
import org.springframework.web.bind.annotation.RequestBody;

public interface TransparentWorkshopManageService {


    PageBean<BookingOrderDto> queryBookingWeCom(BookingOrderVo bookingOrderVo);


    BookingStatusDto queryBookingStatus(BookingOrderVo bookingOrderVo);

    PageBean<VehicleEntranceParamsVO> queryVehicleEntranceList(VehicleEntranceParamsVO vehicleEntranceVO);

    VehicleEntranceCountDto queryAllocatedCount(@RequestBody VehicleEntranceParamsVO vehicleEntranceVO);

    /**
     * 查询未到货，已到货，部分到货数量
     *
     * @return 返回对象（包含数量）
     */
    MissingPartsStatusDto getShortPartStatus(ReminderShortPartItemVo shortPartItemVo);

    /**
     * 根据缺料主键查询 明细
     *
     * @param shortPartItemVo 明细数据
     * @return 明细数据
     */
    ReminderShortPartItemDto queryShortPartItem(ReminderShortPartItemVo shortPartItemVo);

    /**
     * 记录通话记录
     * @param workshopCallRecordDto 通话记录详情
     * @return true false
     */
    Boolean addCallLog(WorkshopCallRecordDto workshopCallRecordDto);

    /**
     * 查询通话记录
     * @param ownerCode 经销商
     * @param roNo 工单号
     * @param serviceAdvisor 服务顾问
     * @return 通话记录
     */
    PageBean<WorkshopCallRecordDto> queryCallItem(String ownerCode, String roNo, String serviceAdvisor, Integer pageNum, Integer pageSize);

    /**
     * 服务看板缺件查询（企微店端）
     */
    PageBean<ShortPartItemWeComDto> queryShortPartWeCom(ReminderShortPartItemVo shortPartItemVo);

    /**
     * 修改预计交车时间
     * @param roNo 工单号
     * @param endTimeSupposed 预交车时间
     */
    void updateRepairOrderStatus(String roNo, String endTimeSupposed);

}
