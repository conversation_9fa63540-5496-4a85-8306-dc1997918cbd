package com.volvo.bff.volvoworks.wechat.common.model.dto.basedata;



import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@SuppressWarnings("rawtypes")
@ApiModel(value = "ReportPayOffDTO", description = "配件报溢DTO")
public class ReportPayOffDTO {
    public String getPartProfitId() {
        return partProfitId;
    }

    public void setPartProfitId(String partProfitId) {
        this.partProfitId = partProfitId;
    }

    //配件报溢ID
    @ApiModelProperty(value = "配件报溢ID", name = "配件报溢ID", required = true)
    private String partProfitId;
    @ApiModelProperty(value = "经手人", name = "经手人", required = false)
    private String handler;// 经手人

    @ApiModelProperty(value = "报溢日期", name = "报溢日期", required = false)
    private Date orderDate;// 报溢日期
    @ApiModelProperty(value = "表格数据", name = "表格数据", required = true)
    private List<Map> partProfitItemList;// 表格数据
    @ApiModelProperty(value = "盘点单号", name = "盘点单号", required = true)
    private String inventoryNo;// 盘点单号
    @ApiModelProperty(value = "报溢单号", name = "报溢单号", required = true)
    private String profitNo;// 报溢单号
    
    @ApiModelProperty(value = "报溢报损是否需要车厂审核", name = "itemCode", required = true)
    private String itemCode;// 报溢报损是否需要车厂审核
    @ApiModelProperty(value = "录入方式", name = "operType", required = true)
    private String operType;// 录入方式
    @ApiModelProperty(value = "录入原因", name = "operReason")
    private String operReason;// 录入原因

    public String getProfitNo() {
        return profitNo;
    }

    public String getItemCode() {
		return itemCode;
	}

	public void setItemCode(String itemCode) {
		this.itemCode = itemCode;
	}

	public void setProfitNo(String profitNo) {
        this.profitNo = profitNo;
    }

    public String getHandler() {
        return handler;
    }

    public void setHandler(String handler) {
        this.handler = handler;
    }

    public Date getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Date orderDate) {
        this.orderDate = orderDate;
    }

    public List<Map> getPartProfitItemList() {
        return partProfitItemList;
    }

    public void setPartProfitItemList(List<Map> partProfitItemList) {
        this.partProfitItemList = partProfitItemList;
    }

    public String getInventoryNo() {
        return inventoryNo;
    }

    public void setInventoryNo(String inventoryNo) {
        this.inventoryNo = inventoryNo;
    }

    public String getOperType() {
        return operType;
    }

    public void setOperType(String operType) {
        this.operType = operType;
    }

    public String getOperReason() {
        return operReason;
    }

    public void setOperReason(String operReason) {
        this.operReason = operReason;
    }
}
