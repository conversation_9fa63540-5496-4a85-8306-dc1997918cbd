package com.volvo.bff.volvoworks.wechat.common.service.stockmanage;

import com.volvo.bff.volvoworks.wechat.common.model.dto.basedata.ListPartAllocateOutItemDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.Map;

public interface PartAllocateOutService {

    PageInfoDto search(Map<String, String> param);

    List<Map> getPartAllocateOutItems(String allocateOutNo, Map<String, String> queryParam);

    void appPartAllotOut(ListPartAllocateOutItemDto listPartAllocateOutItemDto);

    String savePartAllocateIn(ListPartAllocateOutItemDto listPartAllocateOutItemDto);

    void accountPartAllocateOut(ListPartAllocateOutItemDto listPartAllocateOutItemDto);
}
