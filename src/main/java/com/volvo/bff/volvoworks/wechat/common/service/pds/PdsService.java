package com.volvo.bff.volvoworks.wechat.common.service.pds;

import com.volvo.bff.volvoworks.wechat.common.model.dto.pds.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;

public interface PdsService {
	ResponseDTO<ListForAPPrspDTO> listForAPP(ListForAPPrsqDTO dto);

	ResponseDTO<AddBeforerspDTO> addBefore(AddBeforersqDTO dto);

	ResponseDTO<Integer> add(AddRsqDTO dto);

	ResponseDTO<ItemGroupModeDTO> itemGroupMode(ItemRsqDTO dto);
}
