package com.volvo.bff.volvoworks.wechat.common.model.vo.bookingregister;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 交修项目Vo
 * <AUTHOR>
 * @since 2020-04-07
 */
@Data
@ApiModel("交修项目Vo")
public class HandRepairProjectParamsVo {

    @ApiModelProperty(name = "rowKey",value = "rowKey")
    private String rowKey;

    @ApiModelProperty(name = "交修项目itemId",value = "itemId")
    private Integer itemId;

    @ApiModelProperty(name = "Job_No",value = "Job_No")
    private String jobNo;

    @ApiModelProperty(name = "交修项目代码",value = "handRepairProjectCode")
    private String handRepairProjectCode;

    @ApiModelProperty(name = "交修项目名称",value = "handRepairProjectName")
    private String handRepairProjectName;

    @ApiModelProperty(name = "维修类型代码",value = "repairTypeCode")
    private String repairTypeCode;

    @ApiModelProperty(name = "收费区分",value = "chargePartitionCode")
    private String chargePartitionCode;

    @ApiModelProperty(name = "帐类",value = "accountsType")
    private String accountsType;

    @ApiModelProperty(name = "折扣率",value = "discount")
    private String discount;

    @ApiModelProperty(name = "csc_code",value = "cscCode")
    private String cscCode;

    @ApiModelProperty(name = "csc_content",value = "cscContent")
    private String cscContent;

    @ApiModelProperty(name = "csc_class4",value = "cscClass4")
    private String cscClass4;

    @ApiModelProperty(name = "qb_phone",value = "qbPhone")
    private String qbPhone;

    @ApiModelProperty(name = "VIDA单号",value = "vidaNo")
    private String vidaNo;

    @ApiModelProperty(name = "VIDA单号",value = "orderNo")
    private String orderNo;

    @ApiModelProperty(name = "vida主键",value = "vidaId")
    private String vidaId;

    @ApiModelProperty(name = "原vidaJobNo",value = "vidaJobNo")
    private String vidaJobNo;

    @ApiModelProperty(name = "预约单号",value = "bookingOrderNo")
    private String bookingOrderNo;

    @ApiModelProperty(name = "服务套餐代码",value = "setCode")
    private String setCode;

    @ApiModelProperty(value = "添加时间",name = "createdAt")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(name = "适用维修类型",value = "jobType")
    private String jobType;

    @ApiModelProperty(name = "适用账类",value = "accountGroup")
    private String accountGroup;

    @ApiModelProperty(value = "是否延保",name = "IsExtendInsurance")
    private String IsExtendInsurance ;

    @ApiModelProperty(value = "是否服务合同购买工单",name = "IsServeContractBuyOrder")
    private Integer IsServeContractBuyOrder;

    @ApiModelProperty(value = "是否保养活动工单",name = "IsUpkeepActivityOrder")
    private Integer IsUpkeepActivityOrder;

    @ApiModelProperty(value = "次序",name = "mealOrder")
    private String mealOrder;

    @ApiModelProperty(value = "购买ID",name = "careBuyedId")
    private Integer careBuyedId;

    @ApiModelProperty(value = "乐观锁",name = "recordVersion")
    private String recordVersion;

    @ApiModelProperty(value = "服务合同编号",name = "serveContractCode")
    private String activityNo;

    @ApiModelProperty(value = "服务合同名称",name = "serveContractName")
    private String activityName;

    @ApiModelProperty(value = "保养类型",name = "upkeepType")
    private String upkeepType;

    @ApiModelProperty(value = "机油级别",name = "enginOilRank")
    private String enginOilRank;

    @ApiModelProperty(value = "保养次数=》保养活动可享用次数",name = "activityAvailableTimes")
    private Integer activityAvailableTimes;

    @ApiModelProperty(value = "该车使用权益的车龄上限",name = "carAgeMax")
    private Integer carAgeMax;

    @ApiModelProperty(value = "该车使用权益的里程上限",name = "mileageMax")
    private Integer mileageMax;

    @ApiModelProperty(value = "活动类型",name = "activityType")
    private Integer activityType;

    @ApiModelProperty(value = "保养年数",name = "maintainYears")
    private Integer maintainYears;

    /**
     * 套餐代码
     */
    private List<MainFileReturnVO> mainFileList;


    @ApiModelProperty(name = "确认状态",value = "orderConfirmStatus")
    private String orderConfirmStatus;

    @ApiModelProperty(name = "不返还销售价",value = "noReturnSalesPrice")
    private BigDecimal noReturnSalesPrice;
}
