package com.volvo.bff.volvoworks.wechat.common.model.vo.accidentClues;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 
* TODO description
* <AUTHOR>
* @date 2020年11月13日
 */
@ApiModel("线索工单接口出参Vo")
@Data
public class ClueRepairOrderResultVO {

    /**
     * 经销商代码、工单号、开单时间、工单状态、维修类型、是否接待（是否关联环检单）、是否工单（工单是否首次打印）
     */
    @ApiModelProperty(value = "工单号", name = "roNo")
    private String roNo;

    @ApiModelProperty(value = "经销商代码", name = "ownerCode")
    private String ownerCode;

    @ApiModelProperty(value = "开单时间", name = "roCreateDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date roCreateDate;

    @ApiModelProperty(value = "维修类型", name = "repairTypeCode")
    private String repairTypeCode;

    @ApiModelProperty(value = "维修类型名称", name = "repairTypeName")
    private String repairTypeName;

    @ApiModelProperty(value = "是否接待（是否关联环检单）", name = "isReceive")
    private String isReceive;

    @ApiModelProperty(value = "是否工单（工单是否首次打印）", name = "isOrder")
    private String isOrder;

    @ApiModelProperty(value = "VIN", name = "vin")
    private String vin;
    
    @ApiModelProperty(value = "工单状态(8049)", name = "roStatus")
    private Integer roStatus;

}
