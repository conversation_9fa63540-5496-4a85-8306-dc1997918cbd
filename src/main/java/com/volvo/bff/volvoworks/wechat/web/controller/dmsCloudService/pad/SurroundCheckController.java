package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.pad;


import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.pad.PadCustomerRequireVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.pad.PadPreviewInteriorVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.pad.SurroundCheckVO;
import com.volvo.bff.volvoworks.wechat.common.service.pad.SurroundCheckService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/SurroundCheckController")
@Api(value = "车辆环检", tags = {"pad-车辆环检"})
public class SurroundCheckController {

    @Autowired
    SurroundCheckService surroundCheckService;

    /**
     * 新增环检单-第一页
     *
     * <AUTHOR>
     * @date 2020年5月18日
     */

    @ApiOperation(value = "新增环检单-第一页", notes = "新增环检单-第一页", httpMethod = "POST")
    @PostMapping(value = "/saveSuroundCheck")
    public ResponseDTO<String> saveSuroundCheck1(@RequestBody SurroundCheckVO surroundCheckVO) {
        return ResponseUtils.success(surroundCheckService.saveSurroundCheck1(surroundCheckVO));
    }

    /**
     * 新增环检单-第二页
     * 外观检查
     *
     * <AUTHOR>
     * @date 2020年5月18日
     */

    @ApiOperation(value = "新增环检单-第二页", notes = "新增环检单-第二页", httpMethod = "POST")
    @PostMapping(value = "/saveSuroundCheck2")
    public ResponseDTO<String> saveSuroundCheck2(@RequestBody PadPreviewInteriorVo appearanceCheckVO) {
        return ResponseUtils.success(surroundCheckService.saveSurroundCheck2(appearanceCheckVO));
    }

    /**
     * 新增环检单-内饰
     * <AUTHOR>
     * @date 2020年5月18日
     */

    @ApiOperation(value = "新增环检单-内饰", notes = "新增环检单-内饰", httpMethod = "POST")
    @PostMapping(value = "/saveSuroundCheckInterior")
    public ResponseDTO<String> saveSuroundCheckInterior(@RequestBody PadPreviewInteriorVo padPreviewInteriorVo) {
        return ResponseUtils.success(surroundCheckService.savePreviewInterior(padPreviewInteriorVo));
    }

    /**
     * 新增环检单-客户需求
     * <AUTHOR>
     * @date 2020年5月18日
     */

    @ApiOperation(value = "新增环检单-客户需求", notes = "新增环检单-客户需求", httpMethod = "POST")
    @PostMapping(value = "/savePreviewCustomerRequire")
    public ResponseDTO<String> savePreviewCustomerRequire(@RequestBody PadCustomerRequireVo padCustomerRequireVo) {
        return  ResponseUtils.success(surroundCheckService.savePreviewCustomerRequire(padCustomerRequireVo));
    }

    /**
     * 环检单Base64转File 传中台
     * <AUTHOR>
     * @date 2021-2-23
     */

    @ApiOperation(value = "环检单图片上传", notes = "环检单图片上传", httpMethod = "POST")
    @PostMapping(value = "/saveSendBaseFile")
    public ResponseDTO<String> saveSendBaseFile(@RequestBody Map<String,String> map) {
        return ResponseUtils.success(surroundCheckService.saveSendBaseFile(map));
    }
}
