package com.volvo.bff.volvoworks.wechat.web.controller.bffAftersales;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.LucencyWorkshop.*;
import com.volvo.bff.volvoworks.wechat.common.model.dto.LucencyWorkshop.QueryParamDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.workshop.LucencyWorkShopService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/lucencyWorkShop/v1")
@Api(value = "透明车间列表查询", tags = {"透明车间列表查询"})
@Slf4j
@AllArgsConstructor
public class LucencyWorkShopController {

    @Autowired
    private LucencyWorkShopService lucencyWorkshopService;


    /**
     * 看版交车列表
     */
    @ApiOperation(value = "看板交车列表", notes = "看板交车列表", httpMethod = "POST")
    @PostMapping(value = "/selectDeliveryList")
    @ResponseBody
    public ResponseDTO<PageBean<DeliveryDTO>> selectDeliveryList(@RequestBody QueryParamDto queryParamDto){
        return ResponseUtils.success(lucencyWorkshopService.selectDeliveryList(queryParamDto));
    }

    /**
     * 开单页面查询
     * @param queryParamDto
     * @return
     */

    @ApiOperation(value = "开单页面查询", notes = "开单页面查询", httpMethod = "POST")
    @PostMapping(value = "/selectBeginOrderList")
    @ResponseBody
    public ResponseDTO<PageBean<BeginOrderDTO>> selectBeginOrderList(@RequestBody QueryParamDto queryParamDto) {
        if(StringUtils.isEmpty(queryParamDto.getOwnerCode())){
            log.info("selectBeginOrderList ownerCoe is null");
            return ResponseUtils.success(new PageBean<>());
        }
        return ResponseUtils.success(lucencyWorkshopService.selectBeginOrderList(queryParamDto));

    }

    /**
     * 查询派工数量
     * @param queryParamDto
     * @return
     */

    @ApiOperation(value = "查询派工数量", notes = "查询派工数量", httpMethod = "POST")
    @PostMapping(value = "/selectAssignStatusCount")
    @ResponseBody
    public ResponseDTO<List<StatusCountDTO>> selectAssignStatusCount(@RequestBody QueryParamDto queryParamDto){
        return ResponseUtils.success(lucencyWorkshopService.selectAssignStatusCount(queryParamDto));
    }

    /**
     * 查询维修页面
     * @param queryParamDto
     * @return
     */

    @ApiOperation(value = "查询维修页面", notes = "查询维修页面", httpMethod = "POST")
    @PostMapping(value = "/selectWorkshopRepairOrder")
    @ResponseBody
    public ResponseDTO<PageBean<WorkShopRepairOrderDTO>> selectWorkshopRepairOrder(@RequestBody QueryParamDto queryParamDto){
        if(StringUtils.isEmpty(queryParamDto.getOwnerCode())){
            log.info("selectWorkshopRepairOrder ownerCode is null");
            return ResponseUtils.success(new PageBean<>());
        }
        return ResponseUtils.success(lucencyWorkshopService.selectWorkshopRepairOrder(queryParamDto));
    }


    @ApiOperation(value = "查询打卡数量", notes = "查询打卡数量", httpMethod = "POST")
    @PostMapping(value = "/selectIsPunchCount")
    @ResponseBody
    public ResponseDTO<List<StatusCountDTO>> selectIsPunchCount(@RequestBody QueryParamDto queryParamDto){
        return ResponseUtils.success(lucencyWorkshopService.selectIsPunchCount(queryParamDto));
    }


    @ApiOperation(value = "查询结算页面", notes = "查询结算页面", httpMethod = "POST")
    @PostMapping(value = "/selectWorkShopBalanceOrder")
    @ResponseBody
    public ResponseDTO<PageBean<WorkShopBalanceOrderDTO>> selectWorkShopBalanceOrder(@RequestBody QueryParamDto queryParamDto){
        if(StringUtils.isEmpty(queryParamDto.getOwnerCode())){
            log.info("selectWorkShopBalanceOrder ownerCode is null");
            return ResponseUtils.success(new PageBean<>());
        }
        return ResponseUtils.success(lucencyWorkshopService.selectWorkShopBalanceOrder(queryParamDto));
    }


    @ApiOperation(value = "查询交车数量", notes = "查询交车数量", httpMethod = "POST")
    @PostMapping(value = "/selectDeliveryTagCount")
    @ResponseBody
    public ResponseDTO<List<StatusCountDTO>> selectDeliveryTagCount(@RequestBody QueryParamDto queryParamDto){
        return ResponseUtils.success(lucencyWorkshopService.selectDeliveryTagCount(queryParamDto));
    }
}
