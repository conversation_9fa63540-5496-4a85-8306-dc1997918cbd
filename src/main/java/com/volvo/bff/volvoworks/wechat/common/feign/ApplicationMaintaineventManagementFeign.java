package com.volvo.bff.volvoworks.wechat.common.feign;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * 经销商贷款 feign 调用接口
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@FeignClient(name = "application-maintainevent-management",
url = "${mse-in.tob.application-maintainevent-management.url}",
path = "${mse-in.tob.application-maintainevent-management.path}")
public interface ApplicationMaintaineventManagementFeign {
	
    /**
     * 更新是否已读
     * @param id
     * @return
     * 查询 mq 配置
     *
     * @return 配置信息
     */
    @GetMapping("/v1/event/updateMsgById")
    DmsResultDTO<Boolean> updateMsgById(@RequestParam(value = "id") String id);
}
