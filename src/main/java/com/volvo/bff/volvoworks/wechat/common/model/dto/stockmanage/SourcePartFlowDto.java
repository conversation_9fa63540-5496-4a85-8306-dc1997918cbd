package com.volvo.bff.volvoworks.wechat.common.model.dto.stockmanage;

import lombok.Data;

/**
 * 无系统记录溯源件保存流水记录入参
 * 1.5-1.17零件有码但无溯源记录
 *
 * <AUTHOR>
 * @date    2023年02月05日16:12:48
 * @version origin/feature-CVTB-5799-new-v2
 */
@Data
public class SourcePartFlowDto {

        /**
         * 项目ID
         */
        private String itemId;

        /**
         * 溯源码
         */
        private String sourceCode;

        /**
         * 业务单号
         */
        private String roNo;

        /**
         * 业务类型：80531010（调拨出库），80531032（调拨退回），80531007（零附件销售），80531006（维修领料）
         */
        private String businessCode;

}
