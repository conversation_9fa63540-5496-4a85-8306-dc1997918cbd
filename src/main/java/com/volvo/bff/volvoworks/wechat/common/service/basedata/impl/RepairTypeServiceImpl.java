package com.volvo.bff.volvoworks.wechat.common.service.basedata.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.basedata.RepairTypeService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class RepairTypeServiceImpl  implements RepairTypeService {

    @Resource
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Override
    public List<Map> queryAllRepairType(String ownerCode) {
        DmsResultDTO<List<Map>> res = dmscloudServiceFeign.queryRepairtypes(ownerCode);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }
}
