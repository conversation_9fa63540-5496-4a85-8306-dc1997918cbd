package com.volvo.bff.volvoworks.wechat.common.model.dto.pds;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@ApiModel("AddBeforerspDTO")
public class AddBeforerspDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(name = "pdsNos")
	private List<String> pdsNos;
	@ApiModelProperty(name = "pdsConfig")
	private List<PdsConfigsDTO> pdsConfig;
}