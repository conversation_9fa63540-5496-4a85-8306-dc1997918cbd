package com.volvo.bff.volvoworks.wechat.common.service.app.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.app.DamagedPartService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class DamagedPartServiceImpl implements DamagedPartService {

    @Resource
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Override
    public PageBean<DamagedEstimateOrderResultVo> queryEstimateForDamaged(DamagedEstimateOrderParamVo damagedEstimateOrderParamVo) {
        log.info("queryEstimateForDamaged param:{}",damagedEstimateOrderParamVo);
        DmsResultDTO<PageBean<DamagedEstimateOrderResultVo>> res = dmscloudServiceFeign.queryEstimateForDamaged(damagedEstimateOrderParamVo);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public List<DamagedPartResultDto> queryDamagedPartList(String estimateNo, String locId) {
        DmsResultDTO<List<DamagedPartResultDto>> res = dmscloudServiceFeign.queryDamagedPartList(estimateNo,locId);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }
    @Override
    public String saveDamaged(SaveDamagedPartVo saveDamagedPartVo) {
        DmsResultDTO<String> res = dmscloudServiceFeign.saveDamaged(saveDamagedPartVo);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public SaveDamagedPartVo queryDamagedPart(SaveDamagedPartVo saveDamagedPartVo) {
        DmsResultDTO<SaveDamagedPartVo> res = dmscloudServiceFeign.queryDamagedPart(saveDamagedPartVo);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public PageBean<EstimateDetailResultVo> queryEstimateDetail(String estimateNo) {
        DmsResultDTO<PageBean<EstimateDetailResultVo>> res = dmscloudServiceFeign.queryEstimateDetail(estimateNo);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public String deleteEstimate(String estimateNo,String locId) {
        DmsResultDTO<String> res = dmscloudServiceFeign.deleteEstimate(estimateNo,locId);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }
}
