package com.volvo.bff.volvoworks.wechat.common.model.dto.midendauthcenter;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("账户信息返回")
@Data
public class RoleDealerUserInfoRespDTO {

    /**
     * 用户账号状态
     */
    @ApiModelProperty(value = "用户账号状态",name = "accountStatus")
    private Integer accountStatus;

    /**
     * 销售小区名称
     */
    @ApiModelProperty(value = "销售小区名称",name = "saleSmallAreaName")
    private String saleSmallAreaName;

    /**
     * 国家
     */
    @ApiModelProperty(value = "国家",name = "country")
    private String country;

    /**
     * 用戶修改时间
     */
    @ApiModelProperty(value = "用戶修改时间",name = "updateTime")
    private String updateTime;
    
    /**
     * 用戶创建时间
     */
    @ApiModelProperty(value = "用戶创建时间",name = "createTime")
    private String createTime;
    
    /**
     * 证件号（敏感数据）
     */
    @ApiModelProperty(value = "证件号（敏感数据）",name = "idcardNumber")
    private String idcardNumber;
    
    /**
     * 在职状态 ********:在职 , ********:离职
     */
    @ApiModelProperty(value = "在职状态 ********:在职 , ********:离职",name = "isOnjob")
    private Integer isOnjob;
    
    /**
     * 评分
     */
    @ApiModelProperty(value = "评分",name = "score")
    private Integer score;
    
    /**
     * 性别（10021001男 10021002女）
     */
    @ApiModelProperty(value = "性别（10021001男 10021002女）",name = "gender")
    private Integer gender;
    
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱",name = "email")
    private String email;
    
    /**
     * unionId
     */
    @ApiModelProperty(value = "unionId",name = "unionId")
    private String unionId;
    
    /**
     * 经销商名称中文
     */
    @ApiModelProperty(value = "经销商名称中文",name = "companyNameCn")
    private String companyNameCn;
    
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id",name = "userId")
    private Integer userId;
    
    /**
     * 地址
     */
    @ApiModelProperty(value = "地址",name = "adderss")
    private String adderss;
    
    /**
     * 用戶修改人
     */
    @ApiModelProperty(value = "用戶修改人",name = "updateBy")
    private String updateBy;
    
    /**
     * 工作年限
     */
    @ApiModelProperty(value = "工作年限",name = "workYears")
    private Integer workYears;
    
    /**
     * 用戶创建人
     */
    @ApiModelProperty(value = "用戶创建人",name = "createBy")
    private String createBy;
    
    /**
     * 电话号码（敏感数据）
     */
    @ApiModelProperty(value = "电话号码（敏感数据）",name = "phone")
    private String phone;
    
    /**
     * vcpa
     */
    @ApiModelProperty(value = "vcpa",name = "vcpa")
    private String vcpa;
    
    /**
     * 微信号
     */
    @ApiModelProperty(value = "微信号",name = "wechat")
    private String wechat;
    
    /**
     * 账号
     */
    @ApiModelProperty(value = "账号",name = "userCode")
    private String userCode;
    
    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期",name = "birthday")
    private String birthday;
    
    /**
     * 售后大区名称
     */
    @ApiModelProperty(value = "售后大区名称",name = "afterBigAreaName")
    private String afterBigAreaName;
    
    /**
     * 售后小区名称
     */
    @ApiModelProperty(value = "售后小区名称",name = "afterSmallAreaName")
    private String afterSmallAreaName;
    
    /**
     * 本身所在company_id
     */
    @ApiModelProperty(value = "本身所在company_id",name = "companyId")
    private Integer companyId;
    
    /**
     * 省级
     */
    @ApiModelProperty(value = "省级",name = "province")
    private String province;
    
    /**
     * 头像图片
     */
    @ApiModelProperty(value = "头像图片",name = "heardImgUrl")
    private String heardImgUrl;
    
    /**
     * 经销商简称中文
     */
    @ApiModelProperty(value = "经销商简称中文",name = "companyShortNameCn")
    private String companyShortNameCn;
    
    /**
     * 销售大区名称
     */
    @ApiModelProperty(value = "销售大区名称",name = "saleBigAreaName")
    private String saleBigAreaName;
    
    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名",name = "employeeName")
    private String employeeName;
    
    /**
     * 职位
     */
    @ApiModelProperty(value = "职位",name = "position")
    private String position;
    
    /**
     * 经销商代码
     */
    @ApiModelProperty(value = "经销商代码",name = "companyCode")
    private String companyCode;

}
