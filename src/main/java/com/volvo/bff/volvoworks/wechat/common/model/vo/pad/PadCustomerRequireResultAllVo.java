package com.volvo.bff.volvoworks.wechat.common.model.vo.pad;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName PadCustomerRequireResultAllVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/5/25 13:04
 */
@Data
@ApiModel("客户需求总查询返回所有Vo")
public class PadCustomerRequireResultAllVo {

    @ApiModelProperty(value = "客户需求结果集",name = "padCustomerRequireResultVo")
    private PadCustomerRequireResultVo padCustomerRequireResultVo;

    @ApiModelProperty(value = "车辆故障问诊结果集",name = "padVehicleDetailsVoList")
    private List<PadVehicleDetailsVo> padVehicleDetailsVoList;

    @ApiModelProperty(value = "内饰外饰结果集",name = "bodyAppearanceVoList")
    private List<BodyAppearanceVo> bodyAppearanceVoList;
}
