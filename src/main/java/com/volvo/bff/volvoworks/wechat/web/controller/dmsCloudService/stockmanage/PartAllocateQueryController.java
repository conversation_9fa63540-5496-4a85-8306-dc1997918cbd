package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.stockmanage;

import com.volvo.bff.volvoworks.wechat.common.model.dto.stockmanage.ListPartAllocateInItemDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.stockmanage.PartAllocataAllService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api("/stockmanage/partAllocateQuery")
@Controller
@RequestMapping(value = "/stockmanage/partAllocateQuery")
public class PartAllocateQueryController {

    @Autowired
    private PartAllocataAllService partAllocataAllService;
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @ResponseBody
    @RequestMapping(value = "/queryAllInfo",method = RequestMethod.GET)
    public ResponseDTO<PageInfoDto> queryAllocateInfoPage(@RequestParam Map<String, String> map) {
        return ResponseUtils.success(partAllocataAllService.queryAllocateInfoPage(map));
    }
    //调拨退回明细
    @ApiOperation(value = "调拨退回明细", notes = "调拨退回明细", httpMethod = "GET")
    @ResponseBody
    @RequestMapping(value = "/queryInfoById/{id}",method = RequestMethod.GET)
    public ResponseDTO<List<Map>> queryAllocateInfoPage(@PathVariable String id) {
        return ResponseUtils.success(partAllocataAllService.queryAllocateInfoById(id));
    }

    @ApiOperation(value = "调拨退回确认", notes = "根据调拨退回退货确认", httpMethod = "POST")
    @RequestMapping(value="/feeSettlement",method = RequestMethod.POST)
    @ResponseBody
    public void  feeSettlement(@RequestBody ListPartAllocateInItemDto listPartAllocateInItemDto){
        partAllocataAllService.feeSettlement(listPartAllocateInItemDto);
    }
}
