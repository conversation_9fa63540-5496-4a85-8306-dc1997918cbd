package com.volvo.bff.volvoworks.wechat.common.model.dto.city;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/03/10
 */
@ApiModel(description = "")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RegionAllDto {

    // id
    @ApiModelProperty(value = "id", position = 1,required = true)
    private Integer id;
    // 地区id
    @ApiModelProperty(value = "地区id", position = 2,required = true)
    private Integer regionId;
    // 地区code
    @ApiModelProperty(value = "地区code", position = 3,required = true)
    private String regionCode;
    // 地区名称
    @ApiModelProperty(value = "地区名称", position = 4,required = true)
    private String regionName;
    // 地区英文名称
    @ApiModelProperty(value = "地区英文名称", position = 5,required = false)
    private String regionEname;
    // 父地区id
    @ApiModelProperty(value = "父地区id", position = 6,required = true)
    private String parentRegionId;
    // 子节点
    @ApiModelProperty(value = "子节点", position = 7,required = false)
    private List<RegionAllDto> tcRegionList;

}
