package com.volvo.bff.volvoworks.wechat.common.service.applicationMaintenanceentertain;

import com.alibaba.fastjson.JSONObject;
import com.volvo.bff.volvoworks.wechat.common.model.dto.applicationMaintenanceentertain.ReceptionEntrancePageConfigReqDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;

import java.util.List;

public interface IReceptionEntranceConfigService {

	/**
	 * 查询进场接待页面配置化
	 * @param pageConfigReq
	 * @return
	 */
	List<JSONObject> queryReceptionEntranceConfigByType(
			ReceptionEntrancePageConfigReqDTO pageConfigReq);
	
}
