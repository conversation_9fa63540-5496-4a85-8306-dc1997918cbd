package com.volvo.bff.volvoworks.wechat.common.model.vo.workshop;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


@Data
@ApiModel("维修进度看板查询结果")
public class RepairProgressResultVo {

    //app返回字段
    @ApiModelProperty(value = "工单状态",name = "roStatus")
    private Long roStatus;


    @ApiModelProperty(value = "技师",name = "technician")
    private String technician;

    //app返回字段
    @ApiModelProperty(value = "进厂日期（接车日期）",name = "pickupDate")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date pickupDate;

    @ApiModelProperty(value = "工单号",name = "roNo")
    private String roNo;

    //维修类型
    @ApiModelProperty(value = "维修类型",name = "repairTypeCode")
    private String repairTypeCode;

    @ApiModelProperty(value = "车牌号",name = "license")
    private String license;

    @ApiModelProperty(value = "VIN",name = "vin")
    private String vin;

    @ApiModelProperty(value = "VINForShow",name = "vinForShow")
    private String vinForShow;

    @ApiModelProperty(value = "服务顾问",name = "serviceAdvicer")
    private String serviceAdvisor;

    @ApiModelProperty(value = "服务顾问",name = "roCreateDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date roCreateDate;

    //距离交车时间
    @ApiModelProperty(value = "预交车时间",name = "endTimeSupposed")
    private String endTimeSupposed;

    //预约单号
    @ApiModelProperty(value = "预约",name = "isBooking")
    private String isBooking;

    //环检单号
    @ApiModelProperty(value = "接待",name = "isBooking")
    private String isYJ;

    //工单是否打印、确认
    @ApiModelProperty(value = "工单",name = "isPrint")
    private String isPrint;

    //
    @ApiModelProperty(value = "派工",name = "isAssign")
    private String isAssign;

    //
    @ApiModelProperty(value = "维修",name = "isAssign")
    private String isRepairing;

    @ApiModelProperty(value = "质检",name = "isAssign")
    private String isSeasonCheck;

    @ApiModelProperty(value = "洗车",name = "isWash")
    private String isWash;

    @ApiModelProperty(value = "验收",name = "isFinish")
    private String isFinish;

    @ApiModelProperty(value = "结算",name = "isBalance")
    private String isBalance;

    @ApiModelProperty(value = "领料",name = "isPicking")
    private String isPicking;




}
