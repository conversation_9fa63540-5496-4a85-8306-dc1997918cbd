package com.volvo.bff.volvoworks.wechat.common.model.dto.order;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@ApiModel("服务信息查询")
@Data
public class OrderServiceInfoQueryDTO {

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号",name = "roNo",required = true)
    private String roNo;

    /**
     * 经销商
     */
    @ApiModelProperty(value = "经销商",name = "ownerCode",required = true)
    private String ownerCode;

    /**
     * vin
     */
    @ApiModelProperty(value = "vin",name = "vin")
    private String vin;
    
    /**
     * 服务信息来源
     */
    @ApiModelProperty(value = "服务信息来源",name = "infoSource")
    private String infoSource;

}
