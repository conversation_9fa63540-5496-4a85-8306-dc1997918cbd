package com.volvo.bff.volvoworks.wechat.common.aspect;
import java.util.*;

import javax.servlet.http.HttpServletRequest;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.RestResultResponse;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.google.gson.Gson;
import com.volvo.bff.volvoworks.wechat.common.exception.ExceptionEnum;
import com.volvo.bff.volvoworks.wechat.common.exception.UtilException;
import com.volvo.bff.volvoworks.wechat.common.utils.JSONUtil;
import com.volvo.bff.volvoworks.wechat.common.utils.TransmittableThreadLocalUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 统一返回JSON序列化处理切面
 * 
 */
@Slf4j
@Component
@Aspect
public class LogPrintingAspect {
    
    /**
     * 定义切点，这里切在controller包下所有方法上
     */
    @Pointcut("within(com.volvo.bff.volvoworks.wechat.*.controller..*)")
    public void logPrintingAspect() {
    	// Do nothing because controller切点
    }

    /**    
     * 在切点之前织入    
     * @param joinPoint    
     * @throws Throwable    
     */    
    @Before("logPrintingAspect()")    
    public void doBefore(JoinPoint joinPoint) {    
        // 开始打印请求日志    
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();    
        HttpServletRequest request = attributes.getRequest();    
        // 打印请求相关参数    
        log.info("========================================== Start ==========================================");
        
        List<String> list = requestPrinting(request);
        // 打印请求 url
        log.info("URL: {}, HTTP Method: {}, Class Method : {}.{}, IP: {}, uuid: {}, Request Args : {}", request.getRequestURL().toString(), request.getMethod(), joinPoint.getSignature().getDeclaringTypeName(), joinPoint.getSignature().getName(), request.getRemoteAddr(), list, joinPoint.getArgs());    
    }
    
    /**
     * 请求头打印
     * @param httpServletRequest
     */
	private List<String> requestPrinting(HttpServletRequest httpServletRequest) {
		// 
		
		
		// 请求头存入线程缓存
		try {
			Enumeration<String> headerNames = httpServletRequest.getHeaderNames();
			List<String> list = new ArrayList<>();
			Map<String, String> map = new HashMap<>();
			if (headerNames != null) {
				while (headerNames.hasMoreElements()) {
					String name = headerNames.nextElement();
					String value = httpServletRequest.getHeader(name);
					map.put(name, value);
					
					list.add(String.join("====header====", name, value));
				}
				TransmittableThreadLocalUtils.setValue(map);
			}
			log.info("header==== {}", list);
			return list;
		} catch (Exception e) {
			log.info("异常：{}", e.getMessage());
		}
		return new ArrayList<>();
	}
    
    /**
     * 定义一个环绕通知，打印方法入参 与 出参
     * 
     * @param joinPoint
     *            连接点
     * @return Object
     * @throws Throwable 
     */
    @Around("logPrintingAspect()")
    public Object around(ProceedingJoinPoint joinPoint){
        long startTime = System.currentTimeMillis();
        Object result = null;
        try {
        	result = joinPoint.proceed();
        	// 执行耗时
        	result = errorMsgConvert(result);
        } catch (UtilException e) {
			throw e;
		} catch (Throwable e) {
			log.error("异常处理：", e);
			throw new UtilException(ExceptionEnum.SYSTEM_ERROR);
		} finally {
        	log.info("Time-Consuming : {} ms", System.currentTimeMillis() - startTime);    
		}
        return result;
    }
    
    /**
     * 异常msg转换
     * @param result
     * @return
     */
	private Object errorMsgConvert(Object result) {
		try {
			String json = new Gson().toJson(result);
			// 打印出参
			log.info("Response Args  : {}", json);
			RestResultResponse fromJson = new Gson().fromJson(json, RestResultResponse.class);
			String errMsg = fromJson.getErrorMessage();
			if(errMsg.length()>50) {
				fromJson.setDisplayMessage(ExceptionEnum.SYSTEM_ERROR.getDisplayMessage());
				result = fromJson;
			}
			log.info("Response Args Error convert : {}", new Gson().toJson(result));
		} catch (Exception e) {
			log.info("【异常转换异常：{}】", e.getMessage());
		}
		return result;
	}
    
    /**    
     * 在切点之后织入    
     * @throws Throwable    
     */    
    @After("logPrintingAspect()")    
    public void doAfter() {
    	TransmittableThreadLocalUtils.remove();
    	log.info("已清理线程级缓存：{}", JSONUtil.objectToJson(TransmittableThreadLocalUtils.getValue()));
        log.info("=========================================== End ===========================================");    
    }
    
    @AfterThrowing(pointcut = "logPrintingAspect()", throwing = "ex")
    public void afterThrowing(JoinPoint joinPoint, Throwable ex) {
    	try {
    		Signature signature = joinPoint.getSignature();
    		String method = signature.getName();
    		// 处理异常的逻辑
    		log.info("执行方法{}出错，异常为：{}", method, ex);
		} catch (Exception e) {
			log.info("异常：{}", e.getCause());
		}
    }

}
