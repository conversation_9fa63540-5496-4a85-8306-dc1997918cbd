package com.volvo.bff.volvoworks.wechat.common.feign;


import com.volvo.bff.volvoworks.wechat.common.model.dto.accidentClues.GetDealerUserDataDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.accidentClues.EmpByRoleCodeVO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "dmscus-ifservice",
        url = "${mse-in.tob.dmscus-ifservice.url}",
        path = "${mse-in.tob.dmscus-ifservice.path}")
public interface DmscusIFserviceFeign {
    @ApiOperation(value = "查看员工")
    @PostMapping(value="/dealer/userTwo")
    DmsResultDTO<List<EmpByRoleCodeVO>> getDealerUserTwo(@RequestBody GetDealerUserDataDTO getDealerUserDTO);

    @ApiOperation(value = "查看员工")
    @PostMapping(value="/dealer/user")
    DmsResultDTO<List<EmpByRoleCodeVO>> getDealerUser(@RequestBody GetDealerUserDataDTO getDealerUserDTO);

}
