package com.volvo.bff.volvoworks.wechat.common.model.dto.vhc;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * vhc报价-检查项DTO
 */
@Data
public class VhcMaintanceDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private Integer id;
    /**
     * 大类名称
     */
    private String className;
    /**
     * 检查项目(车辆举升,非举升)
     */
    private String classProject;
    /**
     * 状态（红绿黄灯）
     * 红:89706001
     * 绿:89706002
     * 黄:89706003
     */
    private String classState;
    /**
     * 配置表大类id
     */
    private String configClassId;
    /**
     * 二级类目检查集合
     */
    private List<VhcItemDTO> vhcItemList;
    /**
     * 二级类目检查集合数量
     */
    private Integer vhcItemCount;
}
