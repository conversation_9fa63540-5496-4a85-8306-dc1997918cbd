package com.volvo.bff.volvoworks.wechat.common.model.dto.pds;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@ApiModel("SummaryDTO")
public class SummaryDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(name = "firstCheckIng")
	private int firstCheckIng;
	@ApiModelProperty(name = "lastCheckIng")
	private int lastCheckIng;
	@ApiModelProperty(name = "firstCheckDone")
	private int firstCheckDone;
	@ApiModelProperty(name = "lastCheckDone")
	private int lastCheckDone;
}