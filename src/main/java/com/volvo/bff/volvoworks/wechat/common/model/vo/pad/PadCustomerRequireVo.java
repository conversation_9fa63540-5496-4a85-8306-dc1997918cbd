package com.volvo.bff.volvoworks.wechat.common.model.vo.pad;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName PadCustomerRequireVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/5/21 10:10
 */
@Data
@ApiModel("pad环检单客户需求VO")
public class PadCustomerRequireVo {

    @ApiModelProperty(value = "预检单号",name = "yjNo")
    private String yjNo;

    @ApiModelProperty(value = "维修类型",name = "maintType")
    private String maintType;

    @ApiModelProperty(value = "常规保养",name = "routeMaint")
    private String routeMaint;

    @ApiModelProperty(value = "更换后刹车片",name = "replaceAfter")
    private String replaceAfter;

    @ApiModelProperty(value = "雨刮",name = "wiper")
    private String wiper;

    @ApiModelProperty(value = "其它待定",name = "otherPend")
    private String otherPend;

    @ApiModelProperty(value = "旧件是否带走",name = "oldGoway")
    private String oldGoway;

    @ApiModelProperty(value = "洗车类型",name = "watherCarType")
    private String watherCarType;

    @ApiModelProperty(value = "是否在等待区休息",name = "instoreWait")
    private String instoreWait;

    @ApiModelProperty(value = "是否在店内就餐",name = "eatLunch")
    private String eatLunch;

    @ApiModelProperty(value = "是否送车上门",name = "isSendCar")
    private String isSendCar;

    @ApiModelProperty(value = "客户需求录音",name = "bodyAppearanceVo")
    private BodyAppearanceVo bodyAppearanceVo;

    @ApiModelProperty(value = "故障问诊",name = "padVehicleDetailsVoList")
    private List<PadVehicleDetailsVo> padVehicleDetailsVoList;

    @ApiModelProperty(value = "客户描述",name = "customerDesc")
    private String customerDesc;

    @ApiModelProperty(value = "车辆症状",name = "vehicleSymptom")
    private String vehicleSymptom;

}
