package com.volvo.bff.volvoworks.wechat.common.model.dto.pds;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@ApiModel("StatusGroupDTO")
public class StatusGroupDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(name = "pdsStatus")
	private Integer pdsStatus;
	@ApiModelProperty(name = "count")
	private Integer count;
}