package com.volvo.bff.volvoworks.wechat.web.controller.repairorder;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.volvo.bff.volvoworks.wechat.common.model.PageBeanV2;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.QueryRepairingOrderResultV2Vo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.QueryStructureCheckRrquestV2Vo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.order.RepairOrderService;
import com.volvo.bff.volvoworks.wechat.common.utils.TransmittableThreadLocalUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Api(value = "/queryRepairOrder/v2", tags = {"功能描述：工单查询"})
@RestController
@RequestMapping("/queryRepairOrder/v2")
@Slf4j
public class QueryRepairOrderV2Controller {

	@Autowired
	private RepairOrderService repairOrderService;

    @ApiOperation(value = "维修终检-根据入参查询工单列表", notes = "维修终检-根据入参查询工单列表", httpMethod = "POST")
    @PostMapping("/queryAllRepairOrder")
    public ResponseDTO<PageBeanV2<QueryRepairingOrderResultV2Vo>> queryAllRepairOrder(@RequestBody QueryStructureCheckRrquestV2Vo queryParams) {
    	log.info("queryAllRepairOrder: {}", JSON.toJSONString(queryParams));
        return new ResponseDTO<>().successData(repairOrderService.queryAllRepairOrder(queryParams));
    }
}
