package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.print;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.print.PrintDataVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.print.PrintParamVo;
import com.volvo.bff.volvoworks.wechat.common.service.print.PrintBalanceRoService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Api(tags = "工单结算单打印数据查询")
@RestController
@RequestMapping("/printBalanceRo")
public class PrintBalanceRoController {

    @Autowired
    PrintBalanceRoService printBalanceRoService;

    @ApiOperation(value = "工单/结算单打印数据获取", notes = "工单/结算单打印数据获取", httpMethod = "POST")
    @PostMapping("/balancePrintData")
    public ResponseDTO<PrintDataVo> balancePrintData(@RequestBody PrintParamVo paramsVo) {

        return ResponseUtils.success(printBalanceRoService.balancePrintData(paramsVo));
    }
}
