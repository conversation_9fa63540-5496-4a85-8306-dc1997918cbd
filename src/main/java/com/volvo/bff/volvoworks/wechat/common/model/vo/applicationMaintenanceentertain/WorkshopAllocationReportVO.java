package com.volvo.bff.volvoworks.wechat.common.model.vo.applicationMaintenanceentertain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 透明车间分拨报表
 */
@Data
public class WorkshopAllocationReportVO implements Serializable {
	private Integer index; // 序号

	private String ownerCode;  // 所有者代码

	private String license;  // 车牌号

	private String vin; // vin

	private String vinStr;


	private Integer businessType;   // 进厂类型

	private String businessTypeStr;


	private Integer businessSubType;

	private String businessSubTypeStr;


	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date businessTime;   // 业务触发时间

	private String businessNo;   // 业务单号

	private Long serviceAdvisor;   // 服务顾问ID

	private String serviceName;   // 服务顾问名称

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date entryTime;  // 摄像头进厂时间

	private String valueTag; // 产值标签

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date roCreateDate;  // 开单时间

	private Integer repairOrderFlag;   // 是否转工单(字典:1004)

	private String repairOrderFlagStr;   // 是否转工单(字典:1004)


	private String relationRoNo;  // 主工单工单号
}