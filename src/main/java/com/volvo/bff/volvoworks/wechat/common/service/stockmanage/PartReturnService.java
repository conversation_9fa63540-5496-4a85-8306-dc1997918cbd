package com.volvo.bff.volvoworks.wechat.common.service.stockmanage;

import com.volvo.bff.volvoworks.wechat.common.model.dto.basedata.ListPartReturnDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageDto;

import java.util.List;
import java.util.Map;

public interface PartReturnService {

    PageDto queryPartReturn(Map<String, Object> queryParam);

    List<Map> queryPartReturnDetail(Map<String, String> queryParam);

    void deletePartReturn(ListPartReturnDTO listPartReturnDto);

    String partReturnOut(ListPartReturnDTO listPartReturnDto);

}
