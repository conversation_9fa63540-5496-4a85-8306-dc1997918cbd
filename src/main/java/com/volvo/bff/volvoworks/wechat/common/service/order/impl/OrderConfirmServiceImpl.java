package com.volvo.bff.volvoworks.wechat.common.service.order.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.dto.order.OrderConfirmVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.order.OrderConfirmService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class OrderConfirmServiceImpl  implements OrderConfirmService {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Override
    public String orderIsOrNotConfirm(OrderConfirmVo confirmVo) {
        DmsResultDTO<String> response = dmscloudServiceFeign.getorderStatementConfirm(confirmVo);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public void orderStatementConfirm(OrderConfirmVo confirmVo) {
        DmsResultDTO<Void> response = dmscloudServiceFeign.orderStatementConfirm(confirmVo);
        ErrorConversionUtils.errorCodeConversion(response);
    }
}
