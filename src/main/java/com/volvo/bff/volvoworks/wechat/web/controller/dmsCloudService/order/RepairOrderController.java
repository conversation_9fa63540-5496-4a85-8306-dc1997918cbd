package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.order;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.order.RepairOrderService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Api(value = "order/repair", tags = {"工单信息"})
@RestController
@RequestMapping("order/repair")
public class RepairOrderController {

    @Autowired
    private RepairOrderService orderService;

    @ApiOperation(value = "查询在修工单", notes = "查询在修工单 用于客户接待查询工单子页面", httpMethod = "GET")
    @RequestMapping(value = "/searchRepairOrder",method = RequestMethod.GET)
    @ResponseBody
    public ResponseDTO<PageInfoDto> searchRepairOrder(@RequestParam Map<String, String> queryParam) {
        PageInfoDto pageInfoDto = orderService.searchRepairOrder(queryParam);
        return ResponseUtils.success(pageInfoDto);
    }
}
