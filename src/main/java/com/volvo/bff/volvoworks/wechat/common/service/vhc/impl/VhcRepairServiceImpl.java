package com.volvo.bff.volvoworks.wechat.common.service.vhc.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.ApplicationMaintainManagementFeign;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.vhc.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.vhc.VhcRepairService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class VhcRepairServiceImpl implements VhcRepairService {

    @Autowired
    private ApplicationMaintainManagementFeign applicationMaintainManagementFeign;

    @Override
    public PageBean<VhcDetailsDTO> selectVhcList(QueryVhcDto queryVhcDto) {
        DmsResultDTO<PageBean<VhcDetailsDTO>> pageBeanDmsResultDTO = applicationMaintainManagementFeign.selectVhcList(queryVhcDto);
        ErrorConversionUtils.errorCodeConversion(pageBeanDmsResultDTO);
        return pageBeanDmsResultDTO.getData();
    }

    @Override
    public PageBean<VhcPricesheetDetailsDTO> selectVhcPricesheetList(QueryVhcDto queryVhcDto) {
        DmsResultDTO<PageBean<VhcPricesheetDetailsDTO>> pageBeanDmsResultDTO = applicationMaintainManagementFeign.selectVhcPricesheetList(queryVhcDto);
        ErrorConversionUtils.errorCodeConversion(pageBeanDmsResultDTO);
        return pageBeanDmsResultDTO.getData();
    }

}
