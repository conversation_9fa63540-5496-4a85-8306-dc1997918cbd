package com.volvo.bff.volvoworks.wechat.common.model.dto.order;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("工单确认")
@Data
public class OrderConfirmVo {

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号",name = "roNo")
    private String roNo;

    /**
     * 经销商
     */
    @ApiModelProperty(value = "经销商",name = "ownerCode")
    private String ownerCode;


    /**
     * 用户号
     */
    @ApiModelProperty(value = "用户号",name = "memberid")
    private String memberid;


    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道",name = "operateChannel")
    private String operateChannel;


    /**
     * 工单类型(工单/结算单)
     */
    @ApiModelProperty(value = "工单类型(工单85901001/结算单85901002/环检单85901003)",name = "orderType")
    private String orderType;


    /**
     * 用户类型
     * app 85801001;
     * 小程序85801002;
     */
    @ApiModelProperty(value = "用户类型(app 85801001,小程序85801002,服务顾问85801003)",name = "userType")
    private String userType;

    /**
     * B端确认结算时选择的客户未从C端确认原因
     */
    @ApiModelProperty(value = "客户未从C端确认原因",name = "confirmReason")
    private String confirmReason;

    /**
     * B端确认结算时填写的描述
     */
    @ApiModelProperty(value = "描述",name = "describes")
    private String describes;

    @ApiModelProperty(value = "结算单号",name = "balanceNos")
    private List<String> balanceNos;

    @ApiModelProperty(value = "结算单号",name = "balanceNo")
    private String balanceNo;
}
