package com.volvo.bff.volvoworks.wechat.common.model.vo.workshop;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("车辆进场信息")
public class VehicleEntranceVo {

    /** 进场车辆上报流水id */
    private String entranceId;

    /** 车牌号 */
    private String licensePlate;

    /** 车架号 */
    private String vin;

    /** 经销商code */
    private String dealerCode;

    /** 客户姓名 */
    private String ownerName;

    /** 客户手机号 */
    private String phone;

    /** 车型 */
    private String modelName;

    /** 进场时间 */
    @JsonFormat(pattern="yyyy/MM/dd HH:mm:ss",timezone="GMT+8")
    private Date entryTime;

}
