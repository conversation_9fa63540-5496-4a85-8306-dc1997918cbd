package com.volvo.bff.volvoworks.wechat.common.service.app;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.*;

import java.util.List;


/**
 * <p>
 * 增修建议表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-01
 */
public interface AdditionalRepairSuggestionService {
    PageBean<QueryRepairingOrderResultVo> queryRepairOrder(QueryStructureCheckRrquestVo queryParams);

    List<AdditionalRepairSuggestionResultVo> queryAdditionalRepairSuggestion(String roNo);

    String saveAdditionalRepairSuggestion(SaveAdditionalRepairSuggestionVo saveParams);

    PageBean<VidaOrderListResultVo> getVidaListByVin(String vin);

    Void deleteAdditionalRepairSuggestion(String itemId);

    AdditionalRepairSuggestionResultVo queryAdditionalRepairSuggestionDetail(String itemId);
}
