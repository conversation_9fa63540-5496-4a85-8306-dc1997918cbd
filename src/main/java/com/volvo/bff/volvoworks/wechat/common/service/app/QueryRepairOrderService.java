package com.volvo.bff.volvoworks.wechat.common.service.app;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.*;

import java.util.List;


public interface QueryRepairOrderService {

    /**
     * @Description 查询所有工单
     * <AUTHOR>
     * @Date create on 2020/7/27
     **/
    PageBean<QueryRepairingOrderResultVo> queryAllRepairOrder(QueryStructureCheckRrquestVo queryParams);

    /**
     * @Description 查询工单明细
     * <AUTHOR>
     * @Date create on 2020/7/27
     **/
    RepairOrderDetailsResultVo queryRepairOrderDetail(RepairOrderDetailParamsVo paramsVo);

    void saveAvailableOneId(RepairOrderAvailableOneIdParamsVo paramsVo);

}
