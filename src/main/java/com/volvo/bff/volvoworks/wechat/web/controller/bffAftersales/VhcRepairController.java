package com.volvo.bff.volvoworks.wechat.web.controller.bffAftersales;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.vhc.QueryVhcDto;
import com.volvo.bff.volvoworks.wechat.common.model.dto.vhc.VhcDetailsDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.vhc.VhcPricesheetDetailsDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.vhc.VhcRepairService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "vhc工单相关控制层")
@RestController
@RequestMapping("/web-VhcRepair/v1")
public class VhcRepairController {

    @Autowired
    private VhcRepairService vhcRepairService;

    /**
     * 检查页面查询
     */
    @ApiOperation(value = "检查页面查询", notes = "检查页面查询", httpMethod = "POST")
    @PostMapping("/selectVhcList")
    public ResponseDTO<PageBean<VhcDetailsDTO>> selectVhcList(@RequestBody QueryVhcDto queryVhcDto){
        return ResponseUtils.success(vhcRepairService.selectVhcList(queryVhcDto));
    }


    @ApiOperation(value = "报价页面查询", notes = "报价页面查询", httpMethod = "POST")
    @PostMapping("/selectVhcPricesheetList")
    public ResponseDTO<PageBean<VhcPricesheetDetailsDTO>> selectVhcPricesheetList(@RequestBody QueryVhcDto queryVhcDto){
        return ResponseUtils.success(vhcRepairService.selectVhcPricesheetList(queryVhcDto));
    }
}
