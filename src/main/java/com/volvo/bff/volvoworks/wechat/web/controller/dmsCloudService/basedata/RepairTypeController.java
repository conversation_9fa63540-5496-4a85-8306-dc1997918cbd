package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.basedata;



import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.basedata.RepairTypeService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(value = "维修类型controller", tags = {"维修类型操作接口"})
@RestController
@RequestMapping("/basedataRepairtypes")
public class RepairTypeController  {

    @Autowired
    private RepairTypeService repairtypeService;


    /**
     * 查询维修类型
     */

    @ApiOperation(value = "查询维修类型", notes = "查询维修类型", httpMethod = "GET")
    @RequestMapping(value = "/findAllRepairType", method = RequestMethod.GET)
    @ResponseBody
    public ResponseDTO<List<Map>> findAllRepairType(@RequestParam(value = "ownerCode",required = false)String ownerCode) {
        List<Map> result = repairtypeService.queryAllRepairType(ownerCode);

        return ResponseUtils.success(result);
    }

}
