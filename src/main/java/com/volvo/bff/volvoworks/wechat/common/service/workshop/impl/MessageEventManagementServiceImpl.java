package com.volvo.bff.volvoworks.wechat.common.service.workshop.impl;


import com.volvo.bff.volvoworks.wechat.common.feign.ApplicationMaintaineventManagementFeign;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.workshop.MessageEventManagementService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MessageEventManagementServiceImpl implements MessageEventManagementService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ApplicationMaintaineventManagementFeign applicationMaintaineventManagementFeign;
    
	@Override
	public Boolean updateMsgById(String id) {
		logger.info("updateMsgById : {}", id);
		DmsResultDTO<Boolean> res = applicationMaintaineventManagementFeign.updateMsgById(id);
        ErrorConversionUtils.errorCodeConversion(res);
		return res.getData();
	}

}
