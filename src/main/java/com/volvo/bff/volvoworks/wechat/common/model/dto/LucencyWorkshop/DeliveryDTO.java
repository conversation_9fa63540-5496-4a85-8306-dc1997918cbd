package com.volvo.bff.volvoworks.wechat.common.model.dto.LucencyWorkshop;

import lombok.Data;

/**
 * 交车dto
 */
@Data
public class DeliveryDTO {

    /**
     * 预计交车时间、姓名、车牌、车型、状态、工单号、服务顾问
     */
    //经销商code
    private String ownerCode;
    //车牌号
    private String license;
    //vin
    private String vin;
    //工单号
    private String roNo;
    //预交车时间
    private String endTimeSupposed;
    //送修人
    private String deliverer;
    //车型
    private String model;
    //工单状态
    private String roStatus;
    //服务顾问
    private String serviceAdvisor;
    //标签
    private String label;



}
