package com.volvo.bff.volvoworks.wechat.common.service.midendauthcenter.impl;

import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.lop.open.api.sdk.internal.fastjson.JSON;
import com.volvo.bff.volvoworks.wechat.common.feign.MidEndAuthCenterFeign;
import com.volvo.bff.volvoworks.wechat.common.model.dto.RequestDto;
import com.volvo.bff.volvoworks.wechat.common.model.dto.midendauthcenter.RoleDealerUserInfoMidRequestDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.midendauthcenter.RoleDealerUserInfoRequestDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.midendauthcenter.RoleDealerUserInfoRespDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.midendauthcenter.RoleDealerService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import com.volvo.bff.volvoworks.wechat.common.utils.TransmittableThreadLocalUtils;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class RoleDealerServiceImpl implements RoleDealerService {
	
	@Resource
	private MidEndAuthCenterFeign midEndAuthCenterFeign;
	
	@Override
	public List<RoleDealerUserInfoRespDTO> queryRoleDealerUser(RoleDealerUserInfoRequestDTO dto) {
		log.info("queryRoleDealerUser-RoleDealerUserInfoRequestDTO: {}", JSON.toJSONString(dto));
		// token 中获取经销商信息
		String ownerCode = TransmittableThreadLocalUtils.getValueBykey("ownerCode");
		
		RoleDealerUserInfoMidRequestDTO roleDealerUserInfoMidRequestDTO = new RoleDealerUserInfoMidRequestDTO();
		BeanUtils.copyProperties(dto, roleDealerUserInfoMidRequestDTO);
		roleDealerUserInfoMidRequestDTO.setCompanyCode(ownerCode);
		RequestDto<RoleDealerUserInfoMidRequestDTO> requestDto = new RequestDto<>();
		requestDto.setData(roleDealerUserInfoMidRequestDTO);
		log.info("queryRoleDealerUser-requestDto: {}", JSON.toJSONString(requestDto));
		DmsResultDTO<List<RoleDealerUserInfoRespDTO>> response = midEndAuthCenterFeign.queryRoleDealerUser(requestDto);
		ErrorConversionUtils.errorCodeConversion(response);
		List<RoleDealerUserInfoRespDTO> data = response.getData();
		
		data.stream().filter(Objects::nonNull).forEach(obj->{
			obj.setIdcardNumber(null);
			obj.setPhone(null);
		});
		return data;
	}
	
}
