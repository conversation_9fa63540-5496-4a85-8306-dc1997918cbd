package com.volvo.bff.volvoworks.wechat.common.service.pauseDetail;


import com.volvo.bff.volvoworks.wechat.common.model.vo.reception.PzusedateilParemVo;

import java.util.List;

/**
 * 维修派工新需求
 *
 * <AUTHOR>
 * @since 2020-05-12
 */
public interface RoPauseDetailservice {
    /**
     * 暂停开始 添加
     * @param zaneDetail
     * @return String
     */
    String paintball(List<PzusedateilParemVo> zaneDetail);
    /**
     * 暂停结束
     * @param zaneDetail
     * @return void
     */
    String paintbox(List<PzusedateilParemVo> zaneDetail);
}
