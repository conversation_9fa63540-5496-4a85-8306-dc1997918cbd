package com.volvo.bff.volvoworks.wechat.common.model.vo.accidentClues;

import lombok.Data;

import java.util.List;

@Data
public class EmpByRoleCodeVO {
    private String afterBigAreaName;
    private String afterSmallAreaName;
    private String companyCode;
    private Integer companyId;
    private String companyNameCn;
    private String companyShortNameCn;
    private String country;
    private Integer gender;
    private String heardImgUrl;
    private String province;
    private String saleBigAreaName;
    private String saleSmallAreaName;
    private Integer score;
    private String unionId;
    private Integer workYears;
    private Long userId;
    private String userCode;
    private String employeeName;
    private String idcardNumber;
    private String phone;
    private String email;
    private String createBy;
    private String createTime;
    private String updateBy;
    private String updateTime;
    private Integer accountStatus;
    private List<String> roleCode;
    private String birthday;
    private String adderss;
    private Integer isOnjob;
    private String wechat;
    private String vcpa;
    private String position;
    private Integer nowClueCount;
}