package com.volvo.bff.volvoworks.wechat.common.model.dto.pds;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@ApiModel("PdsConfigDTO")
public class PdsConfigDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	// 层级
	@ApiModelProperty(name = "pdsLevel")
	public Integer pdsLevel;
	// ID
	@ApiModelProperty(name = "id")
	public Integer id;
	// code
	@ApiModelProperty(name = "pdsCode")
	public String pdsCode;
	// 父级ID
	@ApiModelProperty(name = "parentId")
	public Integer parentId;
	// 排序
	@ApiModelProperty(name = "sort")
	public Integer sort;
	// 文案描述2
	@ApiModelProperty(name = "text2Description")
	public String text2Description;
	// 名称
	@ApiModelProperty(name = "pdsName")
	public String pdsName;
	// 文案描述1
	@ApiModelProperty(name = "text1Description")
	public String text1Description;
	// 描述
	@ApiModelProperty(name = "pdsDescription")
	public String pdsDescription;
	// reserve1
	@ApiModelProperty(name = "reserve1")
	private String reserve1;
	// reserve2
	@ApiModelProperty(name = "reserve2")
	private String reserve2;
	// reserve3
	@ApiModelProperty(name = "reserve3")
	private String reserve3;
}