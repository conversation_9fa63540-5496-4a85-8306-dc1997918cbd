package com.volvo.bff.volvoworks.wechat.common.service.insuranceBusiness.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudCustomerFeign;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.insuranceBusiness.InsuranceService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class InsuranceServiceImpl implements InsuranceService {

    @Autowired
    private DmscloudCustomerFeign dmscloudCustomerFeign;


    @Override
    public List<Map> selectInsurance(String isValid) {
        log.info("selectInsurance isValid:{}", isValid);
        DmsResultDTO<List<Map>> insuranceCompany = dmscloudCustomerFeign.getInsuranceCompany(isValid);
        ErrorConversionUtils.errorCodeConversion(insuranceCompany);
        return insuranceCompany.getData();
    }
}
