package com.volvo.bff.volvoworks.wechat.common.model.dto.pds;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@ApiModel("AddRsqDTO")
public class AddRsqDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	// pdsID
	@ApiModelProperty(name = "pdsInfoId")
	private Integer pdsInfoId;
	// 是否强制完成
	@ApiModelProperty(name = "mandatoryCompletion")
	private Integer mandatoryCompletion;
	// 强制完成原因
	@ApiModelProperty(name = "mandatoryDescription")
	private String mandatoryDescription;
	// 里程
	@ApiModelProperty(name = "mileage", required = true)
	private Integer mileage;
	// ocr识别车架号path
	@ApiModelProperty(name = "ocrVin", required = true)
	private String ocrVin;
	// pds状态
	@ApiModelProperty(name = "pdsStatus", required = true)
	private Integer pdsStatus;
	// 车架号
	@ApiModelProperty(name = "vin", required = true)
	private String vin;
	// pds车辆标记图片
	@ApiModelProperty(name = "pdsDataPath")
	private String pdsDataPath;
	// reserve1
	@ApiModelProperty(name = "pdsInfoReserve1")
	private String pdsInfoReserve1;
	// reserve2
	@ApiModelProperty(name = "pdsInfoReserve2")
	private String pdsInfoReserve2;
	// reserve3
	@ApiModelProperty(name = "pdsInfoReserve3")
	private String pdsInfoReserve3;

	// pds资料信息
	@ApiModelProperty(name = "pdsData")
	private List<PdsDataDTO> pdsData;
	// pds项目
	@ApiModelProperty(name = "pdsItem")
	private List<PdsItemDTO> pdsItem;
}