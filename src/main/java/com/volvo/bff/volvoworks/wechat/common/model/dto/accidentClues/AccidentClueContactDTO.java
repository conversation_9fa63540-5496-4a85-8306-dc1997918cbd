package com.volvo.bff.volvoworks.wechat.common.model.dto.accidentClues;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AccidentClueContactDTO {

  @ApiModelProperty(value = "联系人")
  private String contacts;

  @ApiModelProperty(value = "手机号")
  private String contactsPhone;

  @ApiModelProperty(value = "客户ID")
  private Long oneId;

  @ApiModelProperty(value = "acId")
  private Long acId;

  @ApiModelProperty(value = "id")
  private Long id;

  @ApiModelProperty(value = "原联系人")
  private String originalContacts;

  @ApiModelProperty(value = "原手机号")
  private String originalContactsPhone;
}
