package com.volvo.bff.volvoworks.wechat.common.model.dto.pds;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@ApiModel("PdsItemDTO")
public class PdsItemDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	// pds item id
	@ApiModelProperty(name = "pdsItemId")
	private Integer pdsItemId;
	// 检查项code
	@ApiModelProperty(name = "pdsCode")
	private String pdsCode;
	// 检查项name
	@ApiModelProperty(name = "pdsName")
	private String pdsName;
	// 检查结果 字典 3006
	@ApiModelProperty(name = "selectValue")
	private Integer selectValue;
	// 文字填写描述项1
	@ApiModelProperty(name = "text1Description")
	private String text1Description;
	// 文字填写描述项2
	@ApiModelProperty(name = "text2Description")
	private String text2Description;
	// 问题原因描述
	@ApiModelProperty(name = "abnormalDescription")
	private String abnormalDescription;
	// reserve1
	@ApiModelProperty(name = "pdsItemReserve1")
	private String pdsItemReserve1;
	// reserve2
	@ApiModelProperty(name = "pdsItemReserve2")
	private String pdsItemReserve2;
	// reserve3
	@ApiModelProperty(name = "pdsItemReserve3")
	private String pdsItemReserve3;

	// pds项目资料path
	@ApiModelProperty(name = "pdsItemData")
	private List<ParentItemDataDTO> pdsItemData;
}