package com.volvo.bff.volvoworks.wechat.common.model.vo.basedata;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper=false)
@Data
public class RestResultResponse <T> extends ResultBean {

    T data;
    boolean success;

    public RestResultResponse() {
        super();
    }


    public RestResultResponse(Integer resultCode, String errorMessage) {
        this.setCode(resultCode);
        this.setResultCode(resultCode);
        this.setErrorMessage(errorMessage);
        this.setSuccess(false);
    }

    public boolean isSuccess() {
        return success;
    }

    public RestResultResponse<T> success(boolean success) {
        this.setSuccess(success);
        return this;
    }

    public RestResultResponse<T> data(T data) {
        this.setResultCode(200);
        this.setCode(200);
        this.setSuccess(true);
        this.setData(data);
        return this;
    }
}