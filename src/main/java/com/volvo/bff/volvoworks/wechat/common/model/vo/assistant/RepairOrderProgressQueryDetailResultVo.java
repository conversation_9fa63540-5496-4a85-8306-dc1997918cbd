package com.volvo.bff.volvoworks.wechat.common.model.vo.assistant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020-03-13
 */
@Data
@ApiModel("进度查询工单明细结果Vo")
public class RepairOrderProgressQueryDetailResultVo {

    @ApiModelProperty(value = "工单状态",name = "roStatus")
    private Integer roStatus;
    @ApiModelProperty(value = "车主名称",name = "ownerName")
    private String ownerName;
    @ApiModelProperty(value = "车主编号",name = "ownerNo")
    private String ownerNo;
    @ApiModelProperty(value = "工单号",name = "roNo")
    private String roNo;
    @ApiModelProperty(value = "工单创建时间",name = "roCreateDate")
    private Date roCreateDate;
    @ApiModelProperty(value = "车牌号",name = "license")
    private String license;
    @ApiModelProperty(value = "服务顾问",name = "serviceAdvisor")
    private String serviceAdvisor;
    @ApiModelProperty(value = "指定技师",name = "chiefTechnician")
    private String chiefTechnician;
    @ApiModelProperty(value = "手机号",name = "delivererMobile")
    private String delivererMobile;
    @ApiModelProperty(value = "车架号",name = "vin")
    private String vin;
    @ApiModelProperty(value = "送修人",name = "deliverer")
    private String deliverer;
    @ApiModelProperty(value = "送修人电话",name = "delivererPhone")
    private String delivererPhone;
    @ApiModelProperty(value = "故障描述",name = "customerDesc")
    private String customerDesc;
    @ApiModelProperty(value = "完工状态",name = "completeTag")
    private String completeTag;







}
