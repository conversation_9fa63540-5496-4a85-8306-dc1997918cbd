package com.volvo.bff.volvoworks.wechat.common.model.vo.accidentClues;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@ApiModel("全网经销商查询")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DealerVO implements Serializable {
    private static final long serialVersionUID = 53432435L;
    private String returnCode;
    private String returnMessage;
    private DealerPageInfo data;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class DealerPageInfo implements Serializable {
        private static final long serialVersionUID = 78192255L;
        private Integer current;
        private Integer pages;
        private Integer size;
        private Integer total;
        private List<DealerInfo> records;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class DealerInfo implements Serializable {
        private static final long serialVersionUID = 85246254L;
        private String companyCode;
        private String companyNameCn;
    }
}
