package com.volvo.bff.volvoworks.wechat.common.service.stockmanage.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import com.volvo.bff.volvoworks.wechat.common.service.stockmanage.PartBuyService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@Service
public class PartBuyServiceImpl  implements PartBuyService {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Override
    public PageInfoDto queryStockInOrder(Map<String, String> queryParam){
        DmsResultDTO<PageInfoDto> response = dmscloudServiceFeign.queryClueRepairOrder(queryParam);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    /**
     * 配件采购入库-具体单据明细数据查询
     *
     * @param queryParam
     * @return
     */
    @Override
    public List<Map> queryOrderDetail(Map<String, String> queryParam) {
        DmsResultDTO<List<Map>> response = dmscloudServiceFeign.queryOrderDetail(queryParam);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }
}
