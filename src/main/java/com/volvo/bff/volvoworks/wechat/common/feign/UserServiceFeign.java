package com.volvo.bff.volvoworks.wechat.common.feign;

import com.volvo.bff.volvoworks.wechat.common.model.dto.login.MidUserDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.RestResultResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(name = "user",
        url = "${mse-in.url.user}")
public interface UserServiceFeign {
    @GetMapping({"/user/info/new"})
    RestResultResponse<MidUserDto> getUserInfo();
}
