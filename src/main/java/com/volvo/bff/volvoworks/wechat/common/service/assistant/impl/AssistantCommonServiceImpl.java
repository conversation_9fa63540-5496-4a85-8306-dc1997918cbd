package com.volvo.bff.volvoworks.wechat.common.service.assistant.impl;


import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.RepairHistoryResultVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.assistant.AssistantCommonService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 维修历史
 *
 * <AUTHOR>
 * @since 2020-03-30
 */
@Service
public class AssistantCommonServiceImpl implements AssistantCommonService {

    @Resource
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Override
    public List<RepairHistoryResultVo> queryRepairHistory(String vin) {
        DmsResultDTO<List<RepairHistoryResultVo>> res = dmscloudServiceFeign.queryRepairHistory(vin);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

}
