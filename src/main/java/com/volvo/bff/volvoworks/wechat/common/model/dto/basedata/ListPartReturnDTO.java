package com.volvo.bff.volvoworks.wechat.common.model.dto.basedata;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ListPartReturnDTO {
    private String stockReturnNo;	//退货单号
    private String customerCode;	//业务客户编号
    private String customerName;	//业务客户名称
    private String returnDate;	//退货时间
    private Integer returnStatus;	//退货状态
    private BigDecimal returnQuantity;	//退货零件数量
    private BigDecimal returnAmount;	//退货金额
    private String originalNo;	//进货单号
    private String returnCause;	//退货原因
    private List<PartReturnItemDTO> dms_table;  //表格数据
    private String delItems;  //删除记录itemId;以,隔开

    //网络->NewBie 需要经销商代码
    private String ownerCode;

    //网络->NewBie 网络入库单号 sc in开头的单号
    private String stockInNo;

    //单据类型 0采购退货单/1差异索赔单
    private Integer orderType;

    //出库标识 1实出/0虚出
    private Integer outFlag;

    //业务类型  少发D00/多发D1011
    private String businessType;

    //京东出货单号
    private String jdShipNo;

    //差异索赔单号
    private String diffNo;

    private Integer isAPP;

    /*溯源码pc出库原因*/
    private String reason;




}

