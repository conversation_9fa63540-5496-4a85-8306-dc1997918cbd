package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.app;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.app.DamagedPartService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 定损维护接口
 *
 * <AUTHOR>
 * @date 2020年5月30日
 */
@Api(value = "/damagedPart", tags = {"定损维护接口"})
@RestController
@RequestMapping("/damagedPart")
public class DamagedPartController {

    @Autowired
    DamagedPartService damagedPartService;

    /**
     * 查询估价单
     * @return
     */
    @ApiOperation(value = "查询估价单", notes = "查询估价单", httpMethod = "GET")
    @GetMapping("/queryEstimate")
    public ResponseDTO<PageBean<DamagedEstimateOrderResultVo>> queryEstimateForDamaged(DamagedEstimateOrderParamVo damagedEstimateOrderParamVo){
        return ResponseUtils.success(damagedPartService.queryEstimateForDamaged(damagedEstimateOrderParamVo));
    }

    /**
     * 查询级联部件
     * @return
     */
    @ApiOperation(value = "查询级联部件", notes = "查询级联部件", httpMethod = "GET")
    @GetMapping("/queryDamagedPartList")
    public ResponseDTO<List<DamagedPartResultDto>> queryDamagedPartList(String estimateNo, String locId){
        return ResponseUtils.success(damagedPartService.queryDamagedPartList(estimateNo,locId));
    }

    /**
     * 保存定损维护
     * @return
     */

    @ApiOperation(value = "定损维护部件保存", notes = "定损维护部件保存", httpMethod = "POST")
    @PostMapping("/saveDamaged")
    public ResponseDTO<String> saveDamaged(@RequestBody SaveDamagedPartVo saveDamagedPartVo){
        return ResponseUtils.success(damagedPartService.saveDamaged(saveDamagedPartVo));
    }

    /**
     * 查询定损维护部位结果
     * @param saveDamagedPartVo
     * @return
     */
    @ApiOperation(value = "查询定损维护部位结果", notes = "查询定损维护部位结果", httpMethod = "GET")
    @GetMapping("/queryDamagedPart")
    public ResponseDTO<SaveDamagedPartVo> queryDamagedPart(SaveDamagedPartVo saveDamagedPartVo){
        return ResponseUtils.success(damagedPartService.queryDamagedPart(saveDamagedPartVo));
    }


    @ApiOperation(value = "查询定损维护部位结果", notes = "查询定损维护部位结果", httpMethod = "GET")
    @GetMapping("/queryEstimateDetail")
    public ResponseDTO<PageBean<EstimateDetailResultVo>> queryEstimateDetail(@RequestParam("estimateNo") String estimateNo){
        return ResponseUtils.success(damagedPartService.queryEstimateDetail(estimateNo));
    }


    @ApiOperation(value = "删除定损维护部位", notes = "删除定损维护部位", httpMethod = "GET")
    @GetMapping("/deleteEstimate")
    public ResponseDTO<String> deleteEstimate(@RequestParam("estimateNo") String estimateNo,@RequestParam("locId") String locId){
        return ResponseUtils.success(damagedPartService.deleteEstimate(estimateNo,locId));
    }


}
