package com.volvo.bff.volvoworks.wechat.common.model.dto.login;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

public class RoleLoginVO implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("角色Id")
    private Integer roleId;
    @ApiModelProperty("角色代码")
    private String roleCode;
    @ApiModelProperty("角色名称")
    private String roleName;
    @ApiModelProperty("是否关键岗位")
    private Integer isValid;

    public RoleLoginVO() {
    }

    public Integer getRoleId() {
        return this.roleId;
    }

    public String getRoleCode() {
        return this.roleCode;
    }

    public String getRoleName() {
        return this.roleName;
    }

    public Integer getIsValid() {
        return this.isValid;
    }

    public void setRoleId(final Integer roleId) {
        this.roleId = roleId;
    }

    public void setRoleCode(final String roleCode) {
        this.roleCode = roleCode;
    }

    public void setRoleName(final String roleName) {
        this.roleName = roleName;
    }

    public void setIsValid(final Integer isValid) {
        this.isValid = isValid;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof RoleLoginVO)) {
            return false;
        } else {
            RoleLoginVO other = (RoleLoginVO)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label59: {
                    Object this$roleId = this.getRoleId();
                    Object other$roleId = other.getRoleId();
                    if (this$roleId == null) {
                        if (other$roleId == null) {
                            break label59;
                        }
                    } else if (this$roleId.equals(other$roleId)) {
                        break label59;
                    }

                    return false;
                }

                Object this$roleCode = this.getRoleCode();
                Object other$roleCode = other.getRoleCode();
                if (this$roleCode == null) {
                    if (other$roleCode != null) {
                        return false;
                    }
                } else if (!this$roleCode.equals(other$roleCode)) {
                    return false;
                }

                Object this$roleName = this.getRoleName();
                Object other$roleName = other.getRoleName();
                if (this$roleName == null) {
                    if (other$roleName != null) {
                        return false;
                    }
                } else if (!this$roleName.equals(other$roleName)) {
                    return false;
                }

                Object this$isValid = this.getIsValid();
                Object other$isValid = other.getIsValid();
                if (this$isValid == null) {
                    if (other$isValid != null) {
                        return false;
                    }
                } else if (!this$isValid.equals(other$isValid)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof RoleLoginVO;
    }

    public int hashCode() {
        boolean PRIME = true;
        int result = 1;
        Object $roleId = this.getRoleId();
        result = result * 59 + ($roleId == null ? 43 : $roleId.hashCode());
        Object $roleCode = this.getRoleCode();
        result = result * 59 + ($roleCode == null ? 43 : $roleCode.hashCode());
        Object $roleName = this.getRoleName();
        result = result * 59 + ($roleName == null ? 43 : $roleName.hashCode());
        Object $isValid = this.getIsValid();
        result = result * 59 + ($isValid == null ? 43 : $isValid.hashCode());
        return result;
    }

    public String toString() {
        return "RoleLoginVO(roleId=" + this.getRoleId() + ", roleCode=" + this.getRoleCode() + ", roleName=" + this.getRoleName() + ", isValid=" + this.getIsValid() + ")";
    }
}
