package com.volvo.bff.volvoworks.wechat.common.model.dto.vhc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 车辆健康检查详情入参DTO
 * @Date 2024/9/25 16:17
 */
@Data
@ApiModel("车辆详情查询入参")
public class VehicleHealthCheckDetailParamDto {

    @ApiModelProperty("工单号")
    private String roNo;

    @ApiModelProperty("车辆健康检查编号")
    private String vhcNo;

    @ApiModelProperty("经销商")
    private String ownerCode;

}
