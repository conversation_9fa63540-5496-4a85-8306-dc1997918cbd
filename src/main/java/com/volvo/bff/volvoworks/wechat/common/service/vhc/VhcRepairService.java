package com.volvo.bff.volvoworks.wechat.common.service.vhc;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.vhc.QueryVhcDto;
import com.volvo.bff.volvoworks.wechat.common.model.dto.vhc.VhcDetailsDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.vhc.VhcPricesheetDetailsDTO;

public interface VhcRepairService {

    //检查页面查询
    PageBean<VhcDetailsDTO> selectVhcList(QueryVhcDto queryVhcDto);

    //报价页面查询
    PageBean<VhcPricesheetDetailsDTO> selectVhcPricesheetList(QueryVhcDto queryVhcDto);
}
