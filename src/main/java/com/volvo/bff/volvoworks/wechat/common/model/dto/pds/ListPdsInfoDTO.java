package com.volvo.bff.volvoworks.wechat.common.model.dto.pds;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

@Data
@ToString
@ApiModel("ListPdsInfoDTO")
public class ListPdsInfoDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	// ID
	@ApiModelProperty(name = "id")
	private Integer id;
	// 经销商名称
	@ApiModelProperty(name = "companyName")
	private String companyName;
	// 是否强制完成
	@ApiModelProperty(name = "mandatoryCompletion")
	private Integer mandatoryCompletion;
	// 经销商code
	@ApiModelProperty(name = "ownerCode")
	private String ownerCode;
	// 初检完成时间
	@ApiModelProperty(name = "firstCheckTime")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String firstCheckTime;
	// 终检技师ID
	@ApiModelProperty(name = "lastSaId")
	private Integer lastSaId;
	// 里程
	@ApiModelProperty(name = "mileage")
	private Integer mileage;
	// 创建时间
	@ApiModelProperty(name = "createTime")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String createTime;
	// 强制完成原因
	@ApiModelProperty(name = "mandatoryDescription")
	private String mandatoryDescription;
	// 车架号
	@ApiModelProperty(name = "vin")
	private String vin;
	// 创建人技师ID
	@ApiModelProperty(name = "createSaId")
	private Integer createSaId;
	// 创建人技师名称
	@ApiModelProperty(name = "createBy")
	private String createBy;
	// 初检技师名称
	@ApiModelProperty(name = "firstSaName")
	private String firstSaName;
	// ocr识别车架号path
	@ApiModelProperty(name = "ocrVin")
	private String ocrVin;
	// 车型主图path
	@ApiModelProperty(name = "vinImage")
	private String vinImage;
	// 车型name
	@ApiModelProperty(name = "modeName")
	private String modeName;
	// 终检技师名称
	@ApiModelProperty(name = "lastSaName")
	private String lastSaName;
	// 终检完成时间
	@ApiModelProperty(name = "lastCheckTime")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String lastCheckTime;
	// 初检技师ID
	@ApiModelProperty(name = "firstSaId")
	private Integer firstSaId;
	// 车牌号
	@ApiModelProperty(name = "license")
	private String license;
	// pds状态
	@ApiModelProperty(name = "pdsStatus")
	private Integer pdsStatus;
	// 车型code
	@ApiModelProperty(name = "modeCode")
	private String modeCode;
	// pds单号
	@ApiModelProperty(name = "pdsNo")
	private String pdsNo;
	// pds车辆标记图片
	@ApiModelProperty(name = "pdsDataPath")
	private String pdsDataPath;
	// reserve1
	@ApiModelProperty(name = "reserve1")
	private String reserve1;
	// reserve2
	@ApiModelProperty(name = "reserve2")
	private String reserve2;
	// reserve3
	@ApiModelProperty(name = "reserve3")
	private String reserve3;

	// 大区id
	@ApiModelProperty(name = "bigAreaId")
	private Integer bigAreaId;
	// 小区id
	@ApiModelProperty(name = "smallAreaId")
	private Integer smallAreaId;
	// 集团id
	@ApiModelProperty(name = "groupCodeId")
	private Integer groupCodeId;
	// 大区名称
	@ApiModelProperty(name = "bigAreaName")
	private String bigAreaName;
	// 小区名称
	@ApiModelProperty(name = "smallAreaName")
	private String smallAreaName;
	// 集团名称
	@ApiModelProperty(name = "groupCodeName")
	private String groupCodeName;
}