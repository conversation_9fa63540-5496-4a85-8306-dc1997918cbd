package com.volvo.bff.volvoworks.wechat.common.model.dto.accidentClues;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * SA呼叫登记
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Data
public class AccidentCluesSaNumberDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 主键ID
     */
    private Long id;


    private Long acId;

    /**
     * 跟进Id
     */
    private Long followId;

    /**
     * call_id
     */
    private String callId;

    /**
     * 服务顾问ID
     */
    private String saId;

    /**
     * 客户名称
     */
    private String cusName;

    /**
     * 客户电话
     */
    private String cusNumber;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;



    /**
     * 经销商代码
     */
    private String dealerCode;


    /**
     * 服务顾问姓名
     */
    private String saName;

    /**
     * 服务顾问手机号
     */
    private String saNumber;

    /**
     * AI语音工作号
     */
    private String workNumber;
}
