package com.volvo.bff.volvoworks.wechat.common.service.pad.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.pad.PadCustomerRequireResultAllVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.pad.PadCustomerRequireVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.pad.PadPreviewInteriorVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.pad.SurroundCheckVO;
import com.volvo.bff.volvoworks.wechat.common.service.pad.SurroundCheckService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Service
public class SurroundCheckServiceImpl implements SurroundCheckService {

    @Resource
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Override
    public String saveSurroundCheck1(SurroundCheckVO surroundCheckVO) {
        DmsResultDTO<String> response = dmscloudServiceFeign.saveSuroundCheck1(surroundCheckVO);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public String saveSurroundCheck2(PadPreviewInteriorVo appearanceCheckVO) {
        DmsResultDTO<String> response = dmscloudServiceFeign.saveSuroundCheck2(appearanceCheckVO);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public String savePreviewInterior(PadPreviewInteriorVo appearanceCheckVO) {
        DmsResultDTO<String> response = dmscloudServiceFeign.saveSuroundCheckInterior(appearanceCheckVO);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public String savePreviewCustomerRequire(PadCustomerRequireVo padCustomerRequireVo) {
        DmsResultDTO<String> response = dmscloudServiceFeign.savePreviewCustomerRequire(padCustomerRequireVo);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public String saveSendBaseFile(Map<String,String> map) {
        DmsResultDTO<String> response = dmscloudServiceFeign.saveSendBaseFile(map);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }
}
