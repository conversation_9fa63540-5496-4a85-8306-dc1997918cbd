package com.volvo.bff.volvoworks.wechat.common.model.dto.vhc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 车辆健康检查所有信息
 * @Date 2024/10/10 17:45
 */
@Data
@ApiModel("车辆健康检查所有信息")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VehicleHealthCheckInfoDto {

    @ApiModelProperty("检查员")
    private String technician;

    @ApiModelProperty("工单号")
    private String roNo;

    @ApiModelProperty("vhc单号")
    private String vhcNo;

    @ApiModelProperty("经销商")
    private String ownerCode;

    @ApiModelProperty("是否完成标识（0：临时保存，1：完成车辆健康检查")
    private String compleFlag;

    @ApiModelProperty("检查大类小类信息")
    private List<VhcClassInfoDto> vhcClassInfoDtoList;
}
