package com.volvo.bff.volvoworks.wechat.common.service.applicationMaintenanceentertain;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.applicationMaintenanceentertain.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;

public interface IVehicleReceptionEntranceService {

    /**
     * 根据经销商code查询进场车辆列表
     * @param pageSize
     * @param pageIndex
     * @param dealerCode
     * @return
     */
    DmsResultDTO<PageBean<VehicleEntranceVO>> queryVehicleEntranceList(Integer pageSize, Integer pageIndex, String dealerCode);


    /**
     * 更新车辆列表
     * @param vehicleEntrance
     * @return
     */
    DmsResultDTO<Boolean> updateVehicleReception(VehicleEntranceVO vehicleEntrance);

    /**
     * 校验车辆信息
     * @return
     */
    DmsResultDTO<Boolean> checkVehicleReception(CheckVehicleEntranceReqVO checkVehicleEntranceReq);

    /**
     * 根据车牌号查询车辆进场信息
     * @param licensePlate
     * @return
     */
    DmsResultDTO<VehicleEntranceVO> queryVehicleEntranceByLicensePlate(String licensePlate);

    /**
     * 根据车牌号查询两天内快速接待数据
     * @param licensePlate
     * @return
     */
    DmsResultDTO<SubmitEntranceInfoReqVO> queryReceptionEntranceByLicensePlate(String licensePlate);

    /**
     * 提交 快速服务/特殊车辆登记
     * @param submitEntranceInfoReq
     * @return
     */
    DmsResultDTO<Boolean> submitEntranceInfo(SubmitEntranceInfoReqVO submitEntranceInfoReq);

    /**
     * 查询白名单
     * @param dealerCode
     * @return
     */
    DmsResultDTO<Boolean> queryWhiteList(String dealerCode);
}
