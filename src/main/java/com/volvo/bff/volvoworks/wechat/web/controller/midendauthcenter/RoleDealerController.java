package com.volvo.bff.volvoworks.wechat.web.controller.midendauthcenter;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.volvo.bff.volvoworks.wechat.common.model.dto.midendauthcenter.RoleDealerUserInfoRequestDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.midendauthcenter.RoleDealerUserInfoRespDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.midendauthcenter.RoleDealerService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Api("/roleDealer/v1")
@Slf4j
@RestController
@RequestMapping("/roleDealer/v1")
public class RoleDealerController {

    @Autowired
    private RoleDealerService roleDealerService;

    @ApiOperation(value = "根据角色代码查询本店员工", notes = "根据角色代码查询本店员工", httpMethod = "POST")
    @PostMapping(value = "/user")
    public ResponseDTO<List<RoleDealerUserInfoRespDTO>> queryRoleDealerUser(@RequestBody RoleDealerUserInfoRequestDTO dto) {
        log.info("queryRoleDealerUser:{}", JSON.toJSONString(dto));
        return ResponseUtils.success(roleDealerService.queryRoleDealerUser(dto));
    }
}
