package com.volvo.bff.volvoworks.wechat.common.feign;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.*;
import com.volvo.bff.volvoworks.wechat.common.model.dto.part.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * 经销商贷款 feign 调用接口
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@FeignClient(name = "application-purchase-management",
url = "${mse-in.tob.application-purchase-management.url}",
path = "${mse-in.tob.application-purchase-management.path}")
public interface ApplicationPurchaseManagementFeign {


}
