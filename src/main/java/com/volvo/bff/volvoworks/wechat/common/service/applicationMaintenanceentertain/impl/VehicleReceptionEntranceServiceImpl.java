package com.volvo.bff.volvoworks.wechat.common.service.applicationMaintenanceentertain.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.ApplicationMaintenanceentertainFeign;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.applicationMaintenanceentertain.CheckVehicleEntranceReqVO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.applicationMaintenanceentertain.SubmitEntranceInfoReqVO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.applicationMaintenanceentertain.VehicleEntranceVO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.applicationMaintenanceentertain.IVehicleReceptionEntranceService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class VehicleReceptionEntranceServiceImpl implements IVehicleReceptionEntranceService {

    @Resource
    private ApplicationMaintenanceentertainFeign applicationMaintenanceentertainFeign;
    @Override
    public DmsResultDTO<PageBean<VehicleEntranceVO>>  queryVehicleEntranceList(Integer pageSize, Integer pageIndex, String dealerCode) {
        DmsResultDTO<PageBean<VehicleEntranceVO>> res = applicationMaintenanceentertainFeign.queryVehicleEntranceList(pageSize, pageIndex, dealerCode);
        ErrorConversionUtils.errorCodeConversion(res);
        return res;
    }

    @Override
    public  DmsResultDTO<Boolean>  updateVehicleReception(VehicleEntranceVO vehicleEntrance) {
        DmsResultDTO<Boolean> res = applicationMaintenanceentertainFeign.updateVehicleReception(vehicleEntrance);
        ErrorConversionUtils.errorCodeConversion(res);
        return res;
    }

    @Override
    public  DmsResultDTO<Boolean>  checkVehicleReception(CheckVehicleEntranceReqVO checkVehicleEntranceReq) {
        DmsResultDTO<Boolean> res = applicationMaintenanceentertainFeign.checkVehicleReception(checkVehicleEntranceReq);
        ErrorConversionUtils.errorCodeConversion(res);
        return res;
    }

    @Override
    public  DmsResultDTO<VehicleEntranceVO>  queryVehicleEntranceByLicensePlate(String licensePlate) {
        DmsResultDTO<VehicleEntranceVO> res = applicationMaintenanceentertainFeign.queryVehicleEntranceByLicensePlate(licensePlate);
        ErrorConversionUtils.errorCodeConversion(res);
        return res;
    }

    @Override
    public  DmsResultDTO<SubmitEntranceInfoReqVO>  queryReceptionEntranceByLicensePlate(String licensePlate) {
        DmsResultDTO<SubmitEntranceInfoReqVO> res = applicationMaintenanceentertainFeign.queryReceptionEntranceByLicensePlate(licensePlate);
        ErrorConversionUtils.errorCodeConversion(res);
        return res;
    }

    @Override
    public  DmsResultDTO<Boolean>  submitEntranceInfo(SubmitEntranceInfoReqVO submitEntranceInfoReq) {
        DmsResultDTO<Boolean> res = applicationMaintenanceentertainFeign.submitEntranceInfo(submitEntranceInfoReq);
        ErrorConversionUtils.errorCodeConversion(res);
        return res;
    }

    @Override
    public  DmsResultDTO<Boolean>  queryWhiteList(String dealerCode) {
        DmsResultDTO<Boolean> res = applicationMaintenanceentertainFeign.queryWhiteList(dealerCode);
        ErrorConversionUtils.errorCodeConversion(res);
        return res;
    }
}
