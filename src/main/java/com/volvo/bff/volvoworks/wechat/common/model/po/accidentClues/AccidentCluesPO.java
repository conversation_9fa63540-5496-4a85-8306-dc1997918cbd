package com.volvo.bff.volvoworks.wechat.common.model.po.accidentClues;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 事故线索
 */
@Data
public class AccidentCluesPO {

  private String appId;

  private String ownerCode;


  private String ownerParCode;

  private Integer orgId;

  /**
   * 主键ID
   */
  private Integer acId;

  private List<Integer> acIdList;

  /**
   * 经销商代码
   */
  private String dealerCode;

  /**
   * 车牌号
   */
  private String license;

  /**
   * vin
   */
  private String vin;

  /**
   * 车型id
   */
  private String modelsId;

  /**
   * 保险公司id
   */
  private String insuranceCompanyId;

  /**
   * 线索来源
   */
  private Integer cluesResource;

  /**
   * 联系人
   */
  private String contacts;

  /**
   * 联系人电话
   */
  private String contactsPhone;

  /**
   * 报案时间
   */

  private String reportDate;

  /**
   * 事故地点
   */
  private String accidentAddress;

  /**
   * 事故类型
   */
  private Integer accidentType;

  /**
   * 是否有人受伤
   */
  private Integer isBruise;

  /**
   * 外拓费用
   */

  private BigDecimal outsideAmount;

  /**
   * 跟进人员id
   */

  private Integer followPeople;

  /**
   * 备注
   */
  private String remark;

  /**
   * 是否本店承保
   */
  private Integer isInsured;

  /**
   * 事故责任
   */
  private Integer accidentDuty;

  /**
   * 跟进状态
   */
  private Integer followStatus;

  /**
   * 线索状态
   */
  private Integer cluesStatus;

  /**
   * 进厂经销商
   */
  private String intoDealerCode;

  /**
   * 进厂时间
   */

  private String intoDealerDate;

  /**
   * 进厂工单号
   */
  private String intoRoNo;

  /**
   * 下次跟进时间
   */

  private String nextFollowDate;

  /**
   * 是否预约
   */
  private Integer isAppointment;

  /**
   * 跟进次数
   */

  private Integer followCount;

  /**
   * 线索类型
   */

  private String cluesType;

  //数据来源

  private Integer dataSources;

  //是否删除，1：删除，0：未删除"

  private Integer isDeleted;

  //是否有效"

  private Integer isValid;

  //创建时间"
  private String createdAt;

  //创建人"
  private String createdBy;

  private String updatedAt;

  //更新人

  private String updatedBy;

  //版本号（乐观锁）

  private Integer recordVersion;


  private String reportDateStart;


  private String reportDateEnd;


  private String createdDateStart;


  private String createdDateEnd;


  private String nextFollowDateStart;

  private String nextFollowDateEnd;

  private Integer doubleAccident;

  private String  insuranceCompanyName;

  private String  followPeopleName;


  private Long    afterBigAreaId;

  private String  afterBigAreaName;

  private Long    afterSmallAreaId;

  private String  afterSmallAreaName;

//店端/厂端（vcdc）
  private String source;


  private Integer allotStatus;


  private Integer oneId;



  private String appointmentIntoDate;

  private String pictures;

  /**
   * 预约单号
   */
  private String bookingOrderNo;

  @ApiModelProperty(value = "线索ID")
  @JsonSerialize(using = ToStringSerializer.class)
  private Long crmId;

  @ApiModelProperty(value = "报案号")
  private String registNo;

  @ApiModelProperty(value = "是否重复案件")
  private String repeatLead;

  @ApiModelProperty(value = "出险时间")
  private String accidentDate;

  @ApiModelProperty(value = "出险内容")
  private String accidentReason;

  @ApiModelProperty(value = "客户类型")
  private String contactsName;

  @ApiModelProperty(value = "进线类型")
  private String callType;

  @ApiModelProperty(value = "是否报警")
  private String callPoliceFlag;

  @ApiModelProperty(value = "创建渠道,YB，400，Newbie")
  private String sourceChannel;

  @ApiModelProperty(value="渠道标签")
  private String channelType;


  @ApiModelProperty("车型名称")
  private String modelName;


  @ApiModelProperty("车主名")
  private String ownerName;


  @ApiModelProperty("车主电话")
  private String ownerMobile;


  @ApiModelProperty("送返修标识,送修/返修")
  private String clientType;


  @ApiModelProperty("首次跟进时间")
  private String firstFollowTime;


  @ApiModelProperty("二次跟进时间")
  private String lastFollowTime;


  @ApiModelProperty("跟进内容")
  private String followText;


  @ApiModelProperty("跟进失败原因")
  private String followFailWhy;


  @ApiModelProperty("是否虚拟号码")
  private Integer virtualPhoneFlag;


  private List<Integer> cluesStatusList;


  private List<Integer> followStatusList;


  private List<Integer> cluesResourceList;


  private String insuranceSource;


  private String provinceName;

  private String cityName;

  private Integer dataStatus;

  private Long    provinceId;

  private Long    cityId;

  private String    dataStatusRemark;


}
