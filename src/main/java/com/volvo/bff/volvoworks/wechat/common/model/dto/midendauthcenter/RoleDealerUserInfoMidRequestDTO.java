package com.volvo.bff.volvoworks.wechat.common.model.dto.midendauthcenter;


import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("查询入参")
@Data
public class RoleDealerUserInfoMidRequestDTO {

    /**
     * 在职状态 10081001:在职 , 10081002:离职
     */
    @ApiModelProperty(value = "在职状态 10081001:在职 , 10081002:离职",name = "isOnjob")
    private String isOnjob;
    
    /**
     * 人员姓名
     */
    @ApiModelProperty(value = "人员姓名",name = "employeeName")
    private String employeeName;
    
    /**
     * 经销商代码
     */
    @ApiModelProperty(value = "经销商代码",name = "companyCode")
    private String companyCode;
    
    /**
     * 角色代码
     */
    @ApiModelProperty(value = "角色代码",name = "roleCode")
    private List<String> roleCode;
    
}
