package com.volvo.bff.volvoworks.wechat.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
@Component
public class ApplicationContextHolder  implements ApplicationContextAware {
    private static final Logger log = LoggerFactory.getLogger(ApplicationContextHolder.class);
    private static List<ApplicationContext> applicationContextList = new ArrayList();

    public ApplicationContextHolder() {
    }

    public static ApplicationContext getApplicationContext() {
        return (ApplicationContext)applicationContextList.get(0);
    }

    public void setApplicationContext(ApplicationContext applicationContext) {
        log.info("执行了Bean 的初始化");
        applicationContextList.add(applicationContext);
    }

    public static Object getBeanByName(String name) {
        return getApplicationContext().getBean(name);
    }

    public static <T> T getBeanByType(Class<T> beanType) {
        try {
            return getApplicationContext().getBean(beanType);
        } catch (BeansException var2) {
            return null;
        }
    }

    public static <T> T getBeanByName(String name, Class<T> beanType) {
        return getApplicationContext().getBean(name, beanType);
    }

    public static Class getType(String name) {
        return getApplicationContext().getType(name);
    }

    public static <T> Map<String, T> getBeansOfType(Class<T> clazz) {
        return getApplicationContext().getBeansOfType(clazz);
    }
}
