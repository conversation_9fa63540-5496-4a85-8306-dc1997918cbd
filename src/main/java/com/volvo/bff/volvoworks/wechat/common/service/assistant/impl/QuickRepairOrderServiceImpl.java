package com.volvo.bff.volvoworks.wechat.common.service.assistant.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.assistant.QuickRepairOrderService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class QuickRepairOrderServiceImpl implements QuickRepairOrderService {

    @Resource
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Override
    public PageBean<QuickRepairOrderQueryResultVo> findAllRepairOrderForAssistant(QuickRepairOrderQueryParamsVo queryParamsVo) {
        DmsResultDTO<PageBean<QuickRepairOrderQueryResultVo>> res = dmscloudServiceFeign.findAllRepairOrder(queryParamsVo);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public RepairOrderProgressQueryDetailResultVo findOrderInfoByNum(String roNo) {
        DmsResultDTO<RepairOrderProgressQueryDetailResultVo> res = dmscloudServiceFeign.findRepairOrderInfoByRoNo(roNo);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public SubmitCarStatusResultVo findSubmitCarStatus(QuickRepairOrderQueryParamsVo paramsVo) {
        DmsResultDTO<SubmitCarStatusResultVo> res = dmscloudServiceFeign.findSubmitCarStatus(paramsVo);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }
}
