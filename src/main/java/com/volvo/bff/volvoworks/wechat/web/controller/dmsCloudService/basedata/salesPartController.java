package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.basedata;

import com.volvo.bff.volvoworks.wechat.common.model.dto.basedata.ListTtSalesPartItemDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.basedata.SalesPartService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(value = "备件销售controller", tags = {"备件销售操作接口"})
@RestController
@Slf4j
@RequestMapping("/basedata/ttSalesPart")
public class salesPartController {

    @Autowired
    private SalesPartService salesPartService;

    @ApiOperation(value = "备件销售单弹窗查询", notes = "查询所有符合条件的备件销售单", httpMethod = "GET")
    @GetMapping(value = "/SearchSalesPart")
    public ResponseDTO<PageInfoDto> findAllSalesPartInfo(@RequestParam Map<String, String> queryParam) {
        return ResponseUtils.success(salesPartService.findAllSalesPartInfo(queryParam));
    }


    @ApiOperation(value = "备件明细查询", notes = "根据备件销售单号查明细", httpMethod = "GET")
    @GetMapping(value = "/item/{salesPartNo}")
    public ResponseDTO<List<Map>> queryPartSalesItem(@PathVariable(value = "salesPartNo") String salesPartNo) {
        return ResponseUtils.success(salesPartService.QueryPartSalesItem(salesPartNo));
    }


    @ApiOperation(value = "备件销售保存", notes = "备件销售保存按钮", httpMethod = "POST")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ResponseDTO<String> savePartSales(@RequestBody ListTtSalesPartItemDTO listTtSalesPartItemDTO){
        return ResponseUtils.success(salesPartService.savePartSales(listTtSalesPartItemDTO));
    }


    @ApiOperation(value = "备件销售出库", notes = "备件销售出库按钮", httpMethod = "POST")
    @RequestMapping(value = "/enterRecord", method = RequestMethod.POST)
    public void accountPartSales(@RequestBody ListTtSalesPartItemDTO listTtSalesPartItemDTO){
            salesPartService.accountPartSales(listTtSalesPartItemDTO);
    }
}
