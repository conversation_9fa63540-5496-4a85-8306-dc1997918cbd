package com.volvo.bff.volvoworks.wechat.web.controller.bffAftersales;

import com.volvo.bff.volvoworks.wechat.common.model.dto.part.ListPartBuyItemDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.stockmanage.ReplenishmentService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "补料")
@RestController
@RequestMapping("/api")
@Slf4j
public class ReplenishmentController {

    @Autowired
    private ReplenishmentService replenishmentService;
    @ApiOperation(value = "配件采购入库-入账(非原厂件入库单/原厂软件入库单/普通电子验收单/特批电子验收单/调拨验收单)", notes = "根据主界面参数和表格数据（放在MAP中）采购入库入账", httpMethod = "POST")
    @PostMapping(value = "/stockmanage/partbuy/enterRecord")
    @ResponseBody
    public ResponseDTO<String> accountStockBuy(@RequestBody ListPartBuyItemDto listPartBuyItemDTO) {
        return ResponseUtils.success(replenishmentService.accountStockBuy(listPartBuyItemDTO));
    }
}
