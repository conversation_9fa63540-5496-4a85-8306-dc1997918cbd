package com.volvo.bff.volvoworks.wechat.common.service.basedata;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;

import java.util.List;
import java.util.Map;

public interface PartBreakageService {

    PageInfoDto queryPartLossByLossNo(Map<String, String> param);

    List<Map> queryPartLossItem(String lossNo);

    Map<String, Object> outStorage(String lossNo, String inventoryNo, String operType, String operReason);
}
