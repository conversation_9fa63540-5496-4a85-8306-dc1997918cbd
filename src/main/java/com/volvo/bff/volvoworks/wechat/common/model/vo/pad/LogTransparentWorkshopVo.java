package com.volvo.bff.volvoworks.wechat.common.model.vo.pad;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;


/**
 * 
* <AUTHOR>
* @date 2020年6月23日
 */
@ApiModel("LogTransparentWorkshopVo")
@Data
public class LogTransparentWorkshopVo {

	//1:查询入场时间 2：保存环检单
	@ApiModelProperty(value = "类型", name = "type")
	private Integer type;

	@ApiModelProperty(value = "经销商代码", name = "ownerCode")
	private String ownerCode;

	@ApiModelProperty(value = "车牌号", name = "license")
	private String license;

	@ApiModelProperty(value = "版本号", name = "version ")
	private String version;

	@ApiModelProperty(value = "token", name = "token")
	private String token;
	
	@ApiModelProperty(value = "设备Id", name = "deviceId")
    private String deviceId;

	//h5:1 ipad:2
	@ApiModelProperty(value = "数据来源", name = "channel")
	private String channel;
	
	@ApiModelProperty(value = "对应接口的请求参数", name = "reqParam")
    private Map<String,String> reqParam;

}
