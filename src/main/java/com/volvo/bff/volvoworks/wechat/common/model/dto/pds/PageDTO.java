package com.volvo.bff.volvoworks.wechat.common.model.dto.pds;

import lombok.Data;

import java.util.Collections;
import java.util.List;

@Data
public class PageDTO<T> {

	/**
	 * 查询数据列表
	 */
	private List<T> records = Collections.emptyList();

	/**
	 * 总数
	 */
	private Integer total = 0;
	/**
	 * 每页显示条数，默认 10
	 */
	private Integer size = 10;

	/**
	 * 当前页
	 */
	private Integer current = 1;

	/**
	 * 总页数
	 */
	private Integer pages = 1;
}
