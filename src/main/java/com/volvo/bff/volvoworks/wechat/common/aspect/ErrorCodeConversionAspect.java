package com.volvo.bff.volvoworks.wechat.common.aspect;

import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 异常转换与抛出
 * @Date 2024/10/16 16:44
 */
@Aspect
@Component
public class ErrorCodeConversionAspect {

}
