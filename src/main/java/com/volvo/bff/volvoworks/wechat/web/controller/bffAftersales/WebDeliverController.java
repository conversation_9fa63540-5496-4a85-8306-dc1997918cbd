package com.volvo.bff.volvoworks.wechat.web.controller.bffAftersales;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.workshop.WebDeliverService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Api(tags = "送修人web")
@RestController
@RequestMapping("/api/v1/web/deliver")
public class WebDeliverController {

    @Autowired
    private WebDeliverService webDeliverService;


    /**
     * 记录异常送修人工单
     *
     * @param roNo      工单号
     * @param traceTime 回访时间类型
     * @return map 返回结果
     */

    @ApiOperation(value = "记录异常送修人工单", notes = "记录异常送修人工单", httpMethod = "PUT")
    @RequestMapping(value = "/alterTraceTime/{roNo}/{traceTime}", method = RequestMethod.PUT)
    @ResponseBody
    public ResponseDTO<Map<String, Object>> deliveryOrderExceptionRecord(@PathVariable("roNo") String roNo, @PathVariable("traceTime") String traceTime) {
        return ResponseUtils.success(webDeliverService.deliveryOrderExceptionRecord(roNo, traceTime));
    }

}
