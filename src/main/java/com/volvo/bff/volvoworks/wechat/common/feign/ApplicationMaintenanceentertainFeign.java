package com.volvo.bff.volvoworks.wechat.common.feign;

import com.alibaba.fastjson.JSONObject;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.applicationMaintenanceentertain.ReceptionEntrancePageConfigReqDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.applicationMaintenanceentertain.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@FeignClient(name = "application-maintenanceentertain",
		url = "${mse-in.tob.application-maintenanceentertain.url}",
		path = "${mse-in.tob.application-maintenanceentertain.path}")
public interface ApplicationMaintenanceentertainFeign {

	@ApiOperation(value = "查询进场车辆列表", notes = "查询进场车辆列表", httpMethod = "GET")
	@GetMapping("/vehicle-reception-entrance/api/v1/queryVehicleEntranceList")
	DmsResultDTO<PageBean<VehicleEntranceVO>> queryVehicleEntranceList(
			@RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize,
			@RequestParam(value = "pageIndex", defaultValue = "1") Integer pageIndex,
			@RequestParam(value = "dealerCode" , required = true) String dealerCode);

	@ApiOperation(value = "校验当前人员是否可以接待当前车辆", notes = "校验当前人员是否可以接待当前车辆", httpMethod = "POST")
	@PostMapping("/vehicle-reception-entrance/api/v1/checkVehicleReception")
	DmsResultDTO<Boolean> checkVehicleReception(@RequestBody CheckVehicleEntranceReqVO checkVehicleEntranceReq);

	@ApiOperation(value = "更新进场车辆信息", notes = "更新进场车辆信息", httpMethod = "POST")
	@PostMapping("/vehicle-reception-entrance/api/v1/updateVehicleReception")
	DmsResultDTO<Boolean> updateVehicleReception(@RequestBody VehicleEntranceVO vehicleEntrance);

	@ApiOperation(value = "根据车牌号查询进场数据", notes = "根据车牌号查询进场数据", httpMethod = "GET")
	@GetMapping("/vehicle-reception-entrance/api/v1/queryVehicleEntranceByLicensePlate")
	DmsResultDTO<VehicleEntranceVO> queryVehicleEntranceByLicensePlate(@RequestParam(value = "licensePlate" ) String licensePlate);

	@ApiOperation(value = "根据车牌号查询两天内快速接待数据", notes = "根据车牌号查询两天内快速接待数据", httpMethod = "GET")
	@GetMapping("/vehicle-reception-entrance/api/v1/queryReceptionEntranceByLicensePlate")
	DmsResultDTO<SubmitEntranceInfoReqVO> queryReceptionEntranceByLicensePlate(@RequestParam(value = "licensePlate" ) String licensePlate);

	@ApiOperation(value = "提交建单信息", notes = "提交建单信息", httpMethod = "POST")
	@PostMapping("/vehicle-reception-entrance/api/v1/submitEntranceInfo")
	DmsResultDTO<Boolean> submitEntranceInfo(@RequestBody SubmitEntranceInfoReqVO submitEntranceInfoReq);

	@ApiOperation(value = "查询白名单", notes = "查询白名单", httpMethod = "GET")
	@GetMapping("/vehicle-reception-entrance/api/v1/queryWhiteList")
	DmsResultDTO<Boolean> queryWhiteList(@RequestParam(value = "dealerCode" ) String dealerCode);

	@ApiOperation(value = "查询页面配置化", notes = "查询页面配置化", httpMethod = "POST")
	@PostMapping("/reception-entrance-config/api/v1/queryReceptionEntranceConfigByType")
	DmsResultDTO<List<JSONObject>> queryReceptionEntranceConfigByType(@RequestBody ReceptionEntrancePageConfigReqDTO pageConfigReq);
}
