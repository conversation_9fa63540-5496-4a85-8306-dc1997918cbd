package com.volvo.bff.volvoworks.wechat.common.model.vo.assistant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("查询所有预约单参数VO")
public class AppointmentQueryParamsVo {

    @ApiModelProperty(hidden = true)
    private String ownerCode;

    @ApiModelProperty(value = "牌照",name = "license",example = "苏B832NF")
    private String license;

    @ApiModelProperty(value = "服务顾问",name = "serviceAdvisor",example = "123")
    private String serviceAdvisor;

    @ApiModelProperty(value = "起始开单日期",name = "beginCreatedAt",example = "2019-11-05 19:25:14")
    private String beginCreatedAt;

    @ApiModelProperty(value = "截止开单日期",name = "endCreatedAt",example = "2019-11-05 19:25:14")
    private String endCreatedAt;

    @ApiModelProperty(value = "进厂状态",name = "bookingOrderStatus",example = "80671005",required = true)
    private Integer bookingOrderStatus;
}
