package com.volvo.bff.volvoworks.wechat.web.controller.bffAftersales;

import com.volvo.bff.volvoworks.wechat.common.model.dto.workshop.QwJSSDKDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.workshop.QwTokenDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.workshop.WechatEnterpriseService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "企微群聊app(不带token校验)")
@RestController
@RequestMapping("/visitor/api/v1/app/wechat-enterprise")
public class AppWechatEnterpriseVisitorController {
	
	@Autowired
	private WechatEnterpriseService wechatEnterpriseService;
    
	/**
	 * 获取token
	 */

	@ApiOperation(value = "根据code获取token", notes = "获取token", httpMethod = "GET")
	@GetMapping(value = "/getNewbieToken")
	public ResponseDTO<QwTokenDTO> getNewbieTokenAsync(@RequestParam(value = "code", required = true) String code) {
		return ResponseUtils.success(wechatEnterpriseService.getNewbieTokenAsync(code));
	}
	
	/**
	 * 获取签名密钥配置
	 */

	@ApiOperation(value = "获取签名密钥配置", notes = "获取签名密钥配置", httpMethod = "POST")
	@PostMapping(value = "/getQYWeChatAgentJSSDKConfig")
	public ResponseDTO<QwJSSDKDTO> getQYWeChatAgentJSSDKConfig(@RequestBody String url) {
		return ResponseUtils.success(wechatEnterpriseService.getQYWeChatAgentJSSDKConfig(url));
	}

}
