package com.volvo.bff.volvoworks.wechat.common.model.vo.assistant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("车辆检查List明细结果VO")
public class VehicleCheckDetailListResultVo {

    @ApiModelProperty(value="内饰检查",name = "bodyAppearanceInfoList")
    private List<VehicleCheckDetailResultVo> vehicleCheckDetailResultVos;

    @ApiModelProperty(value="发动机舱",name = "bodyAppearanceInfoList2")
    private List<VehicleCheckDetailResultVo> vehicleCheckDetailResultVos2;

    @ApiModelProperty(value="底盘四轮",name = "bodyAppearanceInfoList3")
    private List<VehicleCheckDetailResultVo> vehicleCheckDetailResultVos3;
}
