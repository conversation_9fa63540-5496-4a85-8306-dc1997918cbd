package com.volvo.bff.volvoworks.wechat.common.model.vo.app;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("QueryRepairingOrderResultV2Vo")
@Data
public class QueryRepairingOrderResultV2Vo {

    @ApiModelProperty(value = "工单号",name = "roNo")
    private String roNo;

    @ApiModelProperty(value = "服务专员",name = "serviceAdvisor")
    private String serviceAdvisor;

    @ApiModelProperty(value = "指定技师",name = "chiefTechnician")
    private String chiefTechnician;

    @ApiModelProperty(value = "车主",name = "ownerName")
    private String ownerName;

    @ApiModelProperty(value = "车牌号",name = "license")
    private String license;

    @ApiModelProperty(value = "VIN",name = "vin")
    private String vin;

    @ApiModelProperty(value = "车型",name = "model")
    private String model;

    @ApiModelProperty(value = "预交车时间",name = "endTimeSupposed")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endTimeSupposed;

    @ApiModelProperty(value = "工单状态",name = "roStatus")
    private String roStatus;

    @ApiModelProperty(value = "备货状态",name = "isStockUp")
    private String isStockUp;

    @ApiModelProperty(value = "群聊id", name = "groupId")
    private String groupId;

    @ApiModelProperty(value = "群聊状态", name = "groupState")
    private String groupStatus;
}

