package com.volvo.bff.volvoworks.wechat.common.model.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("查询工单详细信息Vo")
public class RepairOrderDetailsResultVo {

    @ApiModelProperty(value = "工单号", name = "roNo")
    private String roNo;

    @ApiModelProperty(value = "车主", name = "ownerName")
    private String ownerName;

    @ApiModelProperty(value = "手机号", name = "mobile")
    private String mobile;

    @ApiModelProperty(value = "VIN", name = "vin")
    private String vin;

    @ApiModelProperty(value = "车型", name = "model")
    private String model;

    @ApiModelProperty(value = "车牌号", name = "license")
    private String license;

    @ApiModelProperty(value = "销售日期", name = "salesDate")
    private String salesDate;

    @ApiModelProperty(value = "会员码", name = "license")
    private String oneId;

    @ApiModelProperty(value = "里程", name = "license")
    private String mileage;

    @ApiModelProperty(value = "维修费", name = "totalRepairAmount")
    private Double totalRepairAmount;

    @ApiModelProperty(value = "群聊id", name = "groupId")
    private String groupId;

    @ApiModelProperty(value = "群聊状态", name = "groupState")
    private Integer groupState;

    @ApiModelProperty(value = "交修项目", name = "roHandRepairProjectList")
    private List<RoHandRepairProjectVo> roHandRepairProjectList;

    @ApiModelProperty(value = "维修工时", name = "roLabourList")
    private List<RoLabourResultVo> roLabourList;

    @ApiModelProperty(value = "维修零件", name = "repairPartList")
    private List<RoRepairPartResultVo> repairPartList;

    @ApiModelProperty(value = "附加项目", name = "addItemList")
    private List<RoAddItemResultVo> addItemList;

    public List<RoLabourResultVo> getRoLabourList() {
        return roLabourList;
    }

    public void setRoLabourList(List<RoLabourResultVo> roLabourList) {
        this.roLabourList = roLabourList;
    }

    public List<RoRepairPartResultVo> getRepairPartList() {
        return repairPartList;
    }

    public void setRepairPartList(List<RoRepairPartResultVo> repairPartList) {
        this.repairPartList = repairPartList;
    }

    public List<RoAddItemResultVo> getAddItemList() {
        return addItemList;
    }

    public void setAddItemList(List<RoAddItemResultVo> addItemList) {
        this.addItemList = addItemList;
    }

    public Double getTotalRepairAmount() {
        return totalRepairAmount;
    }

    public void setTotalRepairAmount(Double totalRepairAmount) {
        this.totalRepairAmount = totalRepairAmount;
    }

    public String getRoNo() {
        return roNo;
    }

    public void setRoNo(String roNo) {
        this.roNo = roNo;
    }

    public void setRoHandRepairProjectList(List<RoHandRepairProjectVo> roHandRepairProjectList) {
        this.roHandRepairProjectList = roHandRepairProjectList;
    }

    public List<RoHandRepairProjectVo> getRoHandRepairProjectList() {
        return roHandRepairProjectList;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getLicense() {
        return license;
    }

    public void setLicense(String license) {
        this.license = license;
    }

    public String getSalesDate() {
        return salesDate;
    }

    public void setSalesDate(String salesDate) {
        this.salesDate = salesDate;
    }

    public String getOneId() {
        return oneId;
    }

    public void setOneId(String oneId) {
        this.oneId = oneId;
    }

    public String getMileage() {
        return mileage;
    }

    public void setMileage(String mileage) {
        this.mileage = mileage;
    }

}
