package com.volvo.bff.volvoworks.wechat.common.model.vo.workshop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("未提交报告原因")
public class ReportReasonDto {

    @ApiModelProperty("经销商")
    private String ownerCode;
    @ApiModelProperty("工单号")
    private String roNo;
    @ApiModelProperty("未提交报告原因")
    private String reportReason;
    @ApiModelProperty("vin")
    private String vin;


}
