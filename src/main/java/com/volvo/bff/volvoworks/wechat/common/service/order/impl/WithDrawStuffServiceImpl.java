package com.volvo.bff.volvoworks.wechat.common.service.order.impl;

import com.alibaba.fastjson.JSON;
import com.volvo.bff.volvoworks.wechat.common.feign.ApplicationPostsaleManagementFeign;
import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.dto.order.ListAdMaintainDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.order.WithDrawStuffService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.Map;

@Slf4j
@Service
public class WithDrawStuffServiceImpl implements WithDrawStuffService {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Autowired
    private ApplicationPostsaleManagementFeign applicationPostsaleManagementFeign;
    @Override
    public Map<String, Object> queryMaintainPicking(Map<String, String> queryParam) {
        DmsResultDTO<Map<String, Object>> response = dmscloudServiceFeign.queryMaintainPicking(queryParam);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public Integer saveMaintainPart(@RequestBody ListAdMaintainDTO listAdMaintainDTO) {
        log.info("saveMaintainPart:{}", JSON.toJSONString(listAdMaintainDTO));
        DmsResultDTO<Integer> response = dmscloudServiceFeign.saveMaintainPart(listAdMaintainDTO);
        log.info("saveMaintainPart response:{}", JSON.toJSONString(response));
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public  String account(@RequestBody ListAdMaintainDTO listAdMaintainDTO){
        DmsResultDTO<String> response = applicationPostsaleManagementFeign.account(listAdMaintainDTO);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }
}
