package com.volvo.bff.volvoworks.wechat.common.service.vhc.impl;


import com.volvo.bff.volvoworks.wechat.common.feign.ApplicationMaintainManagementFeign;
import com.volvo.bff.volvoworks.wechat.common.model.dto.vhc.*;
import com.volvo.bff.volvoworks.wechat.common.service.vhc.VehicleHealthCheckService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 车辆健康检查
 * @Date 2024/9/25 15:56
 */
@Service
@AllArgsConstructor
public class VehicleHealthCheckServiceImpl implements VehicleHealthCheckService {


    private final ApplicationMaintainManagementFeign applicationMaintainManagementFeign;

    @Override
    public VehicleHealthCheckDetailDto getVehicleHealthCheckDetail(VehicleHealthCheckDetailParamDto vehicleHealthCheckDetailParamDto) {
        return applicationMaintainManagementFeign.getVehicleHealthCheckDetail(vehicleHealthCheckDetailParamDto).getData();
    }

    @Override
    public List<VhcItemConfigInfoDto> getVhcItemInfoByClassId(Integer classId, String configClassId) {
        return applicationMaintainManagementFeign.getVhcItemInfoByClassId(classId, configClassId).getData();
    }

    @Override
    public void saveVehicleHealthCheckInfo(VehicleHealthCheckInfoDto vehicleHealthCheckInfoDto) {
        applicationMaintainManagementFeign.saveVehicleHealthCheckInfo(vehicleHealthCheckInfoDto);
    }
}
