package com.volvo.bff.volvoworks.wechat.common.model.vo.pad;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
@ApiModel("新增环检单参数Vo")
public class SurroundCheckVO {

    @ApiModelProperty(value = "预检单号",name = "yjNo")
    private String yjNo;

    @ApiModelProperty(value = "vin",name = "vin")
    private String vin;

    @ApiModelProperty(value = "车牌号",name = "license")
    private String license;

    @ApiModelProperty(value = "车型",name = "model")
    private String model;

    @ApiModelProperty(value = "车主",name = "ownerName")
    private String ownerName;

    @ApiModelProperty(value = "车主手机号",name = "phone")
    private String phone;

    @ApiModelProperty(value = "送车人姓名",name = "contactorName")
    private String contactorName;

    @ApiModelProperty(value = "送车人手机",name = "contactorPhone")
    private String contactorPhone;

    @ApiModelProperty(value = "预约单号",name = "bookingOrderNo")
    private String bookingOrderNo;

    @ApiModelProperty(value = "车型名称",name = "modelName")
    private String modelName;

    @ApiModelProperty(value = "车主id",name = "oneId")
    private Long oneId;

    @ApiModelProperty(value = "进厂时间",name = "entryTime")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date entryTime;

    @ApiModelProperty(value = "进厂时间类型  1:摄像头  2:人工录入",name = "entryTimeType")
    private String entryTimeType;

    @ApiModelProperty(value="集合list",name = "bodyAppearanceInfoList")
    private List<BodyAppearanceInfo> bodyAppearanceInfoList;

}
