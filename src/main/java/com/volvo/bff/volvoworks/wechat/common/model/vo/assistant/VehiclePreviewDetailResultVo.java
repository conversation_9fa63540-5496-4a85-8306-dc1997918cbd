package com.volvo.bff.volvoworks.wechat.common.model.vo.assistant;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("查询预检单明细结果VO")
public class VehiclePreviewDetailResultVo {

    @ApiModelProperty(value = "牌照", name = "license")
    private String license;

    @ApiModelProperty(value = "预检单号", name = "yjNo")
    private String yjNo;

    @ApiModelProperty(value = "工单号", name = "roNo")
    private String roNo;

    @ApiModelProperty(value = "车型", name = "model")
    private String model;

    @ApiModelProperty(value = "车主", name = "ownerName")
    private String ownerName;

    @ApiModelProperty(value = "手机号", name = "mobile")
    private String mobile;

    @ApiModelProperty(value = "送修人姓名", name = "contactorName")
    private String contactorName;

    @ApiModelProperty(value = "送修人电话",name = "contactorPhone")
    private String contactorPhone;

    @ApiModelProperty(value = "送修人邮箱",name = "contactorEMail")
    private String contactorEMail;

    @ApiModelProperty(value = "里程",name = "inMileage")
    private String inMileage;

    @ApiModelProperty(value = "服务顾问",name = "serviceAdvisor",example = "306")
    private String serviceAdvisor;

    @ApiModelProperty(value = "选择类型:进厂原因",name = "inReason")
    private String inReason;

    @ApiModelProperty(value = "故障描述",name = "remark")
    private String remark;

    @ApiModelProperty(value="车身状态list",name = "bodyAppearanceInfoList")
    private List<BodyAppearanceInfo> bodyAppearanceInfoList;

    @ApiModelProperty(value = "物品清单集合",name = "contentCodes")
    private List<Integer> contentCodes;

}
