package com.volvo.bff.volvoworks.wechat.common.model.dto.login;

import io.swagger.annotations.ApiModelProperty;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Locale;

@Component
@Scope("request")
public class CurrentLoginInfoDto {
    @ApiModelProperty("集团、车厂唯一ID")
    private String appId;
    @ApiModelProperty("公司ID")
    private String companyId;
    @ApiModelProperty("公司CODE")
    private String companyCode;
    @ApiModelProperty("当前登录公司CODE")
    private String ownerCode;
    @ApiModelProperty("二网时当前公司CODE")
    private String ownerParCode;
    @ApiModelProperty("数据类型：公司、部门、大区、小区")
    private Long orgType;
    @ApiModelProperty("数据来源：经销商、集团、车厂")
    private Integer dataType;
    @ApiModelProperty("员工组织ID")
    private Integer orgId;
    @ApiModelProperty("员工本组织以及以下ID")
    private String orgIds;
    @ApiModelProperty("登录方式：pc/APP")
    private String loginWay = "10051001";
    @ApiModelProperty("国际化")
    private Locale locale;
    @ApiModelProperty("员工ID")
    private Long employeeId;
    @ApiModelProperty("员工编号（过时）（售后用）")
    private String employeeNo;
    @ApiModelProperty("用户ID")
    private String userId;
    @ApiModelProperty("用户编号（过时）")
    private String userCode;
    @ApiModelProperty("EMPID")
    private Long empId;
    @ApiModelProperty("职位编码")
    private Long appRole;
    @ApiModelProperty("集团代码")
    private String groupCode;
    @ApiModelProperty("用户名称")
    private String userName;
    @ApiModelProperty("经销商名称")
    private String dealerName;
    @ApiModelProperty("组织名称")
    private String orgName;
    @ApiModelProperty("经销商ID")
    private String ownerId;
    @ApiModelProperty("用户是否检查经销商代码是否与当前session 中代码一致")
    private Boolean leanCheck = true;
    @ApiModelProperty("组织代码")
    private String orgCode;
    @ApiModelProperty("员工编号手机号")
    private String mobile;
    @ApiModelProperty("用户职位ID")
    private String userOrgId;
    @ApiModelProperty("客服顾问ID")
    private String consultantId;
    @ApiModelProperty("用于网关存")
    private String uuid;

    public String toString() {
        return "CurrentLoginInfoDto(appId=" + this.getAppId() + ", companyId=" + this.getCompanyId() + ", companyCode=" + this.getCompanyCode() + ", ownerCode=" + this.getOwnerCode() + ", ownerParCode=" + this.getOwnerParCode() + ", orgType=" + this.getOrgType() + ", dataType=" + this.getDataType() + ", orgId=" + this.getOrgId() + ", orgIds=" + this.getOrgIds() + ", loginWay=" + this.getLoginWay() + ", locale=" + this.getLocale() + ", employeeId=" + this.getEmployeeId() + ", employeeNo=" + this.getEmployeeNo() + ", userId=" + this.getUserId() + ", userCode=" + this.getUserCode() + ", empId=" + this.getEmpId() + ", appRole=" + this.getAppRole() + ", groupCode=" + this.getGroupCode() + ", userName=" + this.getUserName() + ", dealerName=" + this.getDealerName() + ", orgName=" + this.getOrgName() + ", ownerId=" + this.getOwnerId() + ", leanCheck=" + this.getLeanCheck() + ", orgCode=" + this.getOrgCode() + ", mobile=" + this.getMobile() + ", userOrgId=" + this.getUserOrgId() + ", consultantId=" + this.getConsultantId() + ", uuid=" + this.getUuid() + ")";
    }

    public CurrentLoginInfoDto() {
    }

    public String getAppId() {
        return this.appId;
    }

    public String getCompanyId() {
        return this.companyId;
    }

    public String getCompanyCode() {
        return this.companyCode;
    }

    public String getOwnerCode() {
        return this.ownerCode;
    }

    public String getOwnerParCode() {
        return this.ownerParCode;
    }

    public Long getOrgType() {
        return this.orgType;
    }

    public Integer getDataType() {
        return this.dataType;
    }

    public Integer getOrgId() {
        return this.orgId;
    }

    public String getOrgIds() {
        return this.orgIds;
    }

    public String getLoginWay() {
        return this.loginWay;
    }

    public Locale getLocale() {
        return this.locale;
    }

    public Long getEmployeeId() {
        return this.employeeId;
    }

    public String getEmployeeNo() {
        return this.employeeNo;
    }

    public String getUserId() {
        return this.userId;
    }

    public String getUserCode() {
        return this.userCode;
    }

    public Long getEmpId() {
        return this.empId;
    }

    public Long getAppRole() {
        return this.appRole;
    }

    public String getGroupCode() {
        return this.groupCode;
    }

    public String getUserName() {
        return this.userName;
    }

    public String getDealerName() {
        return this.dealerName;
    }

    public String getOrgName() {
        return this.orgName;
    }

    public String getOwnerId() {
        return this.ownerId;
    }

    public Boolean getLeanCheck() {
        return this.leanCheck;
    }

    public String getOrgCode() {
        return this.orgCode;
    }

    public String getMobile() {
        return this.mobile;
    }

    public String getUserOrgId() {
        return this.userOrgId;
    }

    public String getConsultantId() {
        return this.consultantId;
    }

    public String getUuid() {
        return this.uuid;
    }

    public void setAppId(final String appId) {
        this.appId = appId;
    }

    public void setCompanyId(final String companyId) {
        this.companyId = companyId;
    }

    public void setCompanyCode(final String companyCode) {
        this.companyCode = companyCode;
    }

    public void setOwnerCode(final String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public void setOwnerParCode(final String ownerParCode) {
        this.ownerParCode = ownerParCode;
    }

    public void setOrgType(final Long orgType) {
        this.orgType = orgType;
    }

    public void setDataType(final Integer dataType) {
        this.dataType = dataType;
    }

    public void setOrgId(final Integer orgId) {
        this.orgId = orgId;
    }

    public void setOrgIds(final String orgIds) {
        this.orgIds = orgIds;
    }

    public void setLoginWay(final String loginWay) {
        this.loginWay = loginWay;
    }

    public void setLocale(final Locale locale) {
        this.locale = locale;
    }

    public void setEmployeeId(final Long employeeId) {
        this.employeeId = employeeId;
    }

    public void setEmployeeNo(final String employeeNo) {
        this.employeeNo = employeeNo;
    }

    public void setUserId(final String userId) {
        this.userId = userId;
    }

    public void setUserCode(final String userCode) {
        this.userCode = userCode;
    }

    public void setEmpId(final Long empId) {
        this.empId = empId;
    }

    public void setAppRole(final Long appRole) {
        this.appRole = appRole;
    }

    public void setGroupCode(final String groupCode) {
        this.groupCode = groupCode;
    }

    public void setUserName(final String userName) {
        this.userName = userName;
    }

    public void setDealerName(final String dealerName) {
        this.dealerName = dealerName;
    }

    public void setOrgName(final String orgName) {
        this.orgName = orgName;
    }

    public void setOwnerId(final String ownerId) {
        this.ownerId = ownerId;
    }

    public void setLeanCheck(final Boolean leanCheck) {
        this.leanCheck = leanCheck;
    }

    public void setOrgCode(final String orgCode) {
        this.orgCode = orgCode;
    }

    public void setMobile(final String mobile) {
        this.mobile = mobile;
    }

    public void setUserOrgId(final String userOrgId) {
        this.userOrgId = userOrgId;
    }

    public void setConsultantId(final String consultantId) {
        this.consultantId = consultantId;
    }

    public void setUuid(final String uuid) {
        this.uuid = uuid;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof CurrentLoginInfoDto)) {
            return false;
        } else {
            CurrentLoginInfoDto other = (CurrentLoginInfoDto)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label347: {
                    Object this$appId = this.getAppId();
                    Object other$appId = other.getAppId();
                    if (this$appId == null) {
                        if (other$appId == null) {
                            break label347;
                        }
                    } else if (this$appId.equals(other$appId)) {
                        break label347;
                    }

                    return false;
                }

                Object this$companyId = this.getCompanyId();
                Object other$companyId = other.getCompanyId();
                if (this$companyId == null) {
                    if (other$companyId != null) {
                        return false;
                    }
                } else if (!this$companyId.equals(other$companyId)) {
                    return false;
                }

                Object this$companyCode = this.getCompanyCode();
                Object other$companyCode = other.getCompanyCode();
                if (this$companyCode == null) {
                    if (other$companyCode != null) {
                        return false;
                    }
                } else if (!this$companyCode.equals(other$companyCode)) {
                    return false;
                }

                label326: {
                    Object this$ownerCode = this.getOwnerCode();
                    Object other$ownerCode = other.getOwnerCode();
                    if (this$ownerCode == null) {
                        if (other$ownerCode == null) {
                            break label326;
                        }
                    } else if (this$ownerCode.equals(other$ownerCode)) {
                        break label326;
                    }

                    return false;
                }

                label319: {
                    Object this$ownerParCode = this.getOwnerParCode();
                    Object other$ownerParCode = other.getOwnerParCode();
                    if (this$ownerParCode == null) {
                        if (other$ownerParCode == null) {
                            break label319;
                        }
                    } else if (this$ownerParCode.equals(other$ownerParCode)) {
                        break label319;
                    }

                    return false;
                }

                label312: {
                    Object this$orgType = this.getOrgType();
                    Object other$orgType = other.getOrgType();
                    if (this$orgType == null) {
                        if (other$orgType == null) {
                            break label312;
                        }
                    } else if (this$orgType.equals(other$orgType)) {
                        break label312;
                    }

                    return false;
                }

                Object this$dataType = this.getDataType();
                Object other$dataType = other.getDataType();
                if (this$dataType == null) {
                    if (other$dataType != null) {
                        return false;
                    }
                } else if (!this$dataType.equals(other$dataType)) {
                    return false;
                }

                label298: {
                    Object this$orgId = this.getOrgId();
                    Object other$orgId = other.getOrgId();
                    if (this$orgId == null) {
                        if (other$orgId == null) {
                            break label298;
                        }
                    } else if (this$orgId.equals(other$orgId)) {
                        break label298;
                    }

                    return false;
                }

                Object this$orgIds = this.getOrgIds();
                Object other$orgIds = other.getOrgIds();
                if (this$orgIds == null) {
                    if (other$orgIds != null) {
                        return false;
                    }
                } else if (!this$orgIds.equals(other$orgIds)) {
                    return false;
                }

                label284: {
                    Object this$loginWay = this.getLoginWay();
                    Object other$loginWay = other.getLoginWay();
                    if (this$loginWay == null) {
                        if (other$loginWay == null) {
                            break label284;
                        }
                    } else if (this$loginWay.equals(other$loginWay)) {
                        break label284;
                    }

                    return false;
                }

                Object this$locale = this.getLocale();
                Object other$locale = other.getLocale();
                if (this$locale == null) {
                    if (other$locale != null) {
                        return false;
                    }
                } else if (!this$locale.equals(other$locale)) {
                    return false;
                }

                Object this$employeeId = this.getEmployeeId();
                Object other$employeeId = other.getEmployeeId();
                if (this$employeeId == null) {
                    if (other$employeeId != null) {
                        return false;
                    }
                } else if (!this$employeeId.equals(other$employeeId)) {
                    return false;
                }

                label263: {
                    Object this$employeeNo = this.getEmployeeNo();
                    Object other$employeeNo = other.getEmployeeNo();
                    if (this$employeeNo == null) {
                        if (other$employeeNo == null) {
                            break label263;
                        }
                    } else if (this$employeeNo.equals(other$employeeNo)) {
                        break label263;
                    }

                    return false;
                }

                label256: {
                    Object this$userId = this.getUserId();
                    Object other$userId = other.getUserId();
                    if (this$userId == null) {
                        if (other$userId == null) {
                            break label256;
                        }
                    } else if (this$userId.equals(other$userId)) {
                        break label256;
                    }

                    return false;
                }

                Object this$userCode = this.getUserCode();
                Object other$userCode = other.getUserCode();
                if (this$userCode == null) {
                    if (other$userCode != null) {
                        return false;
                    }
                } else if (!this$userCode.equals(other$userCode)) {
                    return false;
                }

                Object this$empId = this.getEmpId();
                Object other$empId = other.getEmpId();
                if (this$empId == null) {
                    if (other$empId != null) {
                        return false;
                    }
                } else if (!this$empId.equals(other$empId)) {
                    return false;
                }

                label235: {
                    Object this$appRole = this.getAppRole();
                    Object other$appRole = other.getAppRole();
                    if (this$appRole == null) {
                        if (other$appRole == null) {
                            break label235;
                        }
                    } else if (this$appRole.equals(other$appRole)) {
                        break label235;
                    }

                    return false;
                }

                Object this$groupCode = this.getGroupCode();
                Object other$groupCode = other.getGroupCode();
                if (this$groupCode == null) {
                    if (other$groupCode != null) {
                        return false;
                    }
                } else if (!this$groupCode.equals(other$groupCode)) {
                    return false;
                }

                Object this$userName = this.getUserName();
                Object other$userName = other.getUserName();
                if (this$userName == null) {
                    if (other$userName != null) {
                        return false;
                    }
                } else if (!this$userName.equals(other$userName)) {
                    return false;
                }

                label214: {
                    Object this$dealerName = this.getDealerName();
                    Object other$dealerName = other.getDealerName();
                    if (this$dealerName == null) {
                        if (other$dealerName == null) {
                            break label214;
                        }
                    } else if (this$dealerName.equals(other$dealerName)) {
                        break label214;
                    }

                    return false;
                }

                label207: {
                    Object this$orgName = this.getOrgName();
                    Object other$orgName = other.getOrgName();
                    if (this$orgName == null) {
                        if (other$orgName == null) {
                            break label207;
                        }
                    } else if (this$orgName.equals(other$orgName)) {
                        break label207;
                    }

                    return false;
                }

                label200: {
                    Object this$ownerId = this.getOwnerId();
                    Object other$ownerId = other.getOwnerId();
                    if (this$ownerId == null) {
                        if (other$ownerId == null) {
                            break label200;
                        }
                    } else if (this$ownerId.equals(other$ownerId)) {
                        break label200;
                    }

                    return false;
                }

                Object this$leanCheck = this.getLeanCheck();
                Object other$leanCheck = other.getLeanCheck();
                if (this$leanCheck == null) {
                    if (other$leanCheck != null) {
                        return false;
                    }
                } else if (!this$leanCheck.equals(other$leanCheck)) {
                    return false;
                }

                label186: {
                    Object this$orgCode = this.getOrgCode();
                    Object other$orgCode = other.getOrgCode();
                    if (this$orgCode == null) {
                        if (other$orgCode == null) {
                            break label186;
                        }
                    } else if (this$orgCode.equals(other$orgCode)) {
                        break label186;
                    }

                    return false;
                }

                Object this$mobile = this.getMobile();
                Object other$mobile = other.getMobile();
                if (this$mobile == null) {
                    if (other$mobile != null) {
                        return false;
                    }
                } else if (!this$mobile.equals(other$mobile)) {
                    return false;
                }

                label172: {
                    Object this$userOrgId = this.getUserOrgId();
                    Object other$userOrgId = other.getUserOrgId();
                    if (this$userOrgId == null) {
                        if (other$userOrgId == null) {
                            break label172;
                        }
                    } else if (this$userOrgId.equals(other$userOrgId)) {
                        break label172;
                    }

                    return false;
                }

                Object this$consultantId = this.getConsultantId();
                Object other$consultantId = other.getConsultantId();
                if (this$consultantId == null) {
                    if (other$consultantId != null) {
                        return false;
                    }
                } else if (!this$consultantId.equals(other$consultantId)) {
                    return false;
                }

                Object this$uuid = this.getUuid();
                Object other$uuid = other.getUuid();
                if (this$uuid == null) {
                    if (other$uuid != null) {
                        return false;
                    }
                } else if (!this$uuid.equals(other$uuid)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof CurrentLoginInfoDto;
    }

    public int hashCode() {
        boolean PRIME = true;
        int result = 1;
        Object $appId = this.getAppId();
        result = result * 59 + ($appId == null ? 43 : $appId.hashCode());
        Object $companyId = this.getCompanyId();
        result = result * 59 + ($companyId == null ? 43 : $companyId.hashCode());
        Object $companyCode = this.getCompanyCode();
        result = result * 59 + ($companyCode == null ? 43 : $companyCode.hashCode());
        Object $ownerCode = this.getOwnerCode();
        result = result * 59 + ($ownerCode == null ? 43 : $ownerCode.hashCode());
        Object $ownerParCode = this.getOwnerParCode();
        result = result * 59 + ($ownerParCode == null ? 43 : $ownerParCode.hashCode());
        Object $orgType = this.getOrgType();
        result = result * 59 + ($orgType == null ? 43 : $orgType.hashCode());
        Object $dataType = this.getDataType();
        result = result * 59 + ($dataType == null ? 43 : $dataType.hashCode());
        Object $orgId = this.getOrgId();
        result = result * 59 + ($orgId == null ? 43 : $orgId.hashCode());
        Object $orgIds = this.getOrgIds();
        result = result * 59 + ($orgIds == null ? 43 : $orgIds.hashCode());
        Object $loginWay = this.getLoginWay();
        result = result * 59 + ($loginWay == null ? 43 : $loginWay.hashCode());
        Object $locale = this.getLocale();
        result = result * 59 + ($locale == null ? 43 : $locale.hashCode());
        Object $employeeId = this.getEmployeeId();
        result = result * 59 + ($employeeId == null ? 43 : $employeeId.hashCode());
        Object $employeeNo = this.getEmployeeNo();
        result = result * 59 + ($employeeNo == null ? 43 : $employeeNo.hashCode());
        Object $userId = this.getUserId();
        result = result * 59 + ($userId == null ? 43 : $userId.hashCode());
        Object $userCode = this.getUserCode();
        result = result * 59 + ($userCode == null ? 43 : $userCode.hashCode());
        Object $empId = this.getEmpId();
        result = result * 59 + ($empId == null ? 43 : $empId.hashCode());
        Object $appRole = this.getAppRole();
        result = result * 59 + ($appRole == null ? 43 : $appRole.hashCode());
        Object $groupCode = this.getGroupCode();
        result = result * 59 + ($groupCode == null ? 43 : $groupCode.hashCode());
        Object $userName = this.getUserName();
        result = result * 59 + ($userName == null ? 43 : $userName.hashCode());
        Object $dealerName = this.getDealerName();
        result = result * 59 + ($dealerName == null ? 43 : $dealerName.hashCode());
        Object $orgName = this.getOrgName();
        result = result * 59 + ($orgName == null ? 43 : $orgName.hashCode());
        Object $ownerId = this.getOwnerId();
        result = result * 59 + ($ownerId == null ? 43 : $ownerId.hashCode());
        Object $leanCheck = this.getLeanCheck();
        result = result * 59 + ($leanCheck == null ? 43 : $leanCheck.hashCode());
        Object $orgCode = this.getOrgCode();
        result = result * 59 + ($orgCode == null ? 43 : $orgCode.hashCode());
        Object $mobile = this.getMobile();
        result = result * 59 + ($mobile == null ? 43 : $mobile.hashCode());
        Object $userOrgId = this.getUserOrgId();
        result = result * 59 + ($userOrgId == null ? 43 : $userOrgId.hashCode());
        Object $consultantId = this.getConsultantId();
        result = result * 59 + ($consultantId == null ? 43 : $consultantId.hashCode());
        Object $uuid = this.getUuid();
        result = result * 59 + ($uuid == null ? 43 : $uuid.hashCode());
        return result;
    }
}
