package com.volvo.bff.volvoworks.wechat.common.model.vo.basedata;

import lombok.Data;

import java.util.List;

@Data
public class PageDto<T> {

    private Long total;
    private Integer pageSize;
    private Integer pageNum;
    List<T> rows;

    public PageDto(Long total, Integer pageSize, Integer pageNum, List<T> rows) {
        this.total = total;
        this.pageSize = pageSize;
        this.pageNum = pageNum;
        this.rows = rows;
    }

    public PageDto(Long total, List<T> rows) {
        this.total = total;
        this.rows = rows;
    }

    public PageDto() {
    }
}
