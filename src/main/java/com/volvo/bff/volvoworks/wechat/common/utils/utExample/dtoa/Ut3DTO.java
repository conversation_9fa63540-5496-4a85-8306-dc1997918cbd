package com.volvo.bff.volvoworks.wechat.common.utils.utExample.dtoa;


import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
public class Ut3DTO {
    private String unsecureWarehouse;
    private Float successfulPayment;
    private Set maxPrice;
    private Boolean nextMessage;
    private Boolean maxItem;
    private Boolean approvedWarehouse;
    private Double currentLog;
    private String initialPermission;
    private String initialFolder;
    private List draftStock;
    private Boolean inactiveCoupon;
    private Long secondarySession;
    private BigDecimal maxAsset;
    private Set maxFolder;
    private String activeTask;
    private String unpublishedEmail;
    private Integer reportedPermission;
    private Set unsecureShipment;
    private List unreportedReview;
    private List verifiedSchedule;
    private String publishedCart;
    private List nextPermission;
    private String modifiedTeam;
    private Set permanentGoal;
    private String syncedProject;
    private Set decryptedFeedback;
    private List successfulNotification;
    private BigDecimal reportedTeam;
    private Set activeSupplier;
    private Float unpublishedUser;
    private Long loadedItem;
    private String previousHistory;
    private Double approvedShipment;
    private BigDecimal failedPlan;
    private Long reportedMerchant;
    private Double hiddenSupplier;
    private String privatePrice;
    private Float hiddenUser;
    private String openFile;
    private Long sharedSession;
    private String approvedSchedule;
    private String typeInvoice;
    private String modifiedStock;
    private BigDecimal firstCategory;
    private Long exportedPolicy;
    private Double unreportedStrategy;
    private Set visibleDepartment;
    private BigDecimal finalProduct;
    private Long updatedBill;
    private String decryptedResource;
    private Float maxSubscription;
    private Float primarySupplier;
    private String previousStrategy;
    private Long currentRecord;
    private Integer currentOrder;
    private String permanentMerchant;
    private Boolean flaggedPayment;
    private Set lockedProduct;
    private BigDecimal levelPlan;
    private Set hiddenPrice;
    private Long disabledGoal;
    private String verifiedGroup;
    private Long archivedStrategy;
    private Integer primaryTask;
    private String unreviewedWarehouse;
    private Integer countShipment;
    private String updatedFolder;
    private String previousEmail;
    private Float importedShipment;
    private Set mainCart;
    private Set sharedCustomer;
    private Float publishedCustomer;
    private Set lockedTransaction;
    private Long sharedDate;
    private Integer loadedNotification;
    private Integer enabledSchedule;
    private BigDecimal levelUser;
    private Double rejectedAsset;
    private Long unpublishedCart;
    private Long savedAsset;
    private Boolean unreportedCoupon;
    private Double activeFolder;
    private BigDecimal visibleStrategy;
    private Set countPrice;
    private Set lockedEmail;
    private Set closedAchievement;
    private String mainSupplier;
    private Float exportedAudit;
    private String temporarySubscription;
    private Integer privateBrand;
    private Boolean createdPrice;
    private Float reportedCategory;
    private List importedTeam;
    private Boolean encryptedEmail;
    private BigDecimal reviewedReport;
    private Integer restoredRecord;
    private Integer activeUser;
    private Float pendingGoal;

    public String getUnsecureWarehouse() {
        return unsecureWarehouse;
    }

    public void setUnsecureWarehouse(String unsecureWarehouse) {
        this.unsecureWarehouse = unsecureWarehouse;
    }

    public Float getSuccessfulPayment() {
        return successfulPayment;
    }

    public void setSuccessfulPayment(Float successfulPayment) {
        this.successfulPayment = successfulPayment;
    }

    public Set getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(Set maxPrice) {
        this.maxPrice = maxPrice;
    }

    public Boolean getNextMessage() {
        return nextMessage;
    }

    public void setNextMessage(Boolean nextMessage) {
        this.nextMessage = nextMessage;
    }

    public Boolean getMaxItem() {
        return maxItem;
    }

    public void setMaxItem(Boolean maxItem) {
        this.maxItem = maxItem;
    }

    public Boolean getApprovedWarehouse() {
        return approvedWarehouse;
    }

    public void setApprovedWarehouse(Boolean approvedWarehouse) {
        this.approvedWarehouse = approvedWarehouse;
    }

    public Double getCurrentLog() {
        return currentLog;
    }

    public void setCurrentLog(Double currentLog) {
        this.currentLog = currentLog;
    }

    public String getInitialPermission() {
        return initialPermission;
    }

    public void setInitialPermission(String initialPermission) {
        this.initialPermission = initialPermission;
    }

    public String getInitialFolder() {
        return initialFolder;
    }

    public void setInitialFolder(String initialFolder) {
        this.initialFolder = initialFolder;
    }

    public List getDraftStock() {
        return draftStock;
    }

    public void setDraftStock(List draftStock) {
        this.draftStock = draftStock;
    }

    public Boolean getInactiveCoupon() {
        return inactiveCoupon;
    }

    public void setInactiveCoupon(Boolean inactiveCoupon) {
        this.inactiveCoupon = inactiveCoupon;
    }

    public Long getSecondarySession() {
        return secondarySession;
    }

    public void setSecondarySession(Long secondarySession) {
        this.secondarySession = secondarySession;
    }

    public BigDecimal getMaxAsset() {
        return maxAsset;
    }

    public void setMaxAsset(BigDecimal maxAsset) {
        this.maxAsset = maxAsset;
    }

    public Set getMaxFolder() {
        return maxFolder;
    }

    public void setMaxFolder(Set maxFolder) {
        this.maxFolder = maxFolder;
    }

    public String getActiveTask() {
        return activeTask;
    }

    public void setActiveTask(String activeTask) {
        this.activeTask = activeTask;
    }

    public String getUnpublishedEmail() {
        return unpublishedEmail;
    }

    public void setUnpublishedEmail(String unpublishedEmail) {
        this.unpublishedEmail = unpublishedEmail;
    }

    public Integer getReportedPermission() {
        return reportedPermission;
    }

    public void setReportedPermission(Integer reportedPermission) {
        this.reportedPermission = reportedPermission;
    }

    public Set getUnsecureShipment() {
        return unsecureShipment;
    }

    public void setUnsecureShipment(Set unsecureShipment) {
        this.unsecureShipment = unsecureShipment;
    }

    public List getUnreportedReview() {
        return unreportedReview;
    }

    public void setUnreportedReview(List unreportedReview) {
        this.unreportedReview = unreportedReview;
    }

    public List getVerifiedSchedule() {
        return verifiedSchedule;
    }

    public void setVerifiedSchedule(List verifiedSchedule) {
        this.verifiedSchedule = verifiedSchedule;
    }

    public String getPublishedCart() {
        return publishedCart;
    }

    public void setPublishedCart(String publishedCart) {
        this.publishedCart = publishedCart;
    }

    public List getNextPermission() {
        return nextPermission;
    }

    public void setNextPermission(List nextPermission) {
        this.nextPermission = nextPermission;
    }

    public String getModifiedTeam() {
        return modifiedTeam;
    }

    public void setModifiedTeam(String modifiedTeam) {
        this.modifiedTeam = modifiedTeam;
    }

    public Set getPermanentGoal() {
        return permanentGoal;
    }

    public void setPermanentGoal(Set permanentGoal) {
        this.permanentGoal = permanentGoal;
    }

    public String getSyncedProject() {
        return syncedProject;
    }

    public void setSyncedProject(String syncedProject) {
        this.syncedProject = syncedProject;
    }

    public Set getDecryptedFeedback() {
        return decryptedFeedback;
    }

    public void setDecryptedFeedback(Set decryptedFeedback) {
        this.decryptedFeedback = decryptedFeedback;
    }

    public List getSuccessfulNotification() {
        return successfulNotification;
    }

    public void setSuccessfulNotification(List successfulNotification) {
        this.successfulNotification = successfulNotification;
    }

    public BigDecimal getReportedTeam() {
        return reportedTeam;
    }

    public void setReportedTeam(BigDecimal reportedTeam) {
        this.reportedTeam = reportedTeam;
    }

    public Set getActiveSupplier() {
        return activeSupplier;
    }

    public void setActiveSupplier(Set activeSupplier) {
        this.activeSupplier = activeSupplier;
    }

    public Float getUnpublishedUser() {
        return unpublishedUser;
    }

    public void setUnpublishedUser(Float unpublishedUser) {
        this.unpublishedUser = unpublishedUser;
    }

    public Long getLoadedItem() {
        return loadedItem;
    }

    public void setLoadedItem(Long loadedItem) {
        this.loadedItem = loadedItem;
    }

    public String getPreviousHistory() {
        return previousHistory;
    }

    public void setPreviousHistory(String previousHistory) {
        this.previousHistory = previousHistory;
    }

    public Double getApprovedShipment() {
        return approvedShipment;
    }

    public void setApprovedShipment(Double approvedShipment) {
        this.approvedShipment = approvedShipment;
    }

    public BigDecimal getFailedPlan() {
        return failedPlan;
    }

    public void setFailedPlan(BigDecimal failedPlan) {
        this.failedPlan = failedPlan;
    }

    public Long getReportedMerchant() {
        return reportedMerchant;
    }

    public void setReportedMerchant(Long reportedMerchant) {
        this.reportedMerchant = reportedMerchant;
    }

    public Double getHiddenSupplier() {
        return hiddenSupplier;
    }

    public void setHiddenSupplier(Double hiddenSupplier) {
        this.hiddenSupplier = hiddenSupplier;
    }

    public String getPrivatePrice() {
        return privatePrice;
    }

    public void setPrivatePrice(String privatePrice) {
        this.privatePrice = privatePrice;
    }

    public Float getHiddenUser() {
        return hiddenUser;
    }

    public void setHiddenUser(Float hiddenUser) {
        this.hiddenUser = hiddenUser;
    }

    public String getOpenFile() {
        return openFile;
    }

    public void setOpenFile(String openFile) {
        this.openFile = openFile;
    }

    public Long getSharedSession() {
        return sharedSession;
    }

    public void setSharedSession(Long sharedSession) {
        this.sharedSession = sharedSession;
    }

    public String getApprovedSchedule() {
        return approvedSchedule;
    }

    public void setApprovedSchedule(String approvedSchedule) {
        this.approvedSchedule = approvedSchedule;
    }

    public String getTypeInvoice() {
        return typeInvoice;
    }

    public void setTypeInvoice(String typeInvoice) {
        this.typeInvoice = typeInvoice;
    }

    public String getModifiedStock() {
        return modifiedStock;
    }

    public void setModifiedStock(String modifiedStock) {
        this.modifiedStock = modifiedStock;
    }

    public BigDecimal getFirstCategory() {
        return firstCategory;
    }

    public void setFirstCategory(BigDecimal firstCategory) {
        this.firstCategory = firstCategory;
    }

    public Long getExportedPolicy() {
        return exportedPolicy;
    }

    public void setExportedPolicy(Long exportedPolicy) {
        this.exportedPolicy = exportedPolicy;
    }

    public Double getUnreportedStrategy() {
        return unreportedStrategy;
    }

    public void setUnreportedStrategy(Double unreportedStrategy) {
        this.unreportedStrategy = unreportedStrategy;
    }

    public Set getVisibleDepartment() {
        return visibleDepartment;
    }

    public void setVisibleDepartment(Set visibleDepartment) {
        this.visibleDepartment = visibleDepartment;
    }

    public BigDecimal getFinalProduct() {
        return finalProduct;
    }

    public void setFinalProduct(BigDecimal finalProduct) {
        this.finalProduct = finalProduct;
    }

    public Long getUpdatedBill() {
        return updatedBill;
    }

    public void setUpdatedBill(Long updatedBill) {
        this.updatedBill = updatedBill;
    }

    public String getDecryptedResource() {
        return decryptedResource;
    }

    public void setDecryptedResource(String decryptedResource) {
        this.decryptedResource = decryptedResource;
    }

    public Float getMaxSubscription() {
        return maxSubscription;
    }

    public void setMaxSubscription(Float maxSubscription) {
        this.maxSubscription = maxSubscription;
    }

    public Float getPrimarySupplier() {
        return primarySupplier;
    }

    public void setPrimarySupplier(Float primarySupplier) {
        this.primarySupplier = primarySupplier;
    }

    public String getPreviousStrategy() {
        return previousStrategy;
    }

    public void setPreviousStrategy(String previousStrategy) {
        this.previousStrategy = previousStrategy;
    }

    public Long getCurrentRecord() {
        return currentRecord;
    }

    public void setCurrentRecord(Long currentRecord) {
        this.currentRecord = currentRecord;
    }

    public Integer getCurrentOrder() {
        return currentOrder;
    }

    public void setCurrentOrder(Integer currentOrder) {
        this.currentOrder = currentOrder;
    }

    public String getPermanentMerchant() {
        return permanentMerchant;
    }

    public void setPermanentMerchant(String permanentMerchant) {
        this.permanentMerchant = permanentMerchant;
    }

    public Boolean getFlaggedPayment() {
        return flaggedPayment;
    }

    public void setFlaggedPayment(Boolean flaggedPayment) {
        this.flaggedPayment = flaggedPayment;
    }

    public Set getLockedProduct() {
        return lockedProduct;
    }

    public void setLockedProduct(Set lockedProduct) {
        this.lockedProduct = lockedProduct;
    }

    public BigDecimal getLevelPlan() {
        return levelPlan;
    }

    public void setLevelPlan(BigDecimal levelPlan) {
        this.levelPlan = levelPlan;
    }

    public Set getHiddenPrice() {
        return hiddenPrice;
    }

    public void setHiddenPrice(Set hiddenPrice) {
        this.hiddenPrice = hiddenPrice;
    }

    public Long getDisabledGoal() {
        return disabledGoal;
    }

    public void setDisabledGoal(Long disabledGoal) {
        this.disabledGoal = disabledGoal;
    }

    public String getVerifiedGroup() {
        return verifiedGroup;
    }

    public void setVerifiedGroup(String verifiedGroup) {
        this.verifiedGroup = verifiedGroup;
    }

    public Long getArchivedStrategy() {
        return archivedStrategy;
    }

    public void setArchivedStrategy(Long archivedStrategy) {
        this.archivedStrategy = archivedStrategy;
    }

    public Integer getPrimaryTask() {
        return primaryTask;
    }

    public void setPrimaryTask(Integer primaryTask) {
        this.primaryTask = primaryTask;
    }

    public String getUnreviewedWarehouse() {
        return unreviewedWarehouse;
    }

    public void setUnreviewedWarehouse(String unreviewedWarehouse) {
        this.unreviewedWarehouse = unreviewedWarehouse;
    }

    public Integer getCountShipment() {
        return countShipment;
    }

    public void setCountShipment(Integer countShipment) {
        this.countShipment = countShipment;
    }

    public String getUpdatedFolder() {
        return updatedFolder;
    }

    public void setUpdatedFolder(String updatedFolder) {
        this.updatedFolder = updatedFolder;
    }

    public String getPreviousEmail() {
        return previousEmail;
    }

    public void setPreviousEmail(String previousEmail) {
        this.previousEmail = previousEmail;
    }

    public Float getImportedShipment() {
        return importedShipment;
    }

    public void setImportedShipment(Float importedShipment) {
        this.importedShipment = importedShipment;
    }

    public Set getMainCart() {
        return mainCart;
    }

    public void setMainCart(Set mainCart) {
        this.mainCart = mainCart;
    }

    public Set getSharedCustomer() {
        return sharedCustomer;
    }

    public void setSharedCustomer(Set sharedCustomer) {
        this.sharedCustomer = sharedCustomer;
    }

    public Float getPublishedCustomer() {
        return publishedCustomer;
    }

    public void setPublishedCustomer(Float publishedCustomer) {
        this.publishedCustomer = publishedCustomer;
    }

    public Set getLockedTransaction() {
        return lockedTransaction;
    }

    public void setLockedTransaction(Set lockedTransaction) {
        this.lockedTransaction = lockedTransaction;
    }

    public Long getSharedDate() {
        return sharedDate;
    }

    public void setSharedDate(Long sharedDate) {
        this.sharedDate = sharedDate;
    }

    public Integer getLoadedNotification() {
        return loadedNotification;
    }

    public void setLoadedNotification(Integer loadedNotification) {
        this.loadedNotification = loadedNotification;
    }

    public Integer getEnabledSchedule() {
        return enabledSchedule;
    }

    public void setEnabledSchedule(Integer enabledSchedule) {
        this.enabledSchedule = enabledSchedule;
    }

    public BigDecimal getLevelUser() {
        return levelUser;
    }

    public void setLevelUser(BigDecimal levelUser) {
        this.levelUser = levelUser;
    }

    public Double getRejectedAsset() {
        return rejectedAsset;
    }

    public void setRejectedAsset(Double rejectedAsset) {
        this.rejectedAsset = rejectedAsset;
    }

    public Long getUnpublishedCart() {
        return unpublishedCart;
    }

    public void setUnpublishedCart(Long unpublishedCart) {
        this.unpublishedCart = unpublishedCart;
    }

    public Long getSavedAsset() {
        return savedAsset;
    }

    public void setSavedAsset(Long savedAsset) {
        this.savedAsset = savedAsset;
    }

    public Boolean getUnreportedCoupon() {
        return unreportedCoupon;
    }

    public void setUnreportedCoupon(Boolean unreportedCoupon) {
        this.unreportedCoupon = unreportedCoupon;
    }

    public Double getActiveFolder() {
        return activeFolder;
    }

    public void setActiveFolder(Double activeFolder) {
        this.activeFolder = activeFolder;
    }

    public BigDecimal getVisibleStrategy() {
        return visibleStrategy;
    }

    public void setVisibleStrategy(BigDecimal visibleStrategy) {
        this.visibleStrategy = visibleStrategy;
    }

    public Set getCountPrice() {
        return countPrice;
    }

    public void setCountPrice(Set countPrice) {
        this.countPrice = countPrice;
    }

    public Set getLockedEmail() {
        return lockedEmail;
    }

    public void setLockedEmail(Set lockedEmail) {
        this.lockedEmail = lockedEmail;
    }

    public Set getClosedAchievement() {
        return closedAchievement;
    }

    public void setClosedAchievement(Set closedAchievement) {
        this.closedAchievement = closedAchievement;
    }

    public String getMainSupplier() {
        return mainSupplier;
    }

    public void setMainSupplier(String mainSupplier) {
        this.mainSupplier = mainSupplier;
    }

    public Float getExportedAudit() {
        return exportedAudit;
    }

    public void setExportedAudit(Float exportedAudit) {
        this.exportedAudit = exportedAudit;
    }

    public String getTemporarySubscription() {
        return temporarySubscription;
    }

    public void setTemporarySubscription(String temporarySubscription) {
        this.temporarySubscription = temporarySubscription;
    }

    public Integer getPrivateBrand() {
        return privateBrand;
    }

    public void setPrivateBrand(Integer privateBrand) {
        this.privateBrand = privateBrand;
    }

    public Boolean getCreatedPrice() {
        return createdPrice;
    }

    public void setCreatedPrice(Boolean createdPrice) {
        this.createdPrice = createdPrice;
    }

    public Float getReportedCategory() {
        return reportedCategory;
    }

    public void setReportedCategory(Float reportedCategory) {
        this.reportedCategory = reportedCategory;
    }

    public List getImportedTeam() {
        return importedTeam;
    }

    public void setImportedTeam(List importedTeam) {
        this.importedTeam = importedTeam;
    }

    public Boolean getEncryptedEmail() {
        return encryptedEmail;
    }

    public void setEncryptedEmail(Boolean encryptedEmail) {
        this.encryptedEmail = encryptedEmail;
    }

    public BigDecimal getReviewedReport() {
        return reviewedReport;
    }

    public void setReviewedReport(BigDecimal reviewedReport) {
        this.reviewedReport = reviewedReport;
    }

    public Integer getRestoredRecord() {
        return restoredRecord;
    }

    public void setRestoredRecord(Integer restoredRecord) {
        this.restoredRecord = restoredRecord;
    }

    public Integer getActiveUser() {
        return activeUser;
    }

    public void setActiveUser(Integer activeUser) {
        this.activeUser = activeUser;
    }

    public Float getPendingGoal() {
        return pendingGoal;
    }

    public void setPendingGoal(Float pendingGoal) {
        this.pendingGoal = pendingGoal;
    }
}
