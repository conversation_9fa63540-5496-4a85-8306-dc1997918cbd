package com.volvo.bff.volvoworks.wechat.common.exception;

import java.util.ArrayList;
import java.util.List;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.RestResultResponse;
import org.springframework.http.HttpStatus;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.volvo.bff.volvoworks.wechat.common.utils.JSONUtil;

import lombok.extern.slf4j.Slf4j;

@ControllerAdvice
@Slf4j
public class ExceptionHandlerControllerAdvice {

    @ExceptionHandler(UtilException.class)
    @ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
    @ResponseBody
    public RestResultResponse handleCustomException(UtilException utilException) {
        return RestResultResponse.fail(utilException.getErrorMessage(), utilException.getDisplayMessage());
    }
    
    @ExceptionHandler(ServiceException.class)
    @ResponseStatus(value = HttpStatus.OK)
    @ResponseBody
    public RestResultResponse handleServiceException(ServiceException utilException) {
        return RestResultResponse.fail(utilException.getErrorMessage(), utilException.getDisplayMessage());
    }

    @ExceptionHandler(DmsException.class)
    @ResponseStatus(value = HttpStatus.OK)
    @ResponseBody
    public RestResultResponse handleServiceException(DmsException utilException) {
        return RestResultResponse.fail(utilException.getResultCode(), utilException.getCode(), utilException.getMsg(), utilException.getErrorMessage(), utilException.getDisplayMessage());
    }
    
    /**
     * 通用接口参数校验处理
     *
     * @param ex
     * @return
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public RestResultResponse<String> handleMethodArgumentNotValidExceptions(Exception e) {
            MethodArgumentNotValidException ex = (MethodArgumentNotValidException) e;
            List<String> errorList = new ArrayList<>();
            ex.getBindingResult().getAllErrors().forEach(error -> {
                String fieldName = ((FieldError) error).getField();
                String errorMessage = error.getDefaultMessage();
                errorList.add(String.format("%s: %s;", fieldName, errorMessage));
            });
            return RestResultResponse.requestValidationError(JSONUtil.objectToJson(errorList));
    }
}
