package com.volvo.bff.volvoworks.wechat.common.model.dto.pds;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@ApiModel("ItemGroupModeDTO")
public class ItemGroupModeDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	// ID
	@ApiModelProperty(name = "pdsInfoId")
	private Integer pdsInfoId;
	// 经销商名称
	@ApiModelProperty(name = "companyName")
	private String companyName;
	// 是否强制完成
	@ApiModelProperty(name = "mandatoryCompletion")
	private Integer mandatoryCompletion;
	// 经销商code
	@ApiModelProperty(name = "ownerCode")
	private String ownerCode;
	// 初检完成时间
	@ApiModelProperty(name = "firstCheckTime")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String firstCheckTime;
	// 终检技师ID
	@ApiModelProperty(name = "lastSaId")
	private Integer lastSaId;
	// 里程
	@ApiModelProperty(name = "mileage")
	private Integer mileage;
	// 创建时间
	@ApiModelProperty(name = "createTime")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String createTime;
	// 强制完成原因
	@ApiModelProperty(name = "mandatoryDescription")
	private String mandatoryDescription;
	// 车架号
	@ApiModelProperty(name = "vin")
	private String vin;
	// 创建人技师ID
	@ApiModelProperty(name = "createSaId")
	private Integer createSaId;
	// 创建人技师名称
	@ApiModelProperty(name = "createBy")
	private String createBy;
	// 初检技师名称
	@ApiModelProperty(name = "firstSaName")
	private String firstSaName;
	// ocr识别车架号path
	@ApiModelProperty(name = "ocrVin")
	private String ocrVin;
	// 车型主图path
	@ApiModelProperty(name = "vinImage")
	private String vinImage;
	// 车型name
	@ApiModelProperty(name = "modeName")
	private String modeName;
	// 终检技师名称
	@ApiModelProperty(name = "lastSaName")
	private String lastSaName;
	// 终检完成时间
	@ApiModelProperty(name = "lastCheckTime")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String lastCheckTime;
	// 初检技师ID
	@ApiModelProperty(name = "firstSaId")
	private Integer firstSaId;
	// 车牌号
	@ApiModelProperty(name = "license")
	private String license;
	// pds状态
	@ApiModelProperty(name = "pdsStatus")
	private Integer pdsStatus;
	// 车型code
	@ApiModelProperty(name = "modeCode")
	private String modeCode;
	// pds单号
	@ApiModelProperty(name = "pdsNo")
	private String pdsNo;
	// pds车辆标记图片
	@ApiModelProperty(name = "pdsDataPath")
	private String pdsDataPath;
	// reserve1
	@ApiModelProperty(name = "pdsInfoReserve1")
	private String pdsInfoReserve1;
	// reserve2
	@ApiModelProperty(name = "pdsInfoReserve2")
	private String pdsInfoReserve2;
	// reserve3
	@ApiModelProperty(name = "pdsInfoReserve3")
	private String pdsInfoReserve3;

	// pds资料信息
	@ApiModelProperty(name = "pdsData")
	private List<PdsDataDTO> pdsData;
	// 检查正常集合
	@ApiModelProperty(name = "checkNormal")
	private List<NormalDTO> checkNormal;
	// 检查调整集合
	@ApiModelProperty(name = "checkAndAdjust")
	private List<AndAdjustDTO> checkAndAdjust;
	// 检查异常集合
	@ApiModelProperty(name = "checkException")
	private List<ExceptionDTO> checkException;
	// 检查未适用
	@ApiModelProperty(name = "checkNotApplicable")
	private List<ExceptionDTO> checkNotApplicable;
}