package com.volvo.bff.volvoworks.wechat.common.model.dto.accidentClues;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("事故线索导出查询参数")
public class AccidentCluesExportQueryDto {
    @ApiModelProperty("当前页")
    private Integer currentPage;

    @ApiModelProperty("分页量")
    private Integer pageSize;

    @ApiModelProperty("区分厂店")
    private String source;

    @ApiModelProperty("经销商代码")
    private String dealerCode;

    private String isself;

    @ApiModelProperty(value = "跟进人员id", name = "followPeople")
    private Integer followPeople;

    @ApiModelProperty("线索创建/推送时间起始")
    private String createdDateStart;

    @ApiModelProperty("线索创建/推送时间结束")
    private String createdDateEnd;

    @ApiModelProperty(value = "报案时间开始时间")
    private String reportDateStart;

    @ApiModelProperty(value = "报案时间结束时间")
    private String reportDateEnd;

    @ApiModelProperty(value = "下次跟进开始时间")
    private String nextFollowDateStart;

    @ApiModelProperty(value = "下次跟进结束时间")
    private String nextFollowDateEnd;

    @ApiModelProperty("车牌")
    private String license;

    @ApiModelProperty(value = "保险公司id", name = "insuranceCompanyId")
    private String insuranceCompanyId;

    @ApiModelProperty(value = "保险公司名称", name = "insuranceCompanyName")
    private String insuranceCompanyName;

    @ApiModelProperty(value = "跟进状态", name = "followStatus")
    private Integer followStatus;

    @ApiModelProperty(value = "线索状态", name = "cluesStatus")
    private Integer cluesStatus;

    @ApiModelProperty(value = "分配状态", name = "allotStatus")
    private Integer allotStatus;

    @ApiModelProperty("主键list")
    private List<Integer> acIdList;

    @ApiModelProperty(value = "跟进状态集合", name = "followStatusList")
    private List<Integer> followStatusList;

    @ApiModelProperty(value = "车架号")
    private String vin;

    @ApiModelProperty("线索来源（多选）")
    private List<Integer> cluesResourceList;

    @ApiModelProperty(value = "售后大区ID")
    private Long afterBigAreaId;

    @ApiModelProperty(value = "售后小区ID")
    private Long afterSmallAreaId;

    @ApiModelProperty(value = "保司来源")
    private String insuranceSource;

    @ApiModelProperty(value = "线索异常状态")
    private Integer dataStatus;

    @ApiModelProperty(value = "省ID")
    private Long    provinceId;

    @ApiModelProperty(value = "市ID")
    private Long    cityId;
    @ApiModelProperty(value = "经销商CODE")
    private List<String>    dealerCodeList;
    @ApiModelProperty(value = "来源渠道")
    private List<String> insuranceSourceList;
}
