package com.volvo.bff.volvoworks.wechat.common.service.basedata.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.dto.basedata.ReportPayOffDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import com.volvo.bff.volvoworks.wechat.common.service.basedata.ReportPayOffService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.Map;

@Service
public class ReportPayOffServiceImpl implements ReportPayOffService {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Override
    public PageInfoDto findAllList(Map<String, String> map){
        DmsResultDTO<PageInfoDto> response = dmscloudServiceFeign.findAllList(map);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public List<Map> findItemByPartProfit(String profitNo){
        DmsResultDTO<List<Map>> response = dmscloudServiceFeign.findItemByPartProfit(profitNo);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

    @Override
    public ResponseEntity<ReportPayOffDTO> btnAccount(ReportPayOffDTO dto){
        DmsResultDTO<ResponseEntity<ReportPayOffDTO>> response = dmscloudServiceFeign.btnAccount(dto);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }
}
