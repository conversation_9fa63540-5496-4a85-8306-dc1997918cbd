package com.volvo.bff.volvoworks.wechat.common.model.vo.accidentClues;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 *
* TODO description
* <AUTHOR>
* @date 2020年6月23日
 */
@ApiModel("工单接口入参Vo")
@Data
public class RepairOrderHistoryParamsVO {

	@ApiModelProperty(value = "经销商代码", name = "ownerCode")
	@NotNull(message = "经销商不能为null")
	private String ownerCode;

	@ApiModelProperty(value = "工单号", name = "roNo")
	private String roNo;

	@ApiModelProperty(value = "开单时间-开始", name = "roCreateDateBegin")
	private String roCreateDateBegin;

	@ApiModelProperty(value = "开单时间-结束", name = "roCreateDateEnd")
	private String roCreateDateEnd;

	@ApiModelProperty(value = "车牌", name = "license")
	private String license;

	@ApiModelProperty(value = "VIN", name = "vin")
	private String vin;

	@ApiModelProperty(value = "车主姓名", name = "ownerName")
	private String ownerName;

	@ApiModelProperty(value = "服务顾问", name = "serviceAdvisor")
	private String serviceAdvisor;

	@ApiModelProperty(value = "零件编号", name = "partNo")
	private String partNo;

	@ApiModelProperty(value = "零件名称", name = "partName")
	private String partName;

	@ApiModelProperty(value = "工单状态(80491003)", name = "roStatus")
    private Integer roStatus;

	@ApiModelProperty(value = "维修类型", name = "repairTypeCode")
    private String repairTypeCode;

}
