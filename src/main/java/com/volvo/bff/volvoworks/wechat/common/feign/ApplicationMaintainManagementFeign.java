package com.volvo.bff.volvoworks.wechat.common.feign;

import com.volvo.bff.volvoworks.wechat.common.annotation.ErrorCodeConversion;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.LucencyWorkshop.*;
import com.volvo.bff.volvoworks.wechat.common.model.dto.LucencyWorkshop.QueryParamDto;
import com.volvo.bff.volvoworks.wechat.common.model.dto.accidentClues.*;
import com.volvo.bff.volvoworks.wechat.common.model.dto.icup.IcupMileageDto;
import com.volvo.bff.volvoworks.wechat.common.model.dto.part.ListPartBuyItemDto;
import com.volvo.bff.volvoworks.wechat.common.model.dto.part.ShortPartResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.vhc.*;
import com.volvo.bff.volvoworks.wechat.common.model.dto.workshop.BookingOrderDto;
import com.volvo.bff.volvoworks.wechat.common.model.dto.workshop.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.accidentClues.AccidentClueListVO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.accidentClues.DealerVO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.BookingOrderVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.ReportReasonDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@FeignClient(name = "application-maintain-management",
		url = "${mse-in.tob.application-maintain-management.url}",
		path = "${mse-in.tob.application-maintain-management.path}")
public interface ApplicationMaintainManagementFeign {
	@PostMapping(value = "/workshop/api/v1/syncPartStatus")
	DmsResultDTO<List<ShortPartResultDTO>> syncPartStatus(@RequestBody ListPartBuyItemDto listPartBuyItemDto);


	/**
	 * 查询全网经销商
	 * @param params
	 * @return
	 */
	@PostMapping(value = "/accidentClues/getDealerList", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	DmsResultDTO<DealerVO> getDealerList(@RequestBody DealerDTO params);

	/**
	 * 事故线索集合
	 */
	@PostMapping(value = "/accidentClues/list")
	DmsResultDTO<List<AccidentClueListVO>> accidentClueList(@RequestBody AccidentCluesListQueryDto params);

	/**
	 * 事故线索-根据acId查询虚拟手机号
	 */
	@PostMapping(value = "/accidentClues/getVirtualNumber", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	DmsResultDTO<String> getVirtualNumber(@RequestParam("acId") Long acId);

	/**
	 * 线索分配经销商
	 * @param params
	 * @return
	 */
	@PostMapping(value = "/accidentClues/allotDealer", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	DmsResultDTO<Void> allotDealer(@RequestBody List<AllotDealerDTO> params);

	/**
	 * 查询事故线索统计数据
	 * @param dto
	 * @return
	 */
	@PostMapping("/accidentClues/cluePool/getSumAccidentInfo")
	DmsResultDTO<AccidentCluesSumInfoDTO> getSumAccidentInfo(@RequestBody AccidentCluesExportQueryDto dto);

	@PostMapping("/workshop/weCom/queryBookingOrder")
	DmsResultDTO<PageBean<BookingOrderDto>> queryBookingWeCom(@RequestBody BookingOrderVo bookingOrderVo);

	@PostMapping("/workshop/weCom/queryBookingStatusCount")
	DmsResultDTO<BookingStatusDto> queryBookingStatusCount(@RequestBody BookingOrderVo bookingOrderVo);

	@PostMapping(value = "/lucencyWorkShop/v1/selectDeliveryList")
	@ResponseBody
	DmsResultDTO<PageBean<DeliveryDTO>> selectDeliveryList(@RequestBody QueryParamDto queryParamDto);

	//查询em90工单交车弹框 true弹框  false不弹框
	@GetMapping(value = "/em90Delivery/selectEm90DeliveryFrame")
	DmsResultDTO<Boolean> selectEm90DeliveryFrame(@RequestParam("ownerCode") String ownerCode,@RequestParam("roNo")String roNo,@RequestParam("vin")String vin);

	//提交未提交报告原因
	@PostMapping(value = "/em90Delivery/submitReportReason")
	DmsResultDTO<Void> submitReportReason(@RequestBody ReportReasonDto reportReasonDto);


	@PostMapping(value = "/em90Delivery/em90TakeDeliverPrecheckEmail")
	DmsResultDTO<Void> em90TakeDeliverPrecheckEmail(@RequestBody VehicleEntranceVo vehicleEntranceVO);

	@ApiOperation("查询icup车型里程")
	@GetMapping("/icup/getIcupMileageByVin")
	DmsResultDTO<IcupMileageDto> getIcupMileageByVin(@RequestParam("vin") String vin);

	/**
	 * 服务看板缺件查询（企微店端）
	 */
	@PostMapping("/workshop/weCom/queryShortPart")
	DmsResultDTO<PageBean<ShortPartItemWeComDto>> queryShortPartWeCom(@RequestBody ReminderShortPartItemVo shortPartItemVo);

	/**
	 * 查询未到货，已到货，部分到货数量
	 *
	 * @return 返回对象（包含数量）
	 */
	@PostMapping("/workshop/weCom/getShortPartStatus")
	DmsResultDTO<MissingPartsStatusDto> getShortPartStatus(@RequestBody ReminderShortPartItemVo shortPartItemVo);

	/**
	 * 根据缺料主键查询 明细
	 *
	 * @param shortPartItemVo 明细数据
	 * @return 明细数据
	 */
	@PostMapping("/workshop/weCom/queryShortPartItem")
	DmsResultDTO<ReminderShortPartItemDto> queryShortPartItem(@RequestBody ReminderShortPartItemVo shortPartItemVo);

	/**
	 * 记录通话记录
	 * @param workshopCallRecordDto 通话记录详情
	 * @return true false
	 */
	@PostMapping("/workshop/weCom/addCallLog")
	DmsResultDTO<Boolean> addCallLog(@RequestBody WorkshopCallRecordDto workshopCallRecordDto);

	/**
	 * 查询通话记录
	 * @param ownerCode 经销商
	 * @param roNo 工单号
	 * @param serviceAdvisor 服务顾问
	 * @return 通话记录
	 */
	@GetMapping("/workshop/weCom/callItem")
	DmsResultDTO<PageBean<WorkshopCallRecordDto>> queryCallItem(@RequestParam String ownerCode, @RequestParam String roNo, @RequestParam String serviceAdvisor, @RequestParam Integer pageNum, @RequestParam Integer pageSize);

	/**
	 * 修改预计交车时间
	 * @param roNo 工单号
	 * @param endTimeSupposed 预交车时间
	 */
	@GetMapping("/workshop/roStatus/roNo")
	DmsResultDTO<Void> updateRepairOrderStatus(@RequestParam("roNo") String roNo, @RequestParam("endTimeSupposed") String endTimeSupposed);


	/**
	 * 开单页面查询
	 * @param queryParamDto
	 * @return
	 */
	@PostMapping(value = "/lucencyWorkShop/v1/selectBeginOrderList")
	@ResponseBody
	DmsResultDTO<PageBean<BeginOrderDTO>> selectBeginOrderList(@RequestBody QueryParamDto queryParamDto);

	/**
	 * 查询派工数量
	 * @param queryParamDto
	 * @return
	 */
	@PostMapping(value = "/lucencyWorkShop/v1/selectAssignStatusCount")
	@ResponseBody
	DmsResultDTO<List<StatusCountDTO>> selectAssignStatusCount(@RequestBody QueryParamDto queryParamDto);

	/**
	 * 查询维修页面
	 * @param queryParamDto
	 * @return
	 */
	@PostMapping(value = "/lucencyWorkShop/v1/selectWorkshopRepairOrder")
	@ResponseBody
	DmsResultDTO<PageBean<WorkShopRepairOrderDTO>> selectWorkshopRepairOrder(@RequestBody QueryParamDto queryParamDto);

	/**
	 * 查询打卡数量
	 * @param queryParamDto
	 * @return
	 */
	@PostMapping(value = "/lucencyWorkShop/v1/selectIsPunchCount")
	@ResponseBody
	DmsResultDTO<List<StatusCountDTO>> selectIsPunchCount(@RequestBody QueryParamDto queryParamDto);

	/**
	 * 查询结算页面
	 */
	@PostMapping(value = "/lucencyWorkShop/v1/selectWorkShopBalanceOrder")
	@ResponseBody
	DmsResultDTO<PageBean<WorkShopBalanceOrderDTO>> selectWorkShopBalanceOrder(@RequestBody QueryParamDto queryParamDto);

	/**
	 * 查询交车数量
	 */
	@PostMapping(value = "/lucencyWorkShop/v1/selectDeliveryTagCount")
	@ResponseBody
	DmsResultDTO<List<StatusCountDTO>> selectDeliveryTagCount(@RequestBody QueryParamDto queryParamDto);

	/**
	 * 检查页面查询
	 */
	@PostMapping("/vhcRepair/v1/selectVhcList")
	@ApiOperation(value = "检查页面查询")
	DmsResultDTO<PageBean<VhcDetailsDTO>> selectVhcList(@RequestBody QueryVhcDto queryVhcDto);

	/**
	 * 报价页面查询
	 */
	@PostMapping("/vhcRepair/v1/selectVhcPricesheetList")
	@ApiOperation(value = "报价页面查询")
	DmsResultDTO<PageBean<VhcPricesheetDetailsDTO>> selectVhcPricesheetList(@RequestBody QueryVhcDto queryVhcDto);

	/**
	 * 查询车辆健康检查详情
	 */
	@PostMapping("/vehicleHealthCheck/v1/getVehicleHealthCheckDetail")
	@ApiOperation(value = "查询车辆健康检查详情")
	@ErrorCodeConversion
	DmsResultDTO<VehicleHealthCheckDetailDto> getVehicleHealthCheckDetail(@RequestBody VehicleHealthCheckDetailParamDto vehicleHealthCheckDetailParamDto);

	/**
	 * 根据大类id查询小类信息
	 */
	@GetMapping("/vehicleHealthCheck/v1/getVhcItemInfoByClassId")
	@ApiOperation(value = "根据大类id查询小类信息")
	@ErrorCodeConversion
	DmsResultDTO<List<VhcItemConfigInfoDto>> getVhcItemInfoByClassId(@RequestParam("classId") Integer classId, @RequestParam("configClassId") String configClassId);

	/**
	 * 保存车辆健康检查信息
	 */
	@PostMapping("/vehicleHealthCheck/v1/saveVehicleHealthCheckInfo")
	@ApiOperation(value = "保存车辆健康检查信息")
	@ErrorCodeConversion
	DmsResultDTO<Void> saveVehicleHealthCheckInfo(@RequestBody VehicleHealthCheckInfoDto vehicleHealthCheckInfoDto);

	/**
	 *  VHC-报价-维修项信息查询
	 * @return 解析数据
	 */
	@GetMapping(value = "/workOrder/v1/queryMaintenanceItems", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	DmsResultDTO<VhcQuotedDTO> queryMaintenanceItems(@RequestParam("vhcNo") String vhcNo);
	/**
	 * 代客户反向确认维修项
	 * @param dto
	 * @return
	 */
	@PostMapping(value = "/workOrder/v1/confirmRepair", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	DmsResultDTO<Void> confirmRepair(@RequestBody VhcConfirmRepairDTO dto);

	/**
	 * 推送用户
	 * @return
	 */
	@PostMapping(value = "/workOrder/v1/pushCustomer")
	DmsResultDTO<Void> pushCustomer(@RequestParam(value = "vhcNo") String vhcNo, @RequestParam(value = "roNo") String roNo, @RequestParam(value = "flag") Integer flag, @RequestParam(value = "itemIds") String itemIds);

	/**
	 * 记录异常送修人工单
	 *
	 * @param roNo      工单号
	 * @param traceTime 回访时间类型
	 * @return map 返回结果
	 */
	@RequestMapping(value = "/deliverOrder/alterTraceTime/{roNo}/{traceTime}", method = RequestMethod.PUT)
	DmsResultDTO<Map<String, Object>> deliveryOrderExceptionRecord(@PathVariable("roNo") String roNo, @PathVariable("traceTime") String traceTime);
}
