package com.volvo.bff.volvoworks.wechat.common.service.workshop;

import com.volvo.bff.volvoworks.wechat.common.model.dto.workshop.*;

public interface WechatEnterpriseService {

    /**
     * 拉人进群
     * @param inviteUser
     * @return
     */
    Boolean inviteUserAddGroup(InviteUserDTO inviteUser);

    /**
     * 是否存在
     * @param checkGroupDTO
     * @return
     */
    Integer checkGroup(CheckGroupDTO checkGroupDTO);

    /**
     * 根据code获取token
     * @param code
     * @return
     */
    QwTokenDTO getNewbieTokenAsync(String code);

    /**
     * 获取签名密钥
     * @param url
     * @return
     */
    QwJSSDKDTO getQYWeChatAgentJSSDKConfig(String url);
}
