package com.volvo.bff.volvoworks.wechat.common.model.vo.applicationMaintenanceentertain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * tt_reception_entrance
 * 
 * <AUTHOR>
 * @version 1.0.0 2023-04-17
 */

@Data
public class SubmitEntranceInfoReqVO implements Serializable {
    /** 版本号 */
    private static final long serialVersionUID = 8516095990705980435L;

    private String vehicleEntranceId;
    
    private String entranceId;
    
    /** 经销商code */
    private String dealerCode;

    /** 车牌号 */
    private String licensePlate;

    /** 进场时间 */
    @JsonFormat(pattern="yyyy/MM/dd HH:mm:ss",timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private Date entryTime;
    
    /** 提交类型 快速服务：0 | 特殊车辆登记：1 */
    private String type;

    /** 是否volvo车辆（是-0，否-1） */
    private Integer isVolvoBrand;
    
    /** 服务来源（快速服务建单：0/手动建单：1）  */
    private Integer serviceSource;

    /** 服务类型（检修:0、洗车:1、打气:2、其他:3） */
    private Integer serviceType;

    /** 车架号 */
    private String vin;
    
    /** 特殊车辆类型 （试乘试驾:0、代步车:1、员工车:2、二手车:3） */
    private Integer specialVehicleType;

    /** 情况说明 */
    private String situationExplain;

    /** 补充说明 */
    private String replenishExplain;

    /** expandField */
    private String expandField;
}