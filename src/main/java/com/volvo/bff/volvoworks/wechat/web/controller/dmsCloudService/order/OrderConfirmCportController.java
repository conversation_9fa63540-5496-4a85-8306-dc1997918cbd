package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.order;


import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.order.OrderConfirmCportService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



@Api(tags = "电子工单c端接口")
@RestController
@Slf4j
@RequestMapping("/orderCporConfirm")
public class OrderConfirmCportController {



    @Autowired
    private OrderConfirmCportService orderConfirmCportService;


    /**
     * 查询经销商是否为电子工单白名单
     */
    @ApiOperation(value = "查询经销商是否为电子工单白名单 ", notes = "查询经销商是否为电子工单白名单", httpMethod = "GET")
    @GetMapping(value = "/selectWhitelistByOwnerCode/customerInterAspect")
    @ResponseBody
    public ResponseDTO<Boolean> selectWhitelistByOwnerCode(String ownerCode){
        return ResponseUtils.success(orderConfirmCportService.selectWhitelistByOwnerCode(ownerCode));
    }

}
