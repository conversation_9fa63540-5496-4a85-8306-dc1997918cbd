package com.volvo.bff.volvoworks.wechat.common.model.po.accidentClues;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 工单表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-06
 */
@Data
public class RepairOrderPO  {

	private static final long serialVersionUID = 1L;



	/**
	 * 洗车开始时间
	 */
	private String carWashTimeBegin;


	/**
	 * 洗车时间
	 */
	private String carWashTime;


	/**
	 * 所有者代码
	 */
	private String ownerCode;



	/**
	 * 组织ID
	 */
	private Integer orgId;


	/**
	 * 工单号
	 */
	private String roNo;

	/**
	 * 关联工单号
	 */
	private String relationRoNo;

	/**
	 * 取车订单id
	 */
	private Integer pickUpOrder;

	/**
	 * 送车订单id
	 */
	private Integer deliveryOrder;

	/**
	 * 是否录入
	 */
	private String isInput;

	/**
	 * 配件销售单号
	 */
	private String salesPartNo;

	/**
	 * 预约单号
	 */
	private String bookingOrderNo;

	/**
	 * 估价单号
	 */
	private String estimateNo;

	/**
	 * 工单类型
	 */
	private Integer roType;

	/**
	 * 维修类型代码
	 */
	private String repairTypeCode;

	/**
	 * 客述类型
	 */
	private String ksName;

	/**
	 * 客述类型联动1
	 */
	private String ksCode;

	/**
	 * 客述类型说明
	 */
	private String tips;

	/**
	 * 其他维修类型
	 */
	private String otherRepairType;

	/**
	 * 原工单号
	 */
	private String primaryRoNo;

	/**
	 * 操作人
	 */
	private String operator;

	/**
	 * 理赔单号
	 */
	private String insurationNo;

	/**
	 * 保险公司代码
	 */
	private String insurationCode;

	/**
	 * 客户是否在厂
	 */
	private Long isCustomerInAsc;

	/**
	 * 是否质检
	 */
	private Long isSeasonCheck;

	/**
	 * 进厂油料
	 */
	private Long oilRemain;

	/**
	 * 是否洗车
	 */
	private Long isWash;

	/**
	 * 是否第一次保养
	 */
	private Long is1stGuarantee;

	/**
	 * 是否三日电访
	 */
	private Integer isTrace;

	/**
	 * 三日电访时间
	 */
	private Integer traceTime;

	/**
	 * 不回访原因
	 */
	private String noTraceReason;

	/**
	 * 是否路试
	 */
	private Long needRoadTest;

	/**
	 * 推荐单位
	 */
	private String recommendEmpName;

	/**
	 * 推荐人
	 */
	private String recommendCustomerName;

	/**
	 * 服务专员
	 */
	private String serviceAdvisor;

	/**
	 * 开单人员
	 */
	private String serviceAdvisorAss;

	/**
	 * 工单状态
	 */
	private Long roStatus;

	/**
	 * 工单开单时间
	 */
	private String roCreateDate;

	/**
	 * 预交车时间
	 */
	private String endTimeSupposed;

	/**
	 * 指定技师
	 */
	private String chiefTechnician;

	/**
	 * 车主编号
	 */
	private String ownerNo;

	/**
	 * 车主
	 */
	private String ownerName;

	/**
	 * 车主性质
	 */
	private Integer ownerProperty;

	/**
	 * 车牌号
	 */
	private String license;

	/**
	 * 车辆唯一识别代码
	 */
	private String vin;

	/**
	 * 发动机号
	 */
	private String engineNo;

	/**
	 * 品牌
	 */
	private String brand;

	/**
	 * 车系
	 */
	private String series;

	/**
	 * 车型
	 */
	private String model;

	/**
	 * 进厂行驶里程
	 */
	private Double inMileage;

	/**
	 * 出厂行驶里程
	 */
	private Double outMileage;

	/**
	 * 是否换表
	 */
	private Integer isChangeOdograph;

	/**
	 * 是否离店
	 */
	private Integer isCheckOut;

	/**
	 * 换表里程
	 */
	private Double changeMileage;

	/**
	 * 累计换表里程
	 */
	private Double totalChangeMileage;

	/**
	 * 行驶总里程
	 */
	private Double totalMileage;

	/**
	 * 送修人
	 */
	private String deliverer;

	/**
	 * 送修人性别
	 */
	private Integer delivererGender;

	/**
	 * 送修人电话
	 */
	private String delivererPhone;

	/**
	 * 送修人手机
	 */
	private String delivererMobile;

	/**
	 * 完工验收人
	 */
	private String finishUser;

	/**
	 * 是否竣工
	 */
	private Long completeTag;

	/**
	 * QB状态
	 */
	private Integer qbStarte;

	/**
	 * 待信标志
	 */
	private Long waitInfoTag;

	/**
	 * 待料标志
	 */
	private Long waitPartTag;

	/**
	 * 竣工开始时间
	 */
	private String completeTimeBegin;
	/**
	 * 竣工时间
	 */
	private String completeTime;

	/**
	 * 提交结算时间
	 */
	private String forBalanceTime;


	private Long deliveryTag;

	/**
	 * 交车日期
	 */
	private String deliveryDate;

	/**
	 * 交车人
	 */
	private String deliveryUser;

	/**
	 * 工时单价
	 */
	private Double labourPrice;

	/**
	 * 工时费
	 */
	private Double labourAmount;

	/**
	 * 维修材料费
	 */
	private Double repairPartAmount;

	/**
	 * 销售材料费
	 */
	private Double salesPartAmount;

	/**
	 * 附加项目费
	 */
	private Double addItemAmount;

	/**
	 * 辅料管理费
	 */
	private Double overItemAmount;

	/**
	 * 维修金额
	 */
	private Double repairAmount;

	/**
	 * 预估金额
	 */
	private Double estimateAmount;

	/**
	 * 结算金额
	 */
	private Double balanceAmount;

	/**
	 * 收款金额
	 */
	private Double receiveAmount;

	/**
	 * 去零金额
	 */
	private Double subObbAmount;

	/**
	 * 减免金额
	 */
	private Double derateAmount;

	/**
	 * 首次预估金额
	 */
	private Double firstEstimateAmount;

	/**
	 * 跟踪标识
	 */
	private Long traceTag;

	/**
	 * 试车员
	 */
	private String testDriver;
	/**
	 * 是否是透明车间操作默认为10041002
	 */
	private Integer isWorkshop;
	/**
	 * 工单打印时间
	 */
	private String printRoTime;

	/**
	 * 首次打印时间
	 */
	private String firstPrintTime;

	/**
	 * 工单付费类型
	 */
	private Integer roChargeType;

	/**
	 * 预先捡料单打印时间
	 */
	private String printRpTime;

	/**
	 * 预计开工时间
	 */
	private String estimateBeginTime;

	/**
	 * 是否参加活动
	 */
	private Long isActivity;

	/**
	 * 是否仓库关单
	 */
	private Long isCloseRo;

	/**
	 * 是否保养
	 */
	private Long isMaintain;

	/**
	 * 顾客描述
	 */
	private String customerDesc;

	/**
	 * 工单拆分状态
	 */
	private Long roSplitStatus;

	/**
	 * 进厂原因
	 */
	private Long inReason;

	/**
	 * 工单不进车间
	 */
	private Long notEnterWorkshop;

	/**
	 * 是否带走旧件
	 */
	private Long isTakePartOld;

	/**
	 * 维修故障描述
	 */
	private String roTroubleDesc;

	/**
	 * 服务活动跟踪标识
	 */
	private Long activityTraceTag;

	/**
	 * 是否赠送保养
	 */
	private Long isLargessMaintain;

	/**
	 * 是否修改预交车时间
	 */
	private Long isUpDateEndTimeSupposed;

	/**
	 * 出险单号
	 */
	private String occurInsuranceNo;

	/**
	 * 定损与实际赔付差额
	 */
	private Double dsFactBalance;

	/**
	 * 定损金额
	 */
	private Double dsAmount;

	/**
	 * 收案日期
	 */
	private String caseClosedDate;

	/**
	 * 旧件处理方式
	 */
	private Long oldpartTreatMode;

	/**
	 * 旧件处理时间
	 */
	private String oldpartTreatDate;

	/**
	 * 旧件备注
	 */
	private String oldpartRemark;

	/**
	 * 是否旧件
	 */
	private Long isOldPart;

	/**
	 * 理赔备注
	 */
	private String settlementRemark;

	/**
	 * 赔付收款日期
	 */
	private String settleColDate;

	/**
	 * 赔付收款状态
	 */
	private Integer settlementStatus;

	/**
	 * 赔付金额
	 */
	private Double settlementAmount;

	/**
	 * 送单人
	 */
	private String deliverBillMan;

	/**
	 * 送单理赔日期
	 */
	private String deliverBillDate;

	/**
	 * 出险完成
	 */
	private Long insuranceOver;

	/**
	 * 发料单首次打印时间
	 */
	private String printSendTime;



	private String receptionNo;

	/**
	 * 工位代码
	 */
	private String labourPositionCode;

	/**
	 * 故障发生日期
	 */
	private String troubleOccurDate;

	/**
	 * 索赔单提报状态
	 */
	private Long roClaimStatus;

	/**
	 * 是否购买维护
	 */
	private Long isPurchaseMaintenance;

	/**
	 * 配置代码
	 */
	private String configCode;

	/**
	 * 车间反馈结果
	 */
	private String eworkshopRemark;

	/**
	 * 结算是否打印
	 */
	private Integer isPrint;

	/**
	 * 打印交车服务单时间
	 */
	private String printCarTime;

	/**
	 * 上次保养日期
	 */
	private String lastMaintenanceDate;

	/**
	 * 上次保养里程
	 */
	private Double lastMaintenanceMileage;

	/**
	 * 是否放弃活动
	 */
	private Long isAbandonActivity;

	/**
	 * 三包状态
	 */
	private Long schemeStatus;

	/**
	 * 是否APP
	 */
	private Integer isApp;

	/**
	 * 警告
	 */
	private Integer isTgWarningsExist;

	/**
	 * 是否索赔工单
	 */
	private Integer isClaimOrder;

	/**
	 * 是否由估价单转工单
	 */
	private Long isEotoro;

	/**
	 * 索赔号
	 */
	private String claimNo;

	/**
	 * 联系人邮箱
	 */
	private String delivererEmail;

	/**
	 * 索赔项目金额
	 */
	private Double claimLabourAmount;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 备注1
	 */
	private String remark1;

	/**
	 * 备注2
	 */
	private String remark2;

	/**
	 * 锁定人
	 */
	private String lockUser;

	/**
	 * 是否首次保养
	 */
	private Integer isFirstMaintain;

	/**
	 * 洗车类型
	 */
	private Integer washType;

	/**
	 * 维修类型代码
	 */
	private String repairCategoryCode;

	/**
	 * 技术经理处理结果
	 */
	private String technicalHandelResult;

	/**
	 * 是否售前维修
	 */
	private Integer ifPreSaleRepair;

	/**
	 * 供应商代码
	 */
	private String providerCode;

	/**
	 * 税率
	 */
	private Double tax;

	/**
	 * 维修开始时间
	 */
	private String repairBeginDate;

	/**
	 * 废弃标识
	 */
	private Integer discardData;

	/**
	 * 废弃说明
	 */
	private String discardReason;

	/**
	 * 原预交车时间
	 */
	private String oridinalTimeSupposed;

	/**
	 * FD上传
	 */
	private Integer fdStatus;

	/**
	 * 电子首保卡号
	 */
	private String efwCard;

	/**
	 * FD日期
	 */
	private String fdDate;

	/**
	 * Pad录音url
	 */
	private String mp3File;

	/**
	 * 处理结果
	 */
	private String dealResult;

	/**
	 * 工时定额(故障标准)代码
	 */
	private String manHourQuotaCode;


	private String manHourQuotaName;

	/**
	 * 手机
	 */
	private String mobile;



	/**
	 * 是否删除
	 */
	private String isDeleted;

	/**
	 * 接车日期
	 */
	private String pickupDate;

	/**
	 * 是否快修
	 */
	private Integer isQuickRepair;

	/**
	 * 是否上门服务
	 */
	private Integer isHomeDelivery;

	/**
	 * 工单暂停总时长
	 */
	private String roPauseTotalTime;

	/**
	 * 是否终检
	 */
	private Integer isCheck;

	/**
	 * 创建来源
	 */
	private Integer createSource;



	/**
	 * '是否确认工单'
	 */
	private String submitOrderStatus;

	/**
	 * 工单确认时间
	 */
	private String submitOrderTime;

	/**
	 * 是否确认工单
	 */
	private Integer isSubmitOrder;

	/**
	 * '结算申请状态'
	 */
	private String applicationStatus;
	/**
	 * '会员码'
	 */
	private String vipCode;
	/**
	 * '订单号'
	 */
	private String theOrderNumber;
	/**
	 * '希望联系方式'
	 */
	private String hopeContact;

	/**
	 * 可使用卡券oneid
	 */
	private Long oneId;

	/**
	 * 车主oneid
	 */
	private Long ownerOneId;


	/**
	 * 是否备货
	 */
	private String qualityRemark;
	/**
	 * 是否备货
	 */
	private Integer isStockUp;

	private String stockUpTime;

	private String electricalMachineryOneNo;

	private String electricalMachineryTwoNo;

	private String completeAdviceWay;

	private String roadAssistanceNo;

	private String parkingPosition;

	private String batteryMileage;

	private String customerType;

	private String customerTypeDesc;

	private String yjNo;

	private String endPreckeckEndTime;

	private String endPreckeckRecord;

	/**
	 * 车辆购买价格
	 */
	private Double vehicleBuyAmount;

	/**
	 * 延保年限
	 */
	private String numBerofYears;

	/**
	 * 是否延保
	 */
	private Integer isExtendInsurance;

	/**
	 * 采购单上传时间
	 */
	private String extendInsuranceNoTime;

	/**
	 * 延保采购单号
	 */
	private String extendInsuranceNo;

	/**
	 * 产品号
	 */
	private String extendProductNo;

	/**
	 * 产品号
	private String policyNo;

	/**
	 * qb不执行原因
	 */
	private String qbNoReason;

	/**
	 * 所有qb信息
	 */
	private String allQbInfo;


	/**
	 * 是否自检登记
	 */
	private String isSelfCheckRegister;

	/**
	 * 完工验收人Name
	 */
	private String finishUserName;

	/**
	 * 是否评价
	 */
	private Integer	isEvaluate;

	private String	registNo;

	private String	outClaimNumber;

	/**
	 * 是否服务合同购买工单
	 */
	private Integer isServeContractBuyOrder;

	/**
	 * 服务合同ID
	 */
	private Integer ServeContractId;

	/**
	 * 是否保养活动工单
	 */
	private Integer isUpkeepActivityOrder;

	/**
	 * 配置ID
	 */
	private String apackageId;

	/**
	 * 车间车辆首次进场时间
	 */
	private String firstInShopTime;

	/**
	 * 进厂时间
	 */
	private String entryTime;


	/**
	 * 进厂时间类型  1:摄像头  2:人工录入
	 */
	private String entryTimeType;

	/**
	 * 进入工位时间
	 */
	private String befinWorkshopTime;

	/**
	 * 离开工位时间
	 */
	private String endWorkshopTime;

	/**
	 * 离店时间
	 */
	private String departureShopTime;


	/**
	 * 离店时间类型 1:摄像头  2:人工录入
	 */
	private String departureShopTimeType;

	/**
	 * 燃料类型
	 * */
	private String fuelType;

	private String effectStartDate;
	private String effectEndDate;

	private String orderId;


	private String createdMqAt;

	//最近新增工时时间
	private String createdRoLabourDateMax;

	//透明车间预计交车
	private String predictDate;

	//透明车间 群聊id
	private String groupId;

	//透明车间 是否派工
	private String deliveryStatus;

}
