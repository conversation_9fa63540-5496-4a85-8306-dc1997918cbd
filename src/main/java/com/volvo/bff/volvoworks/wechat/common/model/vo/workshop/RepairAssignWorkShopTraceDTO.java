package com.volvo.bff.volvoworks.wechat.common.model.vo.workshop;


import java.io.Serializable;
import java.util.List;

/**
 * 派工上下工位留痕
 * <AUTHOR>
 * @since 2022-11-03
 */

public class RepairAssignWorkShopTraceDTO  implements Serializable {

    /**
     * 经销商编码
     */
    private String ownerCode;

    /**
     * 派工ID
     */
    private List<Integer> assignIds;

    /**
     * 车牌号
     */
    private String license;

    /**
     * 打卡类型。0：开工打卡 1：完工打卡
     */
    private String workshopType;

    /**
     * 上工位留痕类型。85001001：未进入工位 85001002：工位未安装摄像头 85001003：车辆已在工位
     */
    private String befinWorkshopType;

    /**
     * 下工位留痕类型。85001004：车辆还在工位 85001005：车辆未进工位维修 85001006：车辆已离开工位 85001007：工位未安装摄像头
     */
    private String endWorkshopType;

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public List<Integer> getAssignIds() {
        return assignIds;
    }

    public void setAssignIds(List<Integer> assignIds) {
        this.assignIds = assignIds;
    }

    public String getLicense() {
        return license;
    }

    public void setLicense(String license) {
        this.license = license;
    }

    public String getWorkshopType() {
        return workshopType;
    }

    public void setWorkshopType(String workshopType) {
        this.workshopType = workshopType;
    }

    public String getBefinWorkshopType() {
        return befinWorkshopType;
    }

    public void setBefinWorkshopType(String befinWorkshopType) {
        this.befinWorkshopType = befinWorkshopType;
    }

    public String getEndWorkshopType() {
        return endWorkshopType;
    }

    public void setEndWorkshopType(String endWorkshopType) {
        this.endWorkshopType = endWorkshopType;
    }

    @Override
    public String toString() {
        return "RepairAssignWorkShopTraceDTO{" +
                "ownerCode='" + ownerCode + '\'' +
                ", assignId='" + assignIds + '\'' +
                ", license='" + license + '\'' +
                ", workshopType='" + workshopType + '\'' +
                ", befinWorkshopType='" + befinWorkshopType + '\'' +
                ", endWorkshopType='" + endWorkshopType + '\'' +
                '}';
    }
}
