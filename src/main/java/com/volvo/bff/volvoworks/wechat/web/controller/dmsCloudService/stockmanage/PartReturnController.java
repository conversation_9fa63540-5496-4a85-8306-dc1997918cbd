package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.stockmanage;

import com.volvo.bff.volvoworks.wechat.common.model.dto.basedata.ListPartReturnDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.stockmanage.PartReturnService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(value = "/stockmanage/partReturn", tags = {"配件采购退货Controller"})
@RestController
@RequestMapping("/stockmanage/partReturn")
public class PartReturnController {

    @Autowired
    private PartReturnService partReturnService;
    @ApiOperation(value = "配件采购退货-主界面查询单据", notes = "配件采购退货主单查询", httpMethod = "GET")
    @GetMapping(value = "/queryPartReturn")
    @ResponseBody
    public ResponseDTO<PageDto> queryPartReturn(@RequestParam Map<String, Object> queryParam) {
        return ResponseUtils.success(partReturnService.queryPartReturn(queryParam));
    }


    /**
     * 配件采购退货-具体单据明细数据查询
     * <AUTHOR>
     * @date 2020年7月24日
     */

    @ApiOperation(value = "配件采购退货-具体单据明细数据查询", notes = "具体单据明细数据查询", httpMethod = "GET")
    @GetMapping(value = "/queryPartReturnDetail")
    @ResponseBody
    public ResponseDTO<List<Map>> queryPartReturnDetail(@RequestParam Map<String, String> queryParam) {
        return ResponseUtils.success(partReturnService.queryPartReturnDetail(queryParam));
    }


    @ApiOperation(value = "配件采购退货-作废", notes = "根据单号（放在MAP中)作废单据", httpMethod = "POST")
    @PostMapping(value = "/deletePartReturn")
    @ResponseBody
    public void deletePartReturn(@RequestBody ListPartReturnDTO listPartReturnDto) {
        partReturnService.deletePartReturn(listPartReturnDto);
    }

    @ApiOperation(value = "配件采购入库-出库", notes = "根据主界面参数（放在MAP中）配件采购退货-出库", httpMethod = "POST")
    @RequestMapping(value = "/partReturnOut", method = RequestMethod.POST)
    @ResponseBody
    public ResponseDTO<String> partReturnOut(@RequestBody ListPartReturnDTO listPartReturnDto)  {
        return  ResponseUtils.success(partReturnService.partReturnOut(listPartReturnDto));
    }
}
