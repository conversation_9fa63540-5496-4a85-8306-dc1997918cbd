package com.volvo.bff.volvoworks.wechat.common.model.dto.workshop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023/10/23
 */
@Data
@ApiModel("确认在群")
public class CheckGroupDTO {
    @ApiModelProperty("工单号")
    @NotBlank(message = "工单号不能为空！")
    private String roNo;
    @ApiModelProperty("用户ID")
    private String userId;
    @ApiModelProperty("来源，1：app，2：企微")
    private Integer source;
    
    @NotBlank(message = "群聊id不能为空！")
    private String groupId;
}
