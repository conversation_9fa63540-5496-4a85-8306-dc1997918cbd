package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.order;

import com.volvo.bff.volvoworks.wechat.common.model.dto.order.ListAdMaintainDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.order.WithDrawStuffService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;

import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Map;

@RestController
@RequestMapping("/basedata/ttRepairOrder")
@Api(tags = "配件维修领料")
public class PartRepairOrderController {

    @Autowired
    private WithDrawStuffService withDrawStuffService;

    @ApiOperation(value = "维修领料界面查询领料配件明细", notes = "根据必要条件工单号(要放在map中)查询维修领料配件明细信息", httpMethod = "GET")
    @RequestMapping(value = "/SearchMaintainPicking/queryMaintainPicking", method = RequestMethod.GET)
    public ResponseDTO<Map<String, Object>> queryMaintainPicking(@RequestParam Map<String, String> queryParam) {
        return ResponseUtils.success(withDrawStuffService.queryMaintainPicking(queryParam));
    }


    @ApiOperation(value = "配件维修领料-保存", notes = "保存", httpMethod = "POST")
    @RequestMapping(value = "/btnSave", method = RequestMethod.POST)
    public ResponseDTO<Integer>  btnSave(@RequestBody ListAdMaintainDTO listAdMaintainDTO) {
        return ResponseUtils.success(withDrawStuffService.saveMaintainPart(listAdMaintainDTO));
    }


    @ApiOperation(value = "配件维修领料-入账", notes = "入账", httpMethod = "POST")
    @RequestMapping(value = "/account", method = RequestMethod.POST)
    public ResponseDTO<String> account(@RequestBody ListAdMaintainDTO listAdMaintainDTO){
        return ResponseUtils.success(withDrawStuffService.account(listAdMaintainDTO));
    }
}
