package com.volvo.bff.volvoworks.wechat.common.service.assistant;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.QuickRepairOrderQueryResultVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.*;

public interface QuickRepairOrderService {
    /**
     * 查询所有工单
     * @param queryParamsVo 查询vo
     * @return List<QuickRepairOrderQueryResultVo>
     */
    PageBean<QuickRepairOrderQueryResultVo> findAllRepairOrderForAssistant(QuickRepairOrderQueryParamsVo queryParamsVo);

    /**
     * 查询交车状态数量
     * @param queryParamsVo 查询vo
     * @return SubmitCarStatusResultVo
     */
    SubmitCarStatusResultVo findSubmitCarStatus(QuickRepairOrderQueryParamsVo queryParamsVo);

    /**
     * 进度查询-工单明细查询
     * @param roNo
     * @return
     */
    RepairOrderProgressQueryDetailResultVo findOrderInfoByNum(String roNo);
}
