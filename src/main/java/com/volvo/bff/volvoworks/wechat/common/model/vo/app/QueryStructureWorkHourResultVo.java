package com.volvo.bff.volvoworks.wechat.common.model.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName QueryStructureWorkHourResultVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/6/3 16:31
 */
@Data
@ApiModel("结构件查询工时结果集VO")
public class QueryStructureWorkHourResultVo {

    @ApiModelProperty(value = "工时编号",name = "labourCode")
    private String labourCode;

    @ApiModelProperty(value = "工时名称",name = "labourName")
    private String labourName;

    @ApiModelProperty(value = "是否拍照",name = "isPhotograph")
    private String isPhotograph;

    @ApiModelProperty(value = "工时唯一标识",name = "itemId")
    private Integer itemId;
}
