package com.volvo.bff.volvoworks.wechat.common.model.dto.LucencyWorkshop;

import lombok.Data;

@Data
public class WorkShopRepairOrderDTO {

    //经销商code
    private String ownerCode;
    //车牌号
    private String license;
    //vin
    private String vin;
    //工单号
    private String roNo;
    //预交车时间
    private String endTimeSupposed;
    //工单类型
    private String repairTypeCode;
    //工单状态
    private String roStatus;
    //标签
    private String label;
    //送修人
    private String deliverer;
    //服务顾问
    private String serviceAdvisor;
    //车型
    private String model;
    //打卡 10041001已打卡 10041002未打卡
    private String isPunch;
    //缺货状态
    private String isStockUp;
    //是否质检
    private String completeTag;






}
