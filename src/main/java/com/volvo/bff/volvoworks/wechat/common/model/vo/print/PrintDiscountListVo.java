package com.volvo.bff.volvoworks.wechat.common.model.vo.print;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("结算单打印工单明细列表Vo")
@Data
public class PrintDiscountListVo {
    @ApiModelProperty(value = "优惠类型", name = "preferentialType")
    private String preferentialType;
    @ApiModelProperty(value = "优惠内容", name = "preferentialContent")
    private String preferentialContent;
    @ApiModelProperty(value = "优惠金额", name = "preferentialAmount")
    private Double preferentialAmount;
    @ApiModelProperty(value = "卡券来源", name = "couponOrigin")
    private String couponOrigin;
    @ApiModelProperty(value = "优惠类型code", name = "preferentialTypeCode")
    private String preferentialTypeCode;
    @ApiModelProperty(value = "优惠iD", name = "preferentialId")
    private String preferentialId;
}
