package com.volvo.bff.volvoworks.wechat.common.model.dto.workshop;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PartsStockDetailDto {

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 车牌号
     */
    private String license;

    private String modelCode;

    private String modelName;

    private String partName;

    /**
     * 缺件数量
     */
    private BigDecimal shortQuantity;

    /**
     * 预计到货时间
     */
    private String expectedDeliveryTime;

    /**
     * 预计交车时间
     */
    private String endTimeSupposed;

    /**
     * 详情页面 已到货 未到货标签名称
     */
    private String missingPartsStatusTag;
}
