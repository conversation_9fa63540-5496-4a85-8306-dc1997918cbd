package com.volvo.bff.volvoworks.wechat.common.model.dto.vhc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description VHC车辆健康检查大类信息
 * @Date 2024/9/26 17:13
 */
@Data
@ApiModel("VHC车辆健康检查大类信息")
public class VhcClassInfoDto {

    @ApiModelProperty("大类id")
    private Integer id;

    @ApiModelProperty("经销商")
    private String ownerCode;

    @ApiModelProperty("车辆健康检查编号")
    private String vhcNo;

    @ApiModelProperty("状态（红绿黄灯）")
    private String classState;

    @ApiModelProperty("大类名称")
    private String className;

    @ApiModelProperty("配置表大类id")
    private String configClassId;

    @ApiModelProperty("检查项目(车辆举升,非举升)")
    private String classProject;

    @ApiModelProperty("小类集合")
    List<VhcItemInfoDto> vhcItemInfoDtoList;
}
