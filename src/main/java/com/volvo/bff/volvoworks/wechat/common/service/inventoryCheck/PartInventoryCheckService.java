package com.volvo.bff.volvoworks.wechat.common.service.inventoryCheck;

import com.volvo.bff.volvoworks.wechat.common.model.dto.stockmanage.InventoryItemDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.stockmanage.PartInventoryDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.stockmanage.PartInventorySourceCodeDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;


import java.util.Map;


public interface PartInventoryCheckService {

    PageInfoDto findAllInventoryInfo(Map<String, String> map);

    Map<String, Object> findAllInventoryItemInfoById(String id, String num, Map<String, String> queryParam);

    PartInventoryDTO btnConfirm(InventoryItemDTO dto);

    String saveInventoryInfo(InventoryItemDTO dto);

    Map<String, Object> savePartInventorySourceCode(PartInventorySourceCodeDTO dto);

}
