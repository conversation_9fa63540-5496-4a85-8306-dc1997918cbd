package com.volvo.bff.volvoworks.wechat.common.model.dto.basedata;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DataImportDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@EqualsAndHashCode(callSuper = false)
@Data
public class PartReturnItemDTO extends DataImportDto {

    private String stockReturnNo;	//退货单号
    private String storageCode;	//仓库名称
    private String storagePositionCode;	//库位
    private String partNo;	//零件代码
    private String partName;	//零件名称
    private String unitCode;	//单位
    private BigDecimal returnPrice;	//进货价(退货价)
    private BigDecimal returnQuantity;	//退货数量
    private BigDecimal returnAmount;	//退货金额
    private String itemId;  //明细ITEM_ID
    private String flag;    //前台记录变更标志N:新增/U:更新
    private String returnCause;    //app未扫码原因
    private Long partBuyItemId;  //验收单ID
    private BigDecimal applyQuantity; // 申请数量
    //APP传来的溯源码
    private List<String> sourceCode;

}
