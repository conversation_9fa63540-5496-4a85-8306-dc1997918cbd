package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.order;


import com.volvo.bff.volvoworks.wechat.common.model.dto.order.OrderConfirmVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.order.OrderConfirmService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@Api(tags = "电子工单")
@RestController
@RequestMapping("/orderConfirm")
public class OrderConfirmController {

    @Autowired
    private OrderConfirmService orderConfirmService;


    /**
     * 判断结算单是否已确认
     * @param confirm
     * @return
     */

    @ApiOperation(value = "判断工单结算是否已确认 ", notes = "判断工单结算是否已确认", httpMethod = "POST")
    @PostMapping(value = "/getorderStatementConfirm")
    public ResponseDTO<String> getorderStatementConfirm(@RequestBody OrderConfirmVo  confirm){
        return ResponseUtils.success(orderConfirmService.orderIsOrNotConfirm(confirm));
    }



    @ApiOperation(value = "工单结算确认 ", notes = "工单结算确认", httpMethod = "POST")
    @PostMapping(value = "/orderStatementConfirm")
    public void orderStatementConfirm(@RequestBody OrderConfirmVo confirm){
        orderConfirmService.orderStatementConfirm(confirm);
    }

}
