package com.volvo.bff.volvoworks.wechat.common.service.workshop;


import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.*;

import java.util.List;

public interface Em90DeliveryService {

    boolean selectEm90DeliveryFrame(String ownerCode,String roNo,String vin);

    Void submitReportReason(ReportReasonDto reportReasonDto);


    Void em90TakeDeliverPrecheckEmail(VehicleEntranceVo vehicleEntranceVO);
}
