package com.volvo.bff.volvoworks.wechat.common.model.vo.assistant;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 快速工单查询结果vo
 * <AUTHOR>
 * @date 2020-03-13
 */
@Data
@ApiModel("快速工单查询结果vo")
public class QuickRepairOrderQueryResultVo {
    @ApiModelProperty(value = "工单状态",name = "roStatus")
    private Integer roStatus;
    @ApiModelProperty(value = "车主名称",name = "ownerName")
    private String ownerName;
    @ApiModelProperty(value = "车主编号",name = "ownerNo")
    private String ownerNo;
    @ApiModelProperty(value = "工单号",name = "roNo")
    private String roNo;
    @ApiModelProperty(value = "工单创建时间",name = "roCreateDate")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date roCreateDate;
    @ApiModelProperty(value = "车牌号",name = "license")
    private String license;
    @ApiModelProperty(value = "服务顾问",name = "serviceAdvisor")
    private String serviceAdvisor;
    @ApiModelProperty(value = "指定技师",name = "chiefTechnician")
    private String chiefTechnician;










}
