package com.volvo.bff.volvoworks.wechat.common.model.vo.assistant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("增修结果VO")
public class AdditionalTrainingResultVo {

    @ApiModelProperty(value = "牌照",name = "license")
    private String license;

    @ApiModelProperty(value = "预检单号",name = "yjNo")
    private String yjNo;

    @ApiModelProperty(value = "工单号",name = "roNo")
    private String roNo;

    @ApiModelProperty(value = "服务顾问",name = "serviceAdvisor")
    private String serviceAdvisor;

    @ApiModelProperty(value = "创建人",name = "createdBy")
    private String createdBy;

    @ApiModelProperty(value="内饰检查",name = "bodyAppearanceInfoList")
    private List<BodyAppearanceInfo> bodyAppearanceInfoList;

    @ApiModelProperty(value="发动机舱",name = "bodyAppearanceInfoList2")
    private List<BodyAppearanceInfo> bodyAppearanceInfoList2;

    @ApiModelProperty(value="底盘四轮",name = "bodyAppearanceInfoList3")
    private List<BodyAppearanceInfo> bodyAppearanceInfoList3;
}
