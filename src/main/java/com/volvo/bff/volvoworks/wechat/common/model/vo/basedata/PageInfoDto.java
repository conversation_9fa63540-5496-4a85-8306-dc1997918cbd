package com.volvo.bff.volvoworks.wechat.common.model.vo.basedata;

import java.util.List;
import java.util.Map;

public class PageInfoDto {
    private Long total;
    private Integer pageSize;
    private Integer pageNum;
    private List<Map> rows;

    public PageInfoDto() {
    }

    public Long getTotal() {
        return this.total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public List<Map> getRows() {
        return this.rows;
    }

    public void setRows(List<Map> rows) {
        this.rows = rows;
    }

    public Integer getPageSize() {
        return this.pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNum() {
        return this.pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }
}