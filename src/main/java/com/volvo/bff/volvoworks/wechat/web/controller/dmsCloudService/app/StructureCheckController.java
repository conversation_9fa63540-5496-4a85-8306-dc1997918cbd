package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.app;


import com.alibaba.fastjson.JSONObject;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.app.impl.StructureCheckServiceImpl;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName StructureCheckController
 * @Description 结构件检查
 * <AUTHOR>
 * @Date 2020/6/2 10:27
 */
@Api(value = "/structureCheckApi", tags = {"结构件检查"})
@RestController
@RequestMapping("/structureCheckApi")
public class StructureCheckController {

    @Autowired
    private StructureCheckServiceImpl structureCheckService;


    @ApiOperation(value = "查询在修工单根据条件", notes = "查询在修工单根据条件", httpMethod = "POST")
    @PostMapping(value = "/queryStructureCheckByCondition")
    public ResponseDTO<PageBean<QueryStructureCheckResultVo>> queryStructureCheckByCondition(@RequestBody QueryStructureCheckRrquestVo queryStructureCheckRrquestVo){
        return ResponseUtils.success(structureCheckService.queryStructureCheckByCondition(queryStructureCheckRrquestVo));
    }


    @ApiOperation(value = "根据工单号和车牌号查询维修工时", notes = "根据工单号和车牌号查询维修工时", httpMethod = "GET")
    @GetMapping(value = "/queryStructureCheckWorkHour")
    public ResponseDTO<PageBean<QueryStructureWorkHourResultVo>> queryStructureCheckWorkHourByLienceAndRoNO(@RequestParam("license") String license, @RequestParam("roNo") String roNo){
        return ResponseUtils.success(structureCheckService.queryStructureCheckWorkHourByLienceAndRoNO(license,roNo));
    }


    @ApiOperation(value = "根据工单号和车牌号和维修工时代码查询单个维修工时", notes = "根据工单号和车牌号和维修工时代码查询单个维修工时", httpMethod = "GET")
    @GetMapping(value = "/queryOneStructureCheckWorkHour")
    public ResponseDTO<JSONObject> queryOneStructureCheckWorkHour(@RequestParam("itemId") Integer itemId, @RequestParam("roNo") String roNo, @RequestParam("whCode") String whCode){
        return ResponseUtils.success(structureCheckService.queryOneStructureCheckWorkHour(itemId,roNo,whCode));
    }


    @ApiOperation(value = "保存或更新结构件维修工时", notes = "保存或更新结构件维修工时", httpMethod = "POST")
    @PostMapping(value = "/saveOrUpdateStructureOneWorkHour")
    public ResponseDTO<String> saveOrUpdateStructureOneWorkHour(@RequestBody StructureWorkHourOneRequestVo structureWorkHourOneRequestVo){
        return ResponseUtils.success(structureCheckService.saveOrUpdateStructureOneWorkHour(structureWorkHourOneRequestVo));
    }
}
