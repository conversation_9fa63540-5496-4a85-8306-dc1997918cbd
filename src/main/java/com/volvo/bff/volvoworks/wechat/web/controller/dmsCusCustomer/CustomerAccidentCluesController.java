package com.volvo.bff.volvoworks.wechat.web.controller.dmsCusCustomer;

import com.volvo.bff.volvoworks.wechat.common.model.dto.accidentClues.AccidentCluesDTO;
import com.volvo.bff.volvoworks.wechat.common.model.dto.login.CurrentLoginInfoDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.accidentClues.AccidentClueVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.accidentClues.AccidentCluesService;
import com.volvo.bff.volvoworks.wechat.common.utils.LoginInfoUtil;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import com.volvo.bff.volvoworks.wechat.common.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
@Slf4j
@Api(value = "/accidentClues", tags = {"事故线索"})
@RestController
@RequestMapping("/accidentClues")
public class CustomerAccidentCluesController {

    @Resource
    AccidentCluesService accidentCluesService;

    @ApiOperation(value = "根据短信得到事故线索信息", notes = "", httpMethod = "POST")
    @PostMapping("/getInfoByPic")
    public ResponseDTO<AccidentClueVo> getInfoByPic(@RequestBody AccidentCluesDTO dto){
        if(StringUtils.isNullOrEmpty(dto.getParam())){
            return ResponseUtils.success(new AccidentClueVo());
        }
        return  ResponseUtils.success(accidentCluesService.getInfoByPic(dto));
    }

    @ApiOperation(value = "根据短信得到事故线索信息", notes = "", httpMethod = "POST")
    @PostMapping("/getInfoByPicnew")
    public ResponseDTO<CurrentLoginInfoDto> getInfoByPicnew(){
        CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
        log.info("currentLoginInfo:{}",currentLoginInfo);
        return  ResponseUtils.success(currentLoginInfo);
    }

}
