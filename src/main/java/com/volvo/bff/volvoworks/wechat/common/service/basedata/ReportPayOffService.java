package com.volvo.bff.volvoworks.wechat.common.service.basedata;

import com.volvo.bff.volvoworks.wechat.common.model.dto.basedata.ReportPayOffDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.Map;

public interface ReportPayOffService {

    PageInfoDto findAllList(Map<String, String> queryParam);

    List<Map> findItemByPartProfit(String id);

    ResponseEntity<ReportPayOffDTO> btnAccount(ReportPayOffDTO dto);
}
