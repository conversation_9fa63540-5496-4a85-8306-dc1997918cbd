package com.volvo.bff.volvoworks.wechat.common.service.print.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.print.PrintDataVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.print.PrintParamVo;
import com.volvo.bff.volvoworks.wechat.common.service.print.PrintBalanceRoService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class PrintBalanceRoServiceImpl implements PrintBalanceRoService {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Override
    public PrintDataVo balancePrintData(PrintParamVo paramsVo){
        DmsResultDTO<PrintDataVo> response = dmscloudServiceFeign.balancePrintData(paramsVo);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }
}
