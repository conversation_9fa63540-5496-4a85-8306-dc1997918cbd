package com.volvo.bff.volvoworks.wechat.common.model.vo.midEndInterface;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

public class SeriesVO extends MidEndBaseVO{
    private String companyCode;
    private String dataSources;
    private Integer brandId;
    private String introduction;
    private BigDecimal maxPrice;
    private BigDecimal minPrice;
    private Integer id;
    private Integer seriesId;
    private String seriesCode;
    private String seriesName;
    private String seriesNameEn;
    private Integer SERIES_ID;
    private String SERIES_NAME;
    private String seriesPictureUrl;
    private Integer isValid;
    private String modelYear;

    private String brandCode;

    public SeriesVO() {
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getDataSources() {
        return dataSources;
    }

    public void setDataSources(String dataSources) {
        this.dataSources = dataSources;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public String getModelYear() {
        return modelYear;
    }

    public void setModelYear(String modelYear) {
        this.modelYear = modelYear;
    }

    public String getBrandCode() {
        return brandCode;
    }

    public void setBrandCode(String brandCode) {
        this.brandCode = brandCode;
    }

    public BigDecimal getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(BigDecimal maxPrice) {
        this.maxPrice = maxPrice;
    }

    public BigDecimal getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(BigDecimal minPrice) {
        this.minPrice = minPrice;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.SERIES_ID=id;
        this.seriesId=id;
        this.id = id;
    }

    public String getSeriesCode() {
        return seriesCode;
    }

    public void setSeriesCode(String seriesCode) {
        this.seriesCode = seriesCode;
    }
    public String getSeriesName() {
        return seriesName;
    }

    public void setSeriesName(String seriesName) {
        this.SERIES_NAME=seriesName;
        this.seriesName = seriesName;
    }

    public String getSeriesNameEn() {
        return seriesNameEn;
    }

    public void setSeriesNameEn(String seriesNameEn) {
        this.seriesNameEn = seriesNameEn;
    }

    public String getSeriesPictureUrl() {
        return seriesPictureUrl;
    }

    public void setSeriesPictureUrl(String seriesPictureUrl) {
        this.seriesPictureUrl = seriesPictureUrl;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Integer getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(Integer seriesId) {
        this.seriesId = seriesId;
    }
    @JsonProperty(value = "SERIES_ID")
    public Integer getSERIES_ID() {
        return SERIES_ID;
    }

    public void setSERIES_ID(Integer SERIES_ID) {
        this.SERIES_ID = SERIES_ID;
    }
    @JsonProperty(value = "SERIES_NAME")
    public String getSERIES_NAME() {
        return SERIES_NAME;
    }

    public void setSERIES_NAME(String SERIES_NAME) {
        this.SERIES_NAME = SERIES_NAME;
    }

    public SeriesVO(Integer brandId) {
        this.brandId = brandId;
    }

    @Override
    public String toString() {
        return "SeriesVO{" +
                "companyCode='" + companyCode + '\'' +
                ", dataSources='" + dataSources + '\'' +
                ", brandId=" + brandId +
                ", introduction='" + introduction + '\'' +
                ", maxPrice=" + maxPrice +
                ", minPrice=" + minPrice +
                ", id=" + id +
                ", seriesId=" + seriesId +
                ", seriesCode='" + seriesCode + '\'' +
                ", seriesName='" + seriesName + '\'' +
                ", seriesNameEn='" + seriesNameEn + '\'' +
                ", SERIES_ID=" + SERIES_ID +
                ", SERIES_NAME='" + SERIES_NAME + '\'' +
                ", seriesPictureUrl='" + seriesPictureUrl + '\'' +
                ", isValid=" + isValid +
                ", modelYear='" + modelYear + '\'' +
                ", brandCode='" + brandCode + '\'' +
                '}';
    }
}
