package com.volvo.bff.volvoworks.wechat.common.model.vo.bookingregister;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 维修工时Vo
 * <AUTHOR>
 * @since 2020-04-07
 */
@Data
@ApiModel("维修工时Vo")
public class RepairWorkHourParamsVo {

    @ApiModelProperty(name = "rowKey",value = "rowKey")
    private String rowKey;

    @ApiModelProperty(name = "roNo",value = "roNo")
    private String roNo;

    @ApiModelProperty(name = "工时itemId",value = "itemId")
    private Integer itemId;

    @ApiModelProperty(name = "工时主档ID",value = "roLabourId")
    private Integer roLabourId;

    @ApiModelProperty(name = "付款部门Id",value = "pqymentDepartmentId")
    private Integer pqymentDepartmentId;

    @ApiModelProperty(name = "TYPEID",value = "typeId")
    private String typeId;

    @ApiModelProperty(name = "工时代码",value = "labourCode")
    private String labourCode;

    @ApiModelProperty(name = "工时名称",value = "labourName")
    private String labourName;

    @ApiModelProperty(name = "关联交修项目",value = "itemIdHandProject")
    private Long jobNo;

    @ApiModelProperty(name = "工时维修类型",value = "repairTypeCode")
    private String repairTypeCode;

    @ApiModelProperty(name = "收费区分",value = "chargePartitionCode")
    private String chargePartitionCode;

    @ApiModelProperty(name = "保养套餐代码",value = "maintainPackageCode")
    private String maintainPackageCode;

    @ApiModelProperty(name = "工时单价",value = "labourPrice")
    private Double labourPrice;

    @ApiModelProperty(name = "派工工时",value = "assignLabourHour")
    private Double assignLabourHour;

    @ApiModelProperty(value = "收费工时（赋值）",name = "stdLabourHour")
    private Double stdLabourHour;

    @ApiModelProperty(value = "收费工时（主档）",name = "defineStdLabour")
    private Double defineStdLabour;

    @ApiModelProperty(name = "工时费",value = "labourAmount")
    private Double labourAmount;

    @ApiModelProperty(name = "折扣率",value = "discount")
    private Double discount;

    @ApiModelProperty(name = "优惠金额",value = "discountAmount")
    private String discountAmount;

    @ApiModelProperty(name = "应收工时费",value = "receivableAmount")
    private String receivableAmount;

    @ApiModelProperty(name = "实收工时费",value = "receiveAmount")
    private String receiveAmount;

    @ApiModelProperty(name = "责任技师",value = "technician")
    private String technician;

    @ApiModelProperty(name = "是否派工",value = "assignTag")
    private Integer assignTag;

    @ApiModelProperty(name = "是否增项",value = "isAddition")
    private Integer isAddition;

    @ApiModelProperty(name = "是否委外",value = "consignExterior")
    private Integer consignExterior;

    @ApiModelProperty(name = "是否不修",value = "needlessRepair")
    private Integer needlessRepair;

    @ApiModelProperty(name = "不修原因",value = "reason")
    private Integer reason;

    @ApiModelProperty(name = "是否内返",value = "interReturn")
    private Integer interReturn;

    @ApiModelProperty(name = "是否预检",value = "preCheck")
    private Integer preCheck;

    @ApiModelProperty(name = "故障原因",value = "troubleCause")
    private String troubleCause;

    @ApiModelProperty(name = "行管项目代码",value = "localLabourCode")
    private String localLabourCode;

    @ApiModelProperty(name = "行管项目名称",value = "localLabourName")
    private String localLabourName;

    @ApiModelProperty(name = "收费类别代码",value = "manageSortCode")
    private String manageSortCode;

    @ApiModelProperty(name = "工种代码",value = "workerTypeCode")
    private Integer workerTypeCode;

    @ApiModelProperty(name = "估价单维修项目ID",value = "eLabourId")
    private Integer eLabourId;

    @ApiModelProperty(name = "项目明细UID",value = "rsLabourUid")
    private String rsLabourUid;

    @ApiModelProperty(name = "检查结果",value = "troubleDesc")
    private String troubleDesc;

    @ApiModelProperty(name = "备注",value = "remark")
    private String remark;

    @ApiModelProperty(name = "活动代码",value = "activityCode")
    private String activityCode;

    @ApiModelProperty(name = "组合代码",value = "packageCode")
    private String packageCode;

    @ApiModelProperty(name = "服务套餐代码",value = "servicePkgCode")
    private String servicePkgCode;

    @ApiModelProperty(name = "服务套餐代码",value = "setCode")
    private String setCode;

    @ApiModelProperty(name = "预约单号",value = "bookingOrderNo")
    private String bookingOrderNo;

    @ApiModelProperty(name = "车型组代码",value = "modelLabourCode")
    private String modelLabourCode;

    @ApiModelProperty(name = "索赔工时",value = "claimLabour")
    private Double claimLabour;

    @ApiModelProperty(name = "标准工时",value = "oemLabourHour")
    private Double oemLabourHour;

    @ApiModelProperty(name = "库存数量",value = "stockQuantity")
    private Double stockQuantity;

    @ApiModelProperty(name = "维修工具的唯一UID用于配件的关联关系",value = "uidHidden")
    private String uidHidden;
    @ApiModelProperty(name = "数据来源",value = "datasource")
    private Integer dataSources;

    @ApiModelProperty(name = "帐类",value = "accountsType")
    private String accountsType;

    @ApiModelProperty(value = "是否服务合同购买工单",name = "IsServeContractBuyOrder")
    private Integer IsServeContractBuyOrder;

    @ApiModelProperty(value = "是否保养活动工单",name = "IsUpkeepActivityOrder")
    private Integer IsUpkeepActivityOrder;

    //自己后台翻译用的字段
    @ApiModelProperty(name = "帐类名称",value = "accountsType")
    private String accountsName;

    @ApiModelProperty(name = "type",value = "type")
    private String type;

    @ApiModelProperty(name = "工序",value = "processName")
    private String processName;

    @ApiModelProperty(name = "是否拍照",value = "isPhotograph")
    private Integer isPhotograph;

    @ApiModelProperty(name = "是否需要拍照",value = "isNeedPhotograph")
    private Integer isNeedPhotograph;

    @ApiModelProperty(name = "vidaNo",value = "vidaNoes")
    private String vidaNo;

    @ApiModelProperty(name = "orderNo",value = "orderNo")
    private String orderNo;

    @ApiModelProperty(name = "vida主键",value = "vidaId")
    private String vidaId;

    @ApiModelProperty(name = "原vidaJobNo",value = "vidaJobNo")
    private String vidaJobNo;


    @ApiModelProperty(name = "用户ID",value = "createdBy")
    private Integer createdBy;

//    序号 = jobNo


    @ApiModelProperty(name = "所有者代码",value = "ownerCode")
    private String  ownerCode;

    @ApiModelProperty(name = "工时代码",value = "opCode")
    private String  opCode;

    @ApiModelProperty(name = "委外",value = "ourtWorkFee")
    private String  ourtWorkFee;

    @ApiModelProperty(name = "工时金额",value = "workFee")
    private Double  workFee;

    @ApiModelProperty(name = "数量",value = "workHour")
    private Double  workHour;

    @ApiModelProperty(name = "维修项目名称",value = "workName")
    private String  workName;

    @ApiModelProperty(name = "工时单价",value = "workPrice")
    private Double  workPrice;

    @ApiModelProperty(name = "适用维修类型",value = "jobType")
    private String jobType;

    @ApiModelProperty(name = "适用账类",value = "accountGroup")
    private String accountGroup;

    @ApiModelProperty(value = "建议维修配件ID", name = "suggestMaintainLabourId")
    private Integer suggestMaintainLabourId;

    @ApiModelProperty(name = "处理后的工时代码",value = "labourCodef")
    private String labourCodef;

    @ApiModelProperty(value = "乐观锁",name = "recordVersion")
    private String recordVersion;

    @ApiModelProperty(name = "编辑状态值",value = "isCanUpdate")
    private String isCanUpdate;

    @ApiModelProperty(name = "编辑状态值",value = "isCanUpdate")
    private String appRepairRange;

    @ApiModelProperty(name = "确认状态",value = "orderConfirmStatus")
    private String orderConfirmStatus;

}
