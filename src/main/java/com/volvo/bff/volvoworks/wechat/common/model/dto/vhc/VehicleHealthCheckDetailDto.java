package com.volvo.bff.volvoworks.wechat.common.model.dto.vhc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 车辆健康检查详情
 * @Date 2024/9/25 16:23
 */
@Data
@ApiModel("车辆健康检查详情")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VehicleHealthCheckDetailDto {

    @ApiModelProperty("检查员")
    private String technician;

    @ApiModelProperty("车辆健康检查明细")
    Map<String,List<VhcClassInfoDto>> vhcClassInfo;

}
