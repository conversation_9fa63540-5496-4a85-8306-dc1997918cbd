package com.volvo.bff.volvoworks.wechat.web.controller.bffAftersales;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.accidentClues.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.accidentClues.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.RestResultResponse;
import com.volvo.bff.volvoworks.wechat.common.service.accidentClues.AccidentCluesService;
import com.volvo.bff.volvoworks.wechat.common.utils.JSONUtil;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/api/v1/app/accidentClues")
@Api(value = "事故线索", tags = "事故线索")
@Slf4j
public class BffAccidentCluesController {

    @Resource
    AccidentCluesService accidentCluesService;

    @ApiOperation(value = "修改事故线索")
    @PostMapping(value = "/update")
    public ResponseDTO<Void> updateAccidentClues(@RequestBody AccidentCluesDTO clue) {
        return ResponseUtils.success(accidentCluesService.updateAccidentClues(clue));
    }

    @ApiOperation(value = "保存事故线索")
    @PostMapping(value = "/save")
    public ResponseDTO<Integer> saveAccidentClues(@RequestBody AccidentCluesDTO clue) {
        return ResponseUtils.success(accidentCluesService.saveAccidentClues(clue));
    }

    @PostMapping(value="/dealer/user")
    @ApiOperation(value = "查看员工")
    public ResponseDTO<List<EmpByRoleCodeVO>> getDealerUser(@RequestBody GetDealerUserDataDTO getDealerUserDTO){
        return ResponseUtils.success(accidentCluesService.getDealerUser(getDealerUserDTO));
    }

    @PostMapping(value="/dealer/userTwo")
    @ApiOperation(value = "查看员工2")
    public ResponseDTO<List<EmpByRoleCodeVO>> getDealerUserTwo(@RequestBody GetDealerUserDataDTO getDealerUserDTO){
        return ResponseUtils.success(accidentCluesService.getDealerUserTwo(getDealerUserDTO));
    }


    @ApiOperation(value = "根据id查询线索主单信息", notes = "根据id查询线索主单信息", httpMethod = "GET")
    @GetMapping(value = "/{acId}")
    public ResponseDTO<AccidentCluesDTO> selectById(@PathVariable("acId") Integer acId) {
        return ResponseUtils.success(accidentCluesService.selectById(acId));
    }


    @ApiOperation(value = "根据图片得到事故线索信息", notes = "", httpMethod = "POST")
    @PostMapping("/getInfoByPic")
    public ResponseDTO<AccidentClueInfoVO> getInfoByPic(@RequestBody AccidentCluesDTO dto){
        return ResponseUtils.success(accidentCluesService.getInfoByPicV2(dto));
    }


    @ApiOperation(value = "事故线索-预约单保存", notes = "事故线索-预约单保存", httpMethod = "POST")
    @PostMapping(value = "/saveAppointmentOrder")
    public ResponseDTO<BookingOrderReturnVo> saveAppointmentOrder(@RequestBody AccidentCluesDTO dto) {
        log.info("事故线索-预约单保存（/api/v1/app/accidentClues/saveAppointmentOrder），入参dto：【{}】", JSONUtil.objectToJson(dto));
        return ResponseUtils.success(accidentCluesService.saveAppointmentOrder(dto));
    }


    @ApiOperation(value = "查询全网经销商", notes = "", httpMethod = "POST")
    @PostMapping(value = "/getDealerList")
    public ResponseDTO<DealerVO.DealerPageInfo> getDealerList(@RequestBody DealerDTO params) {
        log.info("bff-getDealerList:{}", params);
        return ResponseUtils.success(accidentCluesService.getDealerList(params).getData());
    }


    @ApiOperation(value = "获取事故线索列表", notes = "", httpMethod = "POST")
    @PostMapping(value = "/getList")
    public ResponseDTO<PageBean<AccidentClueListVO>> getList(@RequestBody AccidentClueListVO params) {
        return ResponseUtils.success(accidentCluesService.getList(params));
    }


    @ApiOperation(value = "查询跟进历史不分页", notes = "", httpMethod = "GET")
    @GetMapping(value = "/getFollowList")
    public ResponseDTO<List<AccidentCluesFollowDTO>> getFollowList(@RequestParam("acId") Integer acId) {

        return ResponseUtils.success(accidentCluesService.getFollowList(acId));
    }

    /**
     * 线索分配
     */

    @ApiOperation(value = "线索分配", notes = "线索分配", httpMethod = "POST")
    @PostMapping(value = "/allot")
    public ResponseDTO<Integer> insertttCluesAllot(@RequestBody AccidentCluesAllotDTO dto) {
        return ResponseUtils.success(accidentCluesService.insertttCluesAllot(dto));
    }


    @ApiOperation(value = "线索跟进", notes = "线索跟进", httpMethod = "POST")
    @PostMapping(value = "/follow")
    public ResponseDTO<Integer> insertCluesFollow(@RequestBody AccidentCluesFollowDTO dto) {
        return ResponseUtils.success(accidentCluesService.insertCluesFollow(dto));
    }


    @ApiOperation(value = "呼叫登记", notes = "呼叫登记", httpMethod = "POST")
    @PostMapping(value = "/saveCluesSaNumber")
    public ResponseDTO<String> saveWorkNumber(@RequestBody AccidentCluesSaNumberDTO saCustomerNumberDTO) {
        return ResponseUtils.success(accidentCluesService.saveWorkNumber(saCustomerNumberDTO));
    }

    @ApiOperation(value = "线索需求查询工单", notes = "线索需求查询工单", httpMethod = "GET")
    @GetMapping(value = "/queryClueRepairOrder")
    @ResponseBody
    public ResponseDTO<List<ClueRepairOrderResultVO>> queryClueRepairOrder(RepairOrderHistoryParamsVO paramsVo) {
        return ResponseUtils.success(accidentCluesService.queryClueRepairOrder(paramsVo));
    }


    @ApiOperation(value = "修改线索联系人", notes = "", httpMethod = "POST")
    @PostMapping(value = "/updateContactInfo")
    public ResponseDTO<Void> updateContactInfo(@RequestBody AccidentClueContactDTO contact) {
        return ResponseUtils.success(accidentCluesService.updateContactInfo(contact));
    }


    @ApiOperation(value = " 查询白名单", notes = "查询白名单", httpMethod = "GET")
    @GetMapping(value = "/whitelist/checkWhitelist")
    @ResponseBody
    public ResponseDTO<Boolean> checkWhitelist(@RequestParam(value = "ownerCode") String ownerCode,
                                                      @RequestParam(value = "modType") Integer modType,
                                                      @RequestParam(value = "rosterType") Integer rosterType,
                                                      @RequestParam(value = "vin") String vin) {
        return ResponseUtils.success(accidentCluesService.checkWhitelist(ownerCode, modType, rosterType, vin));
    }


    @ApiOperation(value = "获取看板", notes = "", httpMethod = "POST")
    @PostMapping(value = "/getDashBoard")
    public ResponseDTO<AccidentClueDashBoardVO> getDashBoard(@RequestBody DashBoardQueryDTO dto) {

        return ResponseUtils.success(accidentCluesService.queryAccidentClueDashboard(dto));
    }


    @ApiOperation(value = "事故线索集合", notes = "", httpMethod = "POST")
    @PostMapping("list")
    public ResponseDTO<List<AccidentClueListVO>> list(@RequestBody AccidentCluesListQueryDto params){
        return ResponseUtils.success(accidentCluesService.list(params));
    }

    /**
     * 根据线索id查询SA呼出虚拟号
     */

    @ApiOperation(value = "根据线索id查询SA呼出虚拟号", notes = "根据线索id查询SA呼出虚拟号", httpMethod = "POST")
    @PostMapping(value = "/getVirtualNumber")
    public ResponseDTO<VirtualPhoneResponseDTO> selectVirtualNumberById(@RequestBody VirtualPhoneDTO dto ) {
        log.info("selectVirtualNumberById:{}", dto);
        String selectVirtualNumberById = accidentCluesService.selectVirtualNumberById(dto.getAcId());
        return ResponseUtils.success(VirtualPhoneResponseDTO.builder().virtualNumber(selectVirtualNumberById).build());
    }


    @ApiOperation(value = "事故线索跟进数量", notes = "", httpMethod = "POST")
    @PostMapping("/follow/count")
    public ResponseDTO<List<AccidentClueFollowVo>> followCount(@RequestBody AccidentClueListVO params){
        return ResponseUtils.success(accidentCluesService.followCount(params));
    }


    @ApiOperation(value = "事故线索分配经销商", notes = "", httpMethod = "POST")
    @PostMapping(value = "/allotDealer")
    public ResponseDTO<Void> allotDealer(@RequestBody List<AllotDealerDTO> params) {
        log.info("bff-allotDealer:{}", params);
        return ResponseUtils.success(accidentCluesService.allotDealer(params));
    }

    @ApiOperation("线索池列表查询线索统计数据")
    @PostMapping("/getSumAccidentInfo")
    public ResponseDTO<AccidentCluesSumInfoDTO> getSumAccidentInfo(@RequestBody AccidentCluesExportQueryDto dto){
        log.info("bff getSumAccidentInfo start");
        return ResponseUtils.success(accidentCluesService.getSumAccidentInfo(dto));
    }

    /**
     * 查询预约单明细
     * @param bookingOrderNo  预约单号
     * @return
     */

    @ApiOperation(value = "查询预约单明细", notes = "查询预约单明细", httpMethod = "GET")
    @GetMapping(value = "/queryAppointmentDetail")
    @ResponseBody
    public ResponseDTO<AppointmentDetailResultVo> queryAppointmentDetail(@RequestParam(value = "bookingOrderNo") String bookingOrderNo) {

        log.info("查询预约单明细(/api/v1/app/accidentClues/queryAppointmentDetail)，入参bookingOrderNo：{}", bookingOrderNo);
        return ResponseUtils.success(accidentCluesService.queryAppointmentDetail(bookingOrderNo));
    }


    @ApiOperation(value = "查询服务顾问", notes = "查询服务顾问", httpMethod = "POST")
    @PostMapping(value="/queryServiceAdvisors")
    public ResponseDTO<List<EmpByRoleCodeVO>> queryServiceAdvisors(@RequestBody GetDealerUserDataDTO dto) {

        log.info("查询服务顾问（/api/v1/app/accidentClues/queryServiceAdvisors）,入参dto：【{}】", null == dto ? null : JSONUtil.objectToJson(dto));

        return ResponseUtils.success(accidentCluesService.queryServiceAdvisors(dto));

    }
}
