package com.volvo.bff.volvoworks.wechat.common.model.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("增修建议保存参数Vo")
public class SaveAdditionalRepairSuggestionVo {

    @ApiModelProperty(value = "增修项目ID",name = "itemId")
    private Integer itemId;

    @ApiModelProperty(value = "工单号",name = "roNo")
    private String roNo;

    @ApiModelProperty(value = "VIDA工单号",name = "orderNo")
    private String orderNo;

    @ApiModelProperty(value = "车架号",name = "vin")
    private String vin;

    @ApiModelProperty(value = "图片URL",name = "ownerName")
    private List<String>  pictures;

    @ApiModelProperty(value = "备注",name = "license")
    private String remark;
    @ApiModelProperty(value = "FG",name = "functionGroup")
    private String functionGroup;

    public String getFunctionGroup() {
        return functionGroup;
    }

    public void setFunctionGroup(String functionGroup) {
        this.functionGroup = functionGroup;
    }

    public Integer getItemId() {
        return itemId;
    }

    public void setItemId(Integer itemId) {
        this.itemId = itemId;
    }

    public String getRoNo() {
        return roNo;
    }

    public void setRoNo(String roNo) {
        this.roNo = roNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public List<String> getPictures() {
        return pictures;
    }

    public void setPictures(List<String> pictures) {
        this.pictures = pictures;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
