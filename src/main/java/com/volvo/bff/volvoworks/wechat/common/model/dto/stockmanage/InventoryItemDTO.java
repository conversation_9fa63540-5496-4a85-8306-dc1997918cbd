package com.volvo.bff.volvoworks.wechat.common.model.dto.stockmanage;

import java.util.Date;
import java.util.List;
import java.util.Map;

@SuppressWarnings("rawtypes")
public class InventoryItemDTO {

    private String inventoryNo;

    private String handler;

    //@JsonDeserialize(using = JsonSimpleDateDeserializer.class)
    private Date inventoryDate;

    private String remark;

    private List<Map> dms_details;

    private String stockTakingDtlNo;
    
    private String inventoryStatus;
    private String inventoryType;

    private Object[] ids;

    public Object[] getIds() {
        return ids;
    }

    public void setIds(Object[] ids) {
        this.ids = ids;
    }

    public String getStockTakingDtlNo() {
        return stockTakingDtlNo;
    }

    public void setStockTakingDtlNo(String stockTakingDtlNo) {
        this.stockTakingDtlNo = stockTakingDtlNo;
    }

    public List<Map> getDms_details() {
        return dms_details;
    }

    public void setDms_details(List<Map> dms_details) {
        this.dms_details = dms_details;
    }

    public String getInventoryNo() {
        return inventoryNo;
    }

    public void setInventoryNo(String inventoryNo) {
        this.inventoryNo = inventoryNo;
    }

    public String getHandler() {
        return handler;
    }

    public void setHandler(String handler) {
        this.handler = handler;
    }

    public Date getInventoryDate() {
        return inventoryDate;
    }

    public void setInventoryDate(Date inventoryDate) {
        this.inventoryDate = inventoryDate;
    }

    public String getRemark() {
        return remark;
    }

  


	public String getInventoryStatus() {
		return inventoryStatus;
	}

	public void setInventoryStatus(String inventoryStatus) {
		this.inventoryStatus = inventoryStatus;
	}

	public String getInventoryType() {
		return inventoryType;
	}

	public void setInventoryType(String inventoryType) {
		this.inventoryType = inventoryType;
	}

	public void setRemark(String remark) {
        this.remark = remark;
    }

}

