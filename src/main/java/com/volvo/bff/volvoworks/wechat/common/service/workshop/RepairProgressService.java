package com.volvo.bff.volvoworks.wechat.common.service.workshop;


import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.*;

public interface RepairProgressService {

    /**
     * 维修进度看板
     * <AUTHOR>
     * @Date  created in  2020/6/1
     */
    PageBean<RepairProgressResultVo> getRepairProgress(RepairProgressParamsVo paramsVo);
}
