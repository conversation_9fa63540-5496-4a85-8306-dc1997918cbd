
package com.volvo.bff.volvoworks.wechat.common.interceptor;

import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import com.alibaba.fastjson.JSON;
import com.volvo.bff.volvoworks.wechat.common.model.dto.login.CurrentLoginInfoDto;
import com.volvo.bff.volvoworks.wechat.common.service.loginInfo.MidUserDataService;
import com.volvo.bff.volvoworks.wechat.common.utils.ApplicationContextHolder;
import com.volvo.bff.volvoworks.wechat.common.utils.TransmittableThreadLocalUtils;

public class DMSUserInterceptor extends HandlerInterceptorAdapter {

    private Logger log = LoggerFactory.getLogger(DMSUserInterceptor.class);
    @Resource
    private MidUserDataService userDataService;


    public DMSUserInterceptor() {
    }

    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse response, Object handler) throws Exception {
        log.info("into LoginFilter请求的地址: {} ,请求方法: {}", httpServletRequest.getMethod());
        this.requestPrinting(httpServletRequest);
        Object userIdObject = this.getUserIdObject(httpServletRequest);
        log.info("DMS LOGIN USERID {}", userIdObject);
        CurrentLoginInfoDto fillCurrentLoginInfoDto = this.fillCurrentLoginInfoDto(userIdObject);
        TransmittableThreadLocalUtils.setMapValue("ownerCode", fillCurrentLoginInfoDto.getOwnerCode());
        return true;
    }

    private Object getUserIdObject(HttpServletRequest httpServletRequest) {
        Object userIdObject = httpServletRequest.getHeader("userId");
        if (Objects.isNull(userIdObject)) {
            userIdObject = httpServletRequest.getHeader("Authorization");
            if (Objects.nonNull(userIdObject)) {
                userIdObject = userIdObject.toString().substring(7);
                log.info("获取登录人userIdObject" + JSON.toJSONString(userIdObject));
            }
        }
        return userIdObject;
    }
    
    private CurrentLoginInfoDto fillCurrentLoginInfoDto(Object userIdObject) {
        if (Objects.isNull(userIdObject)) {
            log.error("获取登录人ID信息失败，请确认Eureka，redis配置是否正确！！！！！！");
            return new CurrentLoginInfoDto();
        }
        if (Objects.nonNull(userIdObject)) {
            try {
                CurrentLoginInfoDto logininfovo = userDataService.getUserInfo(String.valueOf(userIdObject));
                CurrentLoginInfoDto loginInfo = (CurrentLoginInfoDto)ApplicationContextHolder.getBeanByType(CurrentLoginInfoDto.class);
                log.info("loginInfo-----------{}", loginInfo);
                if (Objects.nonNull(logininfovo) && Objects.isNull(loginInfo)) {
                    loginInfo = new CurrentLoginInfoDto();
                    BeanUtils.copyProperties(logininfovo, loginInfo);
                    log.info("loginInfo info: {}", loginInfo);
                    return loginInfo;
                } else {
                    BeanUtils.copyProperties(logininfovo, loginInfo);
                    log.info("loginInfo info: {}", loginInfo);
                    return loginInfo;
                }
            } catch (Exception var5) {
                log.info("获取登录信息失败:",var5);
            }
        }
        return new CurrentLoginInfoDto();
    }

    private List<String> requestPrinting(HttpServletRequest httpServletRequest) {
        // 请求头存入线程缓存
        try {
            Enumeration<String> headerNames = httpServletRequest.getHeaderNames();
            List<String> list = new ArrayList<>();
            Map<String, String> map = new HashMap<>();
            if (headerNames != null) {
                while (headerNames.hasMoreElements()) {
                    String name = headerNames.nextElement();
                    String value = httpServletRequest.getHeader(name);
                    map.put(name, value);

                    list.add(String.join("====header====", name, value));
                }
                TransmittableThreadLocalUtils.setValue(map);
            }
            log.info("header==== {}", list);
            return list;
        } catch (Exception e) {
            log.info("异常：{}", e.getMessage());
        }
        return new ArrayList<>();
    }


}

