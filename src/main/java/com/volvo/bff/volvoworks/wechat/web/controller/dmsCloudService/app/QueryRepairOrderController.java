package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.app;


import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.app.QueryRepairOrderService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
@RestController
@RequestMapping("/queryRepairOrder")
@Api(value = "工单查询", tags = {"app-工单查询"})
public class QueryRepairOrderController {

    @Autowired
    private QueryRepairOrderService queryRepairOrderService;

    @ApiOperation(value = "查询所有工单列表", notes = "查询所有工单列表", httpMethod = "GET")
    @GetMapping("/queryAllRepairOrder")
    public ResponseDTO<PageBean<QueryRepairingOrderResultVo>> queryAllRepairOrder(QueryStructureCheckRrquestVo queryParams) {
        return ResponseUtils.success(queryRepairOrderService.queryAllRepairOrder(queryParams));
    }

    @ApiOperation(value = "查询工单明细信息", notes = "查询工单明细信息", httpMethod = "GET")
    @GetMapping("/queryRepairOrderDetail")
    public ResponseDTO<RepairOrderDetailsResultVo> queryRepairOrderDetail(RepairOrderDetailParamsVo paramsVo) {
        return ResponseUtils.success(queryRepairOrderService.queryRepairOrderDetail(paramsVo));
    }

    @ApiOperation(value = "扫码保存卡券oneId", notes = "扫码保存卡券oneId", httpMethod = "POST")
    @PostMapping("/saveAvailableOneId")
    public void saveAvailableOneId(@Valid @RequestBody RepairOrderAvailableOneIdParamsVo paramsVo) {
        queryRepairOrderService.saveAvailableOneId(paramsVo);
    }

}
