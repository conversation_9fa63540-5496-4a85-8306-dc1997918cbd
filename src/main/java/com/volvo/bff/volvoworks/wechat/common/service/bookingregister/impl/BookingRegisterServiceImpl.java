package com.volvo.bff.volvoworks.wechat.common.service.bookingregister.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.bookingregister.BookingOrderParamsVo;
import com.volvo.bff.volvoworks.wechat.common.service.bookingregister.BookingRegisterService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class BookingRegisterServiceImpl implements BookingRegisterService {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Override
    public BookingOrderParamsVo getBookingOrderInfoByCode2(String bookingOrderNo){
        DmsResultDTO<BookingOrderParamsVo> response = dmscloudServiceFeign.getBookingOrderInfoByCode2(bookingOrderNo);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }
}
