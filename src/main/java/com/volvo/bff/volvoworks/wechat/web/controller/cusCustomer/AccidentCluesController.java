package com.volvo.bff.volvoworks.wechat.web.controller.cusCustomer;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.accidentClues.AccidentCluesDTO;
import com.volvo.bff.volvoworks.wechat.common.model.po.accidentClues.AccidentCluesPO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.accidentClues.AccidentCluesService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api("/accidentClues")
@RestController
@RequestMapping("/accidentClues")
@Slf4j
public class AccidentCluesController {

    @Resource
    AccidentCluesService accidentCluesService;

    @ApiOperation(value = "分页查询", notes = "分页查询", httpMethod = "GET")
    @GetMapping
    public ResponseDTO<PageBean<AccidentCluesPO>> selectPageBysql(AccidentCluesDTO dto, @RequestParam("currentPage") Long currentPage, @RequestParam("pageSize") Long pageSize) {
        return ResponseUtils.success(accidentCluesService.selectPageBysql(dto,currentPage,pageSize));
    }
}
