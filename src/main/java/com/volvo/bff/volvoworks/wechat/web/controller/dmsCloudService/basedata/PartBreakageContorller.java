package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.basedata;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.service.basedata.PartBreakageService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;

import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Api(value = "/basedata/partBreakage", tags = {"配件报损"})
@RestController
@RequestMapping("/basedata/partBreakage")
public class PartBreakageContorller {

    @Autowired
    private PartBreakageService partBreakageService;

    /**
     * 报损单弹窗查询
     *
     * @return
     */

    @ApiOperation(value = "报损单弹窗查询", notes = "根据报损单号{lossNo}查询报损单主记录", httpMethod = "GET")
    @RequestMapping(value = "/queryPartLossByLossNo", method = RequestMethod.GET)
    public ResponseDTO<PageInfoDto> queryPartLossByLossNo(@RequestParam Map<String, String> param) {
        PageInfoDto pageInfoDto = partBreakageService.queryPartLossByLossNo(param);
        return ResponseUtils.success(pageInfoDto);
    }

    @ApiOperation(value = "报损单明细查询", notes = "根据报损单号{lossNo}查询报损单明细记录", httpMethod = "GET")
    @RequestMapping(value = "/queryPartLossItem", method = RequestMethod.GET)
    public ResponseDTO<List<Map>> queryPartLossItem(String lossNo) {
        List<Map> mapList = partBreakageService.queryPartLossItem(lossNo);
        return ResponseUtils.success(mapList);
    }

    @ApiOperation(value = "报损单出库", notes = "根据报损单号出库,更新盘点单状态", httpMethod = "GET")
    @RequestMapping(value = "/outStorage", method = RequestMethod.GET)
    public ResponseDTO<Map<String, Object>> outStorage(String lossNo, String inventoryNo, String operType, String operReason){
        //报损是否需要车厂审核
        return ResponseUtils.success(partBreakageService.outStorage(lossNo, inventoryNo, operType, operReason));
    }
}
