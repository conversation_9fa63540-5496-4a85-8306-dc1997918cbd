package com.volvo.bff.volvoworks.wechat.common.model.dto.pds;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@ApiModel("PdsConfigsDTO")
public class PdsConfigsDTO extends PdsConfigDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	// 子集
	@ApiModelProperty(name = "childList")
	private List<PdsConfigDTO> childList;
}