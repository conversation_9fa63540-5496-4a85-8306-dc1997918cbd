package com.volvo.bff.volvoworks.wechat.common.feign;

import com.volvo.bff.volvoworks.wechat.common.annotation.ErrorCodeConversion;
import com.volvo.bff.volvoworks.wechat.common.model.dto.order.ListAdMaintainDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;


@FeignClient(name = "application-postsale-management",
        url = "${mse-in.tob.application-postsale-management.url}",
        path = "${mse-in.tob.application-postsale-management.path}")
public interface ApplicationPostsaleManagementFeign {

    @ApiOperation(value = "维修领料出库接口", notes = "维修领料出库接口")
    @PostMapping("/ttRepairOrder/v1/account")
    @ErrorCodeConversion
    DmsResultDTO<String> account(ListAdMaintainDTO listAdMaintainDTO);
}
