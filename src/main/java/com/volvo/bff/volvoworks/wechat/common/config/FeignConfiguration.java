package com.volvo.bff.volvoworks.wechat.common.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.volvo.bff.volvoworks.wechat.common.feign.interceptor.MyFeignLogger;

import feign.Logger;

@Configuration
public class FeignConfiguration {
	
    @Bean
    @ConditionalOnProperty("logging.level.com.volvo.bff.volvoworks.wechat.common.feign.interceptor.MyFeignLogger")
    MyFeignLogger createMyLogger() {
        return new MyFeignLogger();
    }
    
    @Bean
    Logger.Level feignLogLevel() {
        return Logger.Level.FULL;
    }
}
