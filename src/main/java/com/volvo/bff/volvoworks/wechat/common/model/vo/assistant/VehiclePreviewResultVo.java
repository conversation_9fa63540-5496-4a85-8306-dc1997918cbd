package com.volvo.bff.volvoworks.wechat.common.model.vo.assistant;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("查询所有预检单结果VO")
public class VehiclePreviewResultVo {

    @ApiModelProperty(value = "预检单号",name = "yjNo")
    private String yjNo;

    @ApiModelProperty(value = "牌照",name = "license")
    private String license;

    @ApiModelProperty(value = "车主",name = "ownerName")
    private String ownerName;

    @ApiModelProperty(value = "预检单创建时间",name = "createdAt",example = "2019-09-04 00:00:00")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private String createdAt;

    @ApiModelProperty(value = "工单号",name = "roNo")
    private String roNo;

    @ApiModelProperty(value = "服务顾问",name = "serviceAdvisor")
    private String serviceAdvisor;
}
