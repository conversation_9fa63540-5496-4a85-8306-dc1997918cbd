package com.volvo.bff.volvoworks.wechat.common.model.dto.vhc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description VHC故障现象信息
 * @Date 2024/10/8 16:00
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("VHC故障现象信息")
public class VhcMalfunctionInfoDto {

    @ApiModelProperty("检查项故障现象代码")
    private String itemMalfunctionCode;

    @ApiModelProperty("检查项故障现象")
    private String itemMalfunction;

}
