package com.volvo.bff.volvoworks.wechat.common.model.dto.accidentClues;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ApiModel(value="AccidentCluesAllotDTO",description="事故线索分配")
@Data
public class AccidentCluesAllotDTO implements Serializable {

  @ApiModelProperty(value = "系统ID",name="appId")
  private String appId;

  @ApiModelProperty(value = "所有者代码",name="ownerCode")
  private String ownerCode;

  @ApiModelProperty(value = "所有者的父组织代码",name="ownerParCode")
  private String ownerParCode;

  @ApiModelProperty(value = "组织ID",name="orgId")
  private Integer orgId;

  @ApiModelProperty(value = "主键ID",name="allotId")
  private Integer allotId;

  @ApiModelProperty(value = "tt_accident_clues",name="acId")
  private Integer acId;

  @ApiModelProperty(value = "经销商代码",name="dealerCode")
  private String dealerCode;

  @ApiModelProperty(value = "跟进人员",name="followPeople")
  private Integer followPeople;

  @ApiModelProperty(value = "分配人员",name="allotPersonnel")
  private Integer allotPersonnel;

  @ApiModelProperty(value = "分配时间",name="allotDate")
  private Date allotDate;

  @ApiModelProperty(value = "数据来源",name="dataSources")
  private Integer dataSources;

  @ApiModelProperty(value = "是否删除，1：删除，0：未删除",name="isDeleted")
  private Integer isDeleted;

  @ApiModelProperty(value = "是否有效",name="isValid")
  private Integer isValid;

  @ApiModelProperty(value = "创建时间",name="createdAt")
  private Date createdAt;

  @ApiModelProperty(value = "创建人",name="createdBy")
  private String createdBy;

  @ApiModelProperty(value = "更新时间",name="updatedAt")
  private Date updatedAt;

  @ApiModelProperty(value = "更新人",name="updatedBy")
  private String updatedBy;

  @ApiModelProperty(value = "版本号（乐观锁）",name="recordVersion")
  private Integer recordVersion;

  //线索ids
  private List<Integer> acIds;

  //跟进人员ids
  private List<Integer> followPeoples;
  
  //跟进人员ids
  private List<AllotFollowUserInfoDTO> followUserList;

}
