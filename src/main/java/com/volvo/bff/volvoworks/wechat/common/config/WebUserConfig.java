package com.volvo.bff.volvoworks.wechat.common.config;

import com.volvo.bff.volvoworks.wechat.common.interceptor.DMSUserInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.Validator;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 功能描述：web配置信息增加拦截器，获取登录信息
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@Configuration
public class WebUserConfig implements WebMvcConfigurer {
//    @Autowired
//    private ValidationConfig validationConfig;

    public WebUserConfig() {
    }

    @Bean
    public HandlerInterceptor getUserRestInterceptor() {
        return new DMSUserInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(this.getUserRestInterceptor()).addPathPatterns(new String[] {"/**"});
    }

//    @Override
//    public Validator getValidator() {
//        return this.validationConfig.localValidatorFactoryBean();
//    }

    /*@Bean
    public FilterRegistrationBean<SwaggerFilter> swaggerFilterRegistration() {
        FilterRegistrationBean<SwaggerFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new SwaggerFilter());
        registration.addUrlPatterns("/v2/api-docs"); // 确保路径匹配
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE); // 提高优先级
        return registration;
    }*/
}