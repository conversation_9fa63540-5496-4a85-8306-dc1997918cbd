package com.volvo.bff.volvoworks.wechat.common.model.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName QueryStructureCheckRrquestVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/6/2 16:32
 */
@Data
@ApiModel("结构件在修检查查询条件VO")
public class QueryStructureCheckRrquestVo {

    @ApiModelProperty(value = "车主",name = "ownerName")
    private String ownerName;

    @ApiModelProperty(value = "车牌号",name = "license")
    private String license;

    @ApiModelProperty(value = "服务专员",name = "serviceAdvisor")
    private String serviceAdvisor;

    @ApiModelProperty(value = "工单号",name = "roNo")
    private String roNo;

    @ApiModelProperty(value = "工单开单时间",name = "roCreateDate")
    private String roCreateDate;

    @ApiModelProperty(value = "工单结束时间",name = "roEndDate")
    private String roEndDate;

    @ApiModelProperty(value = "所有者代码",name = "ownerCode",hidden = true)
    private String ownerCode;

    @ApiModelProperty(value = "是否质检",name = "isSeasonCheck",hidden = true)
    private Integer isSeasonCheck;

    @ApiModelProperty(value = "是否派工",name = "AssignTag",hidden = true)
    private Integer AssignTag;

    @ApiModelProperty(value = "当前登录人",name = "chiefTechnician",hidden = true)
    private String chiefTechnician;

    @ApiModelProperty(value = "工单状态",name = "roStatus",hidden = true)
    private String roStatus;

    @ApiModelProperty(value = "search",name = "search",hidden = true)
    private String search;

    @ApiModelProperty(value = "角色",name = "role")
    private String role;

    @ApiModelProperty(value = "用户id",name = "userId")
    private Long userId;

}
