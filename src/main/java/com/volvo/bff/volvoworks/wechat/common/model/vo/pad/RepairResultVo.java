package com.volvo.bff.volvoworks.wechat.common.model.vo.pad;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("转工单成功后结果集")
public class RepairResultVo implements Serializable {

    @ApiModelProperty(value = "工单号",name = "roNo")
    private String roNo;

    @ApiModelProperty(value = "进厂行驶里程" , name = "inMileage")
    private String inMileage;

    @ApiModelProperty(value = "车型" , name = "model")
    private String model;

    @ApiModelProperty(value = "发动机号" , name = "engineNo")
    private String engineNo;

    @ApiModelProperty(value = "工单状态" , name = "roStatus")
    private Long roStatus;

    @ApiModelProperty(value = "是否作废" , name = "isDeleted")
    private String isDeleted;
}
