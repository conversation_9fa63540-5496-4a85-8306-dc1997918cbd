package com.volvo.bff.volvoworks.wechat.common.model.dto.login;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

public class MidUserDto  implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("用户id")
    private String userId;
    @ApiModelProperty("员工姓名")
    private String employeeName;
    @ApiModelProperty("公司id")
    private String companyId;
    @ApiModelProperty("公司code")
    private String companyCode;
    @ApiModelProperty("当前登录公司code")
    private String ownerCode;
    @ApiModelProperty("dataType")
    private Integer dataType;
    @ApiModelProperty("员工组织id")
    private Integer orgId;
    @ApiModelProperty("员工组织以及以下id")
    private String orgIds;
    @ApiModelProperty("（pc/app）")
    private String loginWay;
    @ApiModelProperty("集团code")
    private String groupCode;
    @ApiModelProperty("当前职位id")
    private Integer userOrgId;
    @ApiModelProperty("角色列表")
    private List<RoleLoginVO> roleList;

    public MidUserDto() {
    }

    public String getUserId() {
        return this.userId;
    }

    public String getEmployeeName() {
        return this.employeeName;
    }

    public String getCompanyId() {
        return this.companyId;
    }

    public String getCompanyCode() {
        return this.companyCode;
    }

    public String getOwnerCode() {
        return this.ownerCode;
    }

    public Integer getDataType() {
        return this.dataType;
    }

    public Integer getOrgId() {
        return this.orgId;
    }

    public String getOrgIds() {
        return this.orgIds;
    }

    public String getLoginWay() {
        return this.loginWay;
    }

    public String getGroupCode() {
        return this.groupCode;
    }

    public Integer getUserOrgId() {
        return this.userOrgId;
    }

    public List<RoleLoginVO> getRoleList() {
        return this.roleList;
    }

    public void setUserId(final String userId) {
        this.userId = userId;
    }

    public void setEmployeeName(final String employeeName) {
        this.employeeName = employeeName;
    }

    public void setCompanyId(final String companyId) {
        this.companyId = companyId;
    }

    public void setCompanyCode(final String companyCode) {
        this.companyCode = companyCode;
    }

    public void setOwnerCode(final String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public void setDataType(final Integer dataType) {
        this.dataType = dataType;
    }

    public void setOrgId(final Integer orgId) {
        this.orgId = orgId;
    }

    public void setOrgIds(final String orgIds) {
        this.orgIds = orgIds;
    }

    public void setLoginWay(final String loginWay) {
        this.loginWay = loginWay;
    }

    public void setGroupCode(final String groupCode) {
        this.groupCode = groupCode;
    }

    public void setUserOrgId(final Integer userOrgId) {
        this.userOrgId = userOrgId;
    }

    public void setRoleList(final List<RoleLoginVO> roleList) {
        this.roleList = roleList;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof MidUserDto)) {
            return false;
        } else {
            MidUserDto other = (MidUserDto)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label155: {
                    Object this$userId = this.getUserId();
                    Object other$userId = other.getUserId();
                    if (this$userId == null) {
                        if (other$userId == null) {
                            break label155;
                        }
                    } else if (this$userId.equals(other$userId)) {
                        break label155;
                    }

                    return false;
                }

                Object this$employeeName = this.getEmployeeName();
                Object other$employeeName = other.getEmployeeName();
                if (this$employeeName == null) {
                    if (other$employeeName != null) {
                        return false;
                    }
                } else if (!this$employeeName.equals(other$employeeName)) {
                    return false;
                }

                Object this$companyId = this.getCompanyId();
                Object other$companyId = other.getCompanyId();
                if (this$companyId == null) {
                    if (other$companyId != null) {
                        return false;
                    }
                } else if (!this$companyId.equals(other$companyId)) {
                    return false;
                }

                label134: {
                    Object this$companyCode = this.getCompanyCode();
                    Object other$companyCode = other.getCompanyCode();
                    if (this$companyCode == null) {
                        if (other$companyCode == null) {
                            break label134;
                        }
                    } else if (this$companyCode.equals(other$companyCode)) {
                        break label134;
                    }

                    return false;
                }

                label127: {
                    Object this$ownerCode = this.getOwnerCode();
                    Object other$ownerCode = other.getOwnerCode();
                    if (this$ownerCode == null) {
                        if (other$ownerCode == null) {
                            break label127;
                        }
                    } else if (this$ownerCode.equals(other$ownerCode)) {
                        break label127;
                    }

                    return false;
                }

                label120: {
                    Object this$dataType = this.getDataType();
                    Object other$dataType = other.getDataType();
                    if (this$dataType == null) {
                        if (other$dataType == null) {
                            break label120;
                        }
                    } else if (this$dataType.equals(other$dataType)) {
                        break label120;
                    }

                    return false;
                }

                Object this$orgId = this.getOrgId();
                Object other$orgId = other.getOrgId();
                if (this$orgId == null) {
                    if (other$orgId != null) {
                        return false;
                    }
                } else if (!this$orgId.equals(other$orgId)) {
                    return false;
                }

                label106: {
                    Object this$orgIds = this.getOrgIds();
                    Object other$orgIds = other.getOrgIds();
                    if (this$orgIds == null) {
                        if (other$orgIds == null) {
                            break label106;
                        }
                    } else if (this$orgIds.equals(other$orgIds)) {
                        break label106;
                    }

                    return false;
                }

                Object this$loginWay = this.getLoginWay();
                Object other$loginWay = other.getLoginWay();
                if (this$loginWay == null) {
                    if (other$loginWay != null) {
                        return false;
                    }
                } else if (!this$loginWay.equals(other$loginWay)) {
                    return false;
                }

                label92: {
                    Object this$groupCode = this.getGroupCode();
                    Object other$groupCode = other.getGroupCode();
                    if (this$groupCode == null) {
                        if (other$groupCode == null) {
                            break label92;
                        }
                    } else if (this$groupCode.equals(other$groupCode)) {
                        break label92;
                    }

                    return false;
                }

                Object this$userOrgId = this.getUserOrgId();
                Object other$userOrgId = other.getUserOrgId();
                if (this$userOrgId == null) {
                    if (other$userOrgId != null) {
                        return false;
                    }
                } else if (!this$userOrgId.equals(other$userOrgId)) {
                    return false;
                }

                Object this$roleList = this.getRoleList();
                Object other$roleList = other.getRoleList();
                if (this$roleList == null) {
                    if (other$roleList != null) {
                        return false;
                    }
                } else if (!this$roleList.equals(other$roleList)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof MidUserDto;
    }

    public int hashCode() {
        boolean PRIME = true;
        int result = 1;
        Object $userId = this.getUserId();
        result = result * 59 + ($userId == null ? 43 : $userId.hashCode());
        Object $employeeName = this.getEmployeeName();
        result = result * 59 + ($employeeName == null ? 43 : $employeeName.hashCode());
        Object $companyId = this.getCompanyId();
        result = result * 59 + ($companyId == null ? 43 : $companyId.hashCode());
        Object $companyCode = this.getCompanyCode();
        result = result * 59 + ($companyCode == null ? 43 : $companyCode.hashCode());
        Object $ownerCode = this.getOwnerCode();
        result = result * 59 + ($ownerCode == null ? 43 : $ownerCode.hashCode());
        Object $dataType = this.getDataType();
        result = result * 59 + ($dataType == null ? 43 : $dataType.hashCode());
        Object $orgId = this.getOrgId();
        result = result * 59 + ($orgId == null ? 43 : $orgId.hashCode());
        Object $orgIds = this.getOrgIds();
        result = result * 59 + ($orgIds == null ? 43 : $orgIds.hashCode());
        Object $loginWay = this.getLoginWay();
        result = result * 59 + ($loginWay == null ? 43 : $loginWay.hashCode());
        Object $groupCode = this.getGroupCode();
        result = result * 59 + ($groupCode == null ? 43 : $groupCode.hashCode());
        Object $userOrgId = this.getUserOrgId();
        result = result * 59 + ($userOrgId == null ? 43 : $userOrgId.hashCode());
        Object $roleList = this.getRoleList();
        result = result * 59 + ($roleList == null ? 43 : $roleList.hashCode());
        return result;
    }

    public String toString() {
        return "MidUserDto(userId=" + this.getUserId() + ", employeeName=" + this.getEmployeeName() + ", companyId=" + this.getCompanyId() + ", companyCode=" + this.getCompanyCode() + ", ownerCode=" + this.getOwnerCode() + ", dataType=" + this.getDataType() + ", orgId=" + this.getOrgId() + ", orgIds=" + this.getOrgIds() + ", loginWay=" + this.getLoginWay() + ", groupCode=" + this.getGroupCode() + ", userOrgId=" + this.getUserOrgId() + ", roleList=" + this.getRoleList() + ")";
    }
}
