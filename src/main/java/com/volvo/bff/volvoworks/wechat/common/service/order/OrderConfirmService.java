package com.volvo.bff.volvoworks.wechat.common.service.order;

import com.volvo.bff.volvoworks.wechat.common.model.dto.order.OrderConfirmVo;
import org.springframework.stereotype.Service;


public interface OrderConfirmService {

    /**
     * 判断工单结算是否已确认
     * @param confirmVo
     */
    String orderIsOrNotConfirm(OrderConfirmVo confirmVo);

    /**
     * 工单结算确认
     * @param confirmVo
     */
    void orderStatementConfirm(OrderConfirmVo confirmVo);
}
