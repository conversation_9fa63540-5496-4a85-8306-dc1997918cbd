package com.volvo.bff.volvoworks.wechat.common.service.workshop.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.ApplicationMaintainManagementFeign;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.dto.LucencyWorkshop.*;
import com.volvo.bff.volvoworks.wechat.common.model.dto.LucencyWorkshop.QueryParamDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.workshop.LucencyWorkShopService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class LucencyWorkShopServiceImpl implements LucencyWorkShopService {

    @Autowired
    private ApplicationMaintainManagementFeign applicationMaintainManagementFeign;

    @Override
    public PageBean<DeliveryDTO> selectDeliveryList(QueryParamDto queryParamDto) {
        DmsResultDTO<PageBean<DeliveryDTO>> pageBeanDmsResultDTO = applicationMaintainManagementFeign.selectDeliveryList(queryParamDto);
        return pageBeanDmsResultDTO.getData();
    }


    @Override
    public PageBean<BeginOrderDTO> selectBeginOrderList(QueryParamDto queryParamDto) {
        DmsResultDTO<PageBean<BeginOrderDTO>> pageBeanDmsResultDTO = applicationMaintainManagementFeign.selectBeginOrderList(queryParamDto);
        return pageBeanDmsResultDTO.getData();
    }

    @Override
    public List<StatusCountDTO> selectAssignStatusCount(QueryParamDto queryParamDto) {
        DmsResultDTO<List<StatusCountDTO>> listDmsResultDTO = applicationMaintainManagementFeign.selectAssignStatusCount(queryParamDto);
        return listDmsResultDTO.getData();
    }

    @Override
    public PageBean<WorkShopRepairOrderDTO> selectWorkshopRepairOrder(QueryParamDto queryParamDto) {
        DmsResultDTO<PageBean<WorkShopRepairOrderDTO>> pageBeanDmsResultDTO = applicationMaintainManagementFeign.selectWorkshopRepairOrder(queryParamDto);
        return pageBeanDmsResultDTO.getData();
    }

    @Override
    public List<StatusCountDTO> selectIsPunchCount(QueryParamDto queryParamDto) {
        DmsResultDTO<List<StatusCountDTO>> listDmsResultDTO = applicationMaintainManagementFeign.selectIsPunchCount(queryParamDto);
        return listDmsResultDTO.getData();
    }

    @Override
    public PageBean<WorkShopBalanceOrderDTO> selectWorkShopBalanceOrder(QueryParamDto queryParamDto) {
        DmsResultDTO<PageBean<WorkShopBalanceOrderDTO>> pageBeanDmsResultDTO = applicationMaintainManagementFeign.selectWorkShopBalanceOrder(queryParamDto);
        return pageBeanDmsResultDTO.getData();
    }

    @Override
    public List<StatusCountDTO> selectDeliveryTagCount(QueryParamDto queryParamDto) {
        DmsResultDTO<List<StatusCountDTO>> listDmsResultDTO = applicationMaintainManagementFeign.selectDeliveryTagCount(queryParamDto);
        return listDmsResultDTO.getData();
    }
}
