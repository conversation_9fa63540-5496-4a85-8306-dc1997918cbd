package com.volvo.bff.volvoworks.wechat.web.controller.dmsCloudService.midEndCenter;

import com.volvo.bff.volvoworks.wechat.common.model.dto.basedata.LoginInfoDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.midEndInterface.BrandVO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.midEndInterface.ModelVO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.midEndInterface.SeriesVO;
import com.volvo.bff.volvoworks.wechat.common.service.midEndInterface.MidUrlProperties;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api("/midEnd/basicData")
@RestController
@RequestMapping("/midEnd/basicData")
public class MidEndDataCenterController {

    @Autowired
    private MidUrlProperties midUrlProperties;
    /**
     * 产品:查询全部车型
     * 沃尔沃:查询所有车款
     *
     * @author: LWH
     * @date: 2020年06月11
     * @return
     */
    @ApiOperation(value = "产品:查询全部车型 沃尔沃:查询所有车款", notes = "产品:查询全部车型 沃尔沃:查询所有车款", httpMethod = "GET")
    @RequestMapping(value = "/model", method = RequestMethod.GET)
    public ResponseDTO<List<ModelVO>> getVehicleModelAll() {
        return  ResponseUtils.success(midUrlProperties.getVehicleModelAll());

    }


    /**
     * 产品:查询全部车系
     * 沃尔沃:查询所有车型
     *
     * @author: LWH
     * @date: 2020年06月11
     * @return
     */
    @ApiOperation(value = "产品:查询全部车系 沃尔沃:查询所有车型", notes = "产品:查询全部车系 沃尔沃:查询所有车型", httpMethod = "GET")
    @RequestMapping(value = "/series", method = RequestMethod.GET)
    public ResponseDTO<List<SeriesVO>> getVehicleSeriesAll() {
        return ResponseUtils.success(midUrlProperties.getAllSeriesCodeUrl());

    }

    /**
     * 查询全部品牌
     *
     * @author: LWH
     * @date: 2020年06月11
     * @return
     */
    @ApiOperation(value = "查询全部品牌", notes = "查询全部品牌", httpMethod = "GET")
    @RequestMapping(value = "/brand", method = RequestMethod.GET)
    public ResponseDTO<List<BrandVO>> getVehicleBrandAll() {
        return ResponseUtils.success(midUrlProperties.getAllBrandCodeUrl());

    }

    /**
     * 获取登陆人信息
     * <AUTHOR>
     * @date 2020/8/29
     * @return
     */
    @ApiOperation(value = "获取登陆人信息", notes = "获取登陆人信息", httpMethod = "GET")
    @RequestMapping(value = "/getLoginInfo", method = RequestMethod.GET)
    public ResponseDTO<LoginInfoDto> getLoginInfo() {
        return ResponseUtils.success(midUrlProperties.getLoginInfo());
    }

}
