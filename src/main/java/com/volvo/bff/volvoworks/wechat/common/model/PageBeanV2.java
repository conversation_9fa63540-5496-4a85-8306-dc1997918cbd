package com.volvo.bff.volvoworks.wechat.common.model;

import java.util.Collections;
import java.util.List;

import lombok.Data;

@Data
public class PageBeanV2<T> {

    /**
     * 查询数据列表
     */
    private List<T> records = Collections.emptyList();

    /**
     * 总数
     */
    private long total = 0;
    /**
     * 每页显示条数，默认 10
     */
    private long size = 10;

    /**
     * 当前页
     */
    private long current = 1;

    /**
     * 总页数
     */
    private long pages = 1;
}
