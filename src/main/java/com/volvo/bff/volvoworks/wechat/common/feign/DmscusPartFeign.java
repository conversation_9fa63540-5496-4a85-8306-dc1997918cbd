package com.volvo.bff.volvoworks.wechat.common.feign;

import java.util.List;
import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import com.volvo.bff.volvoworks.wechat.common.model.PageBean;

@FeignClient(name = "dmscus-part",
		url = "${mse-in.tob.dmscus-part.url}",
		path = "${mse-in.tob.dmscus-part.path}")
public interface DmscusPartFeign {

}
