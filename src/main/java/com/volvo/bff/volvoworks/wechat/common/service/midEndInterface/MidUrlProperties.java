package com.volvo.bff.volvoworks.wechat.common.service.midEndInterface;

import com.volvo.bff.volvoworks.wechat.common.model.dto.basedata.LoginInfoDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.midEndInterface.BrandVO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.midEndInterface.ModelVO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.midEndInterface.SeriesVO;

import java.util.List;

public interface MidUrlProperties {

    List<ModelVO> getVehicleModelAll();

    List<SeriesVO> getAllSeriesCodeUrl();

    List<BrandVO> getAllBrandCodeUrl();

    LoginInfoDto getLoginInfo();
}
