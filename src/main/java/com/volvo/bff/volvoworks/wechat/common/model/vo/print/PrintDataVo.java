package com.volvo.bff.volvoworks.wechat.common.model.vo.print;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.volvo.bff.volvoworks.wechat.common.model.vo.midEndInterface.CompanyDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@ApiModel("通用打印数据Vo")
@Data
public class PrintDataVo {

    // 经销商信息
    @ApiModelProperty(value = "经销商基本信息", name = "companyInfo")
    private CompanyDetailDTO companyInfo;

    // 传入参数
    @ApiModelProperty(value = "打印单-打印单地区", name = "printRegion")
    private String printRegion;


    // 车辆信息
    @ApiModelProperty(value = "车主姓名", name = "ownerName")
    private String ownerName;
    @ApiModelProperty(value = "车主手机号", name = "ownerMobile")
    private String ownerMobile;
    @ApiModelProperty(value = "车主性别", name = "gender")
    private String gender;
    @ApiModelProperty(value = "车牌号", name = "license")
    private String license;
    @ApiModelProperty(value = "车型", name = "model")
    private String model;
    @ApiModelProperty(value = "车辆识别号", name = "vin")
    private String vin;
    @ApiModelProperty(value = "发动机号", name = "engineNo")
    private String engineNo;
    @ApiModelProperty(value = "保修起始日期", name = "wrtBeginDate")
    private String wrtBeginDate;
    @ApiModelProperty(value = "进厂里程", name = "inMileage")
    private String inMileage;
    @ApiModelProperty(value = "送修人", name = "deliverer")
    private String deliverer;
    @ApiModelProperty(value = "送修人手机", name = "delivererMobile")
    private String delivererMobile;
    @ApiModelProperty(value = "完工时间", name = "completeTime")
    private String completeTime;
    @ApiModelProperty(value = "完工里程", name = "itmeEndMileage")
    private String itmeEndMileage;
    @ApiModelProperty(value = "行驶里程", name = "mileage")
    private String mileage;

    // 表格信息
    @ApiModelProperty(value = "配件工时附加项目", name = "labourPart")
    private List<PrintTableGroupVo> labourPart;
    @ApiModelProperty(value = "优惠项目", name = "discountList")
    private List<PrintDiscountListVo> discountList;
    @ApiModelProperty(value = "收费对象", name = "payObj")
    private String payObj;

    // 费用相关
    @ApiModelProperty(value = "工时费用", name = "labourAmount")
    private String labourAmount;
    @ApiModelProperty(value = "工时合计", name = "labourTotal")
    private String labourTotal;
    @ApiModelProperty(value = "工时费用-折扣金额", name = "discountForLabourAmount")
    private String discountForLabourAmount;
    @ApiModelProperty(value = "工时费用-折扣后金额", name = "labourAmountAfterDiscount")
    private String labourAmountAfterDiscount;
    @ApiModelProperty(value = "零件费用", name = "partAmount")
    private String partAmount;
    @ApiModelProperty(value = "零件费用-折扣金额", name = "discountForPartAmount")
    private String discountForPartAmount;
    @ApiModelProperty(value = "零件费用-折扣后金额", name = "partAmountAfterDiscount")
    private String partAmountAfterDiscount;
    @ApiModelProperty(value = "附加费用", name = "addItemAmount")
    private String addItemAmount;
    @ApiModelProperty(value = "附加费用-折扣金额", name = "addItemAmount")
    private String discountForAddItemAmount;
    @ApiModelProperty(value = "附加项目-折扣后金额", name = "addItemAmountAfterDiscount")
    private String addItemAmountAfterDiscount;
    @ApiModelProperty(value = "总计费用（工时+配件+附加项目）-折扣", name = "amountForDiscount")
    private String amountForDiscount;
    @ApiModelProperty(value = "总计费用", name = "amount")
    private String amount;
    @ApiModelProperty(value = "折扣金额", name = "discountAmount")
    private String discountAmount;
    @ApiModelProperty(value = "优惠金额", name = "preferentialAmount")
    private String preferentialAmount;
    @ApiModelProperty(value = "收费对象-去零金额", name = "paySub")
    private String paySub;
    @ApiModelProperty(value = "收费对象-圆整金额", name = "payYz")
    private String payYz;
    @ApiModelProperty(value = "实际金额", name = "receiveAmount")
    private String receiveAmount;

    // 工单信息
    @ApiModelProperty(value = "工单号", name = "roNo")
    private String roNo;
    @ApiModelProperty(value = "工单创建时间", name = "roCreateDate")
    private String roCreateDate;
    @ApiModelProperty(value = "工单打印时间", name = "roPrintTime")
    private String roPrintTime;
    @ApiModelProperty(value = "工单备注", name = "roRemark")
    private String roRemark;
    @ApiModelProperty(value = "工单开工时间", name = "itemStartTime")
    private String itemStartTime;
    @ApiModelProperty(value = "维修类型", name = "repairType")
    private String repairType;
    @ApiModelProperty(value = "估价单号", name = "estimateNo")
    private String estimateNo;
    @ApiModelProperty(value = "估价单时间", name = "estimateTime")
    private String estimateTime;
    @ApiModelProperty(value = "预约单号",name = "bookingOrderNo")
    private String bookingOrderNo;
    @ApiModelProperty(value = "预约时间",name = "bookingTime")
    private String bookingTime;
    @ApiModelProperty(value = "预交车时间", name = "endTimeSupposed")
    private String endTimeSupposed;
    @ApiModelProperty(value = "销售日期", name = "salesDate")
    private String salesDate;
    @ApiModelProperty(value = "服务顾问", name = "serviceAdvisor")
    private String serviceAdvisor;
    @ApiModelProperty(value = "客户描述", name = "customerDesc")
    private String customerDesc;
    @ApiModelProperty(value = "是否延保工单", name = "isExtendInsurance")
    private String isExtendInsurance;
    @ApiModelProperty(value = "是否服务合同购买工单", name = "isServeContractBuyOrder")
    private String isServeContractBuyOrder;
    @ApiModelProperty(value = "是否保养活动工单", name = "isUpkeepActivityOrder")
    private String isUpkeepActivityOrder;
    @ApiModelProperty(value = "注意事项勾选框", name = "noticeCheck")
    private String noticeCheck;
    @ApiModelProperty(value = "是否要洗车", name = "isCanWash")
    private String isCanWash;


    // 结算单-结算通用相关字段
    @ApiModelProperty(value = "结算单号", name = "balanceNo")
    private String balanceNo;
    @ApiModelProperty(value = "结算员", name = "balanceHandler")
    private String balanceHandler;
    @ApiModelProperty(value = "本次未做项目", name = "notDoneProjectThisTime")
    private String notDoneProjectThisTime;
    @ApiModelProperty(value = "结算备注", name = "balanceRemark")
    private String balanceRemark;
    @ApiModelProperty(value = "结算时间", name = "balanceTime")
    private String balanceTime;
    @ApiModelProperty(value = "结算单打印时间", name = "balancePrintTime")
    private String balancePrintTime;
    @ApiModelProperty(value = "支付方式", name = "payType")
    private String payType;

    // 结算单-延保服务相关字段
    @ApiModelProperty(value = "下次保养日期", name = "nextMaintainDate")
    private String nextMaintainDate;
    @ApiModelProperty(value = "下次保养里程", name = "nextMaintainMileage")
    private String nextMaintainMileage;
    @ApiModelProperty(value = "延保类型(出险无忧和钥匙置换)", name = "extendType")
    private String extendType;
    @ApiModelProperty(value = "延保类型(大类)", name = "extendInsuranceBigType")
    private String extendInsuranceBigType;
    @ApiModelProperty(value = "延保类型(小类)", name = "extendInsuranceSmallType")
    private String extendInsuranceSmallType;
    @ApiModelProperty(value = "延保时长(月)", name = "延保时长(月)")
    private String extendInsuranceDuration;
    @ApiModelProperty(value = "延保服务范围", name = "延保服务范围")
    private String extendInsuranceRange;
    @ApiModelProperty(value = "延保服务名称", name = "延保服务名称")
    private String extendInsuranceName;
    @ApiModelProperty(value = "延保服务开始时间", name = "延保服务开始时间")
    private String extendInsuranceStartDate;
    @ApiModelProperty(value = "延保服务结束时间", name = "延保服务结束时间")
    private String extendInsuranceEndDate;

    // 结算单-使用服务合同相关字段
    @ApiModelProperty(value = "使用服务合同次数", name = "使用服务合同次数")
    private String useCountText;

    // 结算单-服务合同购买相关字段
    @ApiModelProperty(value = "保养类型", name = "保养类型")
    private String maintenanceType;
    @ApiModelProperty(value = "保养合同名称", name = "保养合同名称")
    private String maintenanceContract;

    // 预打印专用
    @ApiModelProperty(value = "打印单-打印类型(RO:工单 BO:结算单 YO:预约单 EO:估价单 EI:延保服务合同 US:使用服务合同打印 BS:服务合同购买 PBO:预打印)", name = "printType")
    private String printType;

    // 预检单专用
    @ApiModelProperty(value = "技师", name = "technician")
    private String technician;
    @ApiModelProperty(value = "进厂时间", name = "inTime")
    private String inTime;

    // 质检报告专用
    @ApiModelProperty(value = "质检单单号", name = "qcRoNo")
    private String qcRoNo;
    @ApiModelProperty(value = "质检单车主姓名", name = "qcOwnerName")
    private String qcOwnerName;
    @ApiModelProperty(value = "质检单车主手机", name = "qcOwnerMobile")
    private String qcOwnerMobile;
    @ApiModelProperty(value = "质检单车牌号", name = "qcLicense")
    private String qcLicense;
    @ApiModelProperty(value = "质检单vin", name = "qcVin")
    private String qcVin;
    @ApiModelProperty(value = "是否包含溯源件", name = "qcVin")
    private String isContainSource;
    @ApiModelProperty(value = "车辆症状",name = "vehicleSymptom")
    private String vehicleSymptom;
    @ApiModelProperty(value = "燃油类型",name = "fuelType")
    private String fuelType;
    @ApiModelProperty(value = "C端H5页面",name = "h5Url")
    private String h5Url;
    @ApiModelProperty(value = "太阳码",name = "sunCode")
    private String sunCode;
    /**
     * 结算单确认渠道
     */
    @ApiModelProperty(value = "结算单确认渠道",name = "operateChannel")
    private String operateChannel;

    /**
     * 操作用户
     */
    @ApiModelProperty(value = "确认人",name = "userName")
    private String userName;

    /**
     * 用户类型(客户/服务顾问)
     */
    @ApiModelProperty(value = "确认人类型",name = "userType")
    private String userType;

    /**
     * 最终确认时间
     */
    @ApiModelProperty(value = "最终确认时间",name = "endDate")
    private String endDate;

    @ApiModelProperty(value = "是否带走旧件",name = "isTakePartOld")
    private String isTakePartOld;

    @ApiModelProperty(value = "客户是否在厂",name = "isTakePartOld")
    private Long isCustomerInAsc;


    /**
     * 原结算单号
     */
    @ApiModelProperty("原结算单号")
    private String lastBalanceNo;

    @ApiModelProperty("是否清洗完成")
    private Long isWash;

    @ApiModelProperty("是否清洗完成")
    private String isWashStr;
    private String ownerCode;
    //终身保政策，六大服务承诺
    @ApiModelProperty("终身保政策，六大服务承诺")
    private PromiseVO promiseVO;

    @ApiModelProperty(value = "结算单确认时间", name = "balanceConfirmationTime")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date balanceConfirmationTime;

    @ApiModelProperty(value = "结算单备注", name = "balanceRemarks")
    private List<String> balanceRemarks;
    @ApiModelProperty("未做项目提示")
    private String notDoneProjectHint;

    /**
     * 延保保单id
     */
    private String orderId;

    @ApiModelProperty("服务合同购买信息")
    private List<CareBuyedExtDto> careBuyedDtos;

    @ApiModelProperty("卡券白名单")
    private Boolean isCouponWhite;
}
