package com.volvo.bff.volvoworks.wechat.common.service.app.impl;

import com.alibaba.fastjson.JSON;
import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.PageBean;
import com.volvo.bff.volvoworks.wechat.common.model.vo.app.*;
import com.volvo.bff.volvoworks.wechat.common.model.vo.assistant.QuickRepairOrderQueryResultVo;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.service.app.QueryRepairOrderService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class QueryRepairOrderServiceImpl implements QueryRepairOrderService {

    @Resource
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Override
    public PageBean<QueryRepairingOrderResultVo> queryAllRepairOrder(QueryStructureCheckRrquestVo queryParamsVo) {
        log.info("queryAllRepairOrder params:{}", queryParamsVo);
        DmsResultDTO<PageBean<QueryRepairingOrderResultVo>> res = dmscloudServiceFeign.queryAllRepairOrder(queryParamsVo);
        log.info("queryAllRepairOrder result:{}", JSON.toJSONString(res));
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public RepairOrderDetailsResultVo queryRepairOrderDetail(RepairOrderDetailParamsVo paramsVo) {
        DmsResultDTO<RepairOrderDetailsResultVo> res = dmscloudServiceFeign.queryRepairOrderDetail(paramsVo);
        ErrorConversionUtils.errorCodeConversion(res);
        return res.getData();
    }

    @Override
    public void saveAvailableOneId(RepairOrderAvailableOneIdParamsVo paramsVo) {
        DmsResultDTO<Void> res = dmscloudServiceFeign.saveAvailableOneId(paramsVo);
        ErrorConversionUtils.errorCodeConversion(res);
    }
}
