package com.volvo.bff.volvoworks.wechat.common.service.stockmanage;

import com.volvo.bff.volvoworks.wechat.common.model.dto.stockmanage.SourcePartFlowDto;

import java.math.BigDecimal;

public interface PartSourceCodeInfoService {

    void saveNoSystemRecordSourcePartInOutFlow(SourcePartFlowDto req);

    BigDecimal checkSourceCode(Integer code, String jdShipNo, String businessNo, String partNo, String sourceCode, int dirDetail);

}
