package com.volvo.bff.volvoworks.wechat.common.service.basedata.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.DmscloudServiceFeign;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.PageInfoDto;
import com.volvo.bff.volvoworks.wechat.common.service.basedata.PartStockService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class PartStockServiceImpl implements PartStockService {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Override
    public List<Map<String,Object>> getOutSourcePartQuantity(List<Map<String,String>> params){
        DmsResultDTO<List<Map<String,Object>>> response = dmscloudServiceFeign.getOutSourcePartQuantity(params);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }
    @Override
    public List<Map<String,Object>> getInSourcePartQuantity(List<Map<String,String>> params){
        DmsResultDTO<List<Map<String,Object>>> response = dmscloudServiceFeign.getInSourcePartQuantity(params);
        ErrorConversionUtils.errorCodeConversion(response);
        return response.getData();
    }

}
