package com.volvo.bff.volvoworks.wechat.common.service.pad;


import com.volvo.bff.volvoworks.wechat.common.model.vo.pad.*;

import java.util.Map;

public interface SurroundCheckService {

    /**
     * 新增环检单主信息-第一页
     *
     * @param  surroundCheckVO 新增环检单主信息
     * @return String 环检单号
     */
    String saveSurroundCheck1(SurroundCheckVO surroundCheckVO);

    /**
     * 新增环检单主信息-第二页外观检查
     *
     * @param  appearanceCheckVO 环检单外观信息
     * @return String 环检单号
     */
    String saveSurroundCheck2(PadPreviewInteriorVo appearanceCheckVO);

    /**
     * 新增环检单-内饰
     * @param  padPreviewInteriorVo 新增环检单主信息
     * @return 布尔值
     */
    String savePreviewInterior(PadPreviewInteriorVo padPreviewInteriorVo);

    /**
     * 新增环检单-客户需求
     * @param  padCustomerRequireVo
     * @return 布尔值
     */
    String savePreviewCustomerRequire(PadCustomerRequireVo padCustomerRequireVo);

    /**
     * 环检单图片上传
     * @param  base64FileStr
     * @return uuid
     */
    String saveSendBaseFile(Map<String,String> map);
}
