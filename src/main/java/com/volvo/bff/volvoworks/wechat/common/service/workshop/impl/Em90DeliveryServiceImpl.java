package com.volvo.bff.volvoworks.wechat.common.service.workshop.impl;

import com.volvo.bff.volvoworks.wechat.common.feign.ApplicationMaintainManagementFeign;
import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.DmsResultDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.ReportReasonDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.VehicleEntranceVo;
import com.volvo.bff.volvoworks.wechat.common.service.workshop.Em90DeliveryService;
import com.volvo.bff.volvoworks.wechat.common.utils.ErrorConversionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class Em90DeliveryServiceImpl implements Em90DeliveryService {

    @Autowired
    private ApplicationMaintainManagementFeign applicationMaintainManagementFeign;

    @Override
    public boolean selectEm90DeliveryFrame(String ownerCode, String roNo, String vin) {
        DmsResultDTO<Boolean> result = applicationMaintainManagementFeign.selectEm90DeliveryFrame(ownerCode, roNo, vin);
        ErrorConversionUtils.errorCodeConversion(result);
        return result.getData();
    }

    @Override
    public Void submitReportReason(ReportReasonDto reportReasonDto) {
        DmsResultDTO<Void> voidDmsResultDTO = applicationMaintainManagementFeign.submitReportReason(reportReasonDto);
        ErrorConversionUtils.errorCodeConversion(voidDmsResultDTO);
        return voidDmsResultDTO.getData();
    }


    @Override
    public Void em90TakeDeliverPrecheckEmail(VehicleEntranceVo vehicleEntranceVO) {
        DmsResultDTO<Void> voidDmsResultDTO = applicationMaintainManagementFeign.em90TakeDeliverPrecheckEmail(vehicleEntranceVO);
        ErrorConversionUtils.errorCodeConversion(voidDmsResultDTO);
        return voidDmsResultDTO.getData();
    }
}
