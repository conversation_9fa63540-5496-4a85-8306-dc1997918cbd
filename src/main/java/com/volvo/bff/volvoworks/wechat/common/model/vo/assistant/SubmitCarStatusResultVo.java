package com.volvo.bff.volvoworks.wechat.common.model.vo.assistant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 交车状态查询结果VO
 * <AUTHOR>
 * @since 2020-03-16
 */
@Data
@ApiModel("交车状态查询结果VO")
public class SubmitCarStatusResultVo {
    @ApiModelProperty(value = "未交车",name = "undeliveredVehicle")
    private String  undeliveredVehicle;
    @ApiModelProperty(value = "已交车",name = "deliveredVehicle")
    private String  deliveredVehicle;



}
