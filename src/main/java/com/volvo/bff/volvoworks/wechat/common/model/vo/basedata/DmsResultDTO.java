package com.volvo.bff.volvoworks.wechat.common.model.vo.basedata;

import com.volvo.bff.volvoworks.wechat.common.constants.CommonConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DmsResultDTO<T> implements Serializable {
	
	private static final long serialVersionUID = 4526467386531853113L;

	@ApiModelProperty(value = "returnCode")
    private String returnCode;
	
	@ApiModelProperty(value = "resultCode")
    private String resultCode;

    @ApiModelProperty(value = "code")
    private String code;
	
	@ApiModelProperty(value = "returnMessage")
    private String returnMessage;

	@ApiModelProperty(value = "msg")
    private String errMsg;
	
	@ApiModelProperty(value = "")
    private Boolean success;
	
    @ApiModelProperty(value = "数据")
    private T data;

    public Boolean getSuccess() {
        if(this.success == null){
            return isSuccess();
        }
        return success;
    }

    public boolean isSuccess() {
        return Objects.equals(this.getResultCode(), CommonConstant.SUCCESS_NEWBIE_CODE)
                || Objects.equals(this.getResultCode(), CommonConstant.SUCCESS_CODE)
                || Objects.equals(this.getReturnCode(), CommonConstant.SUCCESS_NEWBIE_CODE)
                || Objects.equals(this.getReturnCode(), CommonConstant.SUCCESS_CODE)
                || Objects.equals(this.getCode(), CommonConstant.SUCCESS_NEWBIE_CODE)
                || Objects.equals(this.getCode(), CommonConstant.SUCCESS_CODE);
    }

    public boolean isFail() {
        return !isSuccess();
    }
}
