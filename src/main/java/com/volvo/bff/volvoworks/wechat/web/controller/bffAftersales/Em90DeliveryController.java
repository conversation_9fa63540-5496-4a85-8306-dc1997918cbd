package com.volvo.bff.volvoworks.wechat.web.controller.bffAftersales;

import com.volvo.bff.volvoworks.wechat.common.model.vo.basedata.ResponseDTO;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.ReportReasonDto;
import com.volvo.bff.volvoworks.wechat.common.model.vo.workshop.VehicleEntranceVo;
import com.volvo.bff.volvoworks.wechat.common.service.workshop.Em90DeliveryService;
import com.volvo.bff.volvoworks.wechat.common.utils.ResponseUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "em90Controller")
@RequestMapping("/api/v1/app/Em90DeliveryController")
@RestController
@Slf4j
public class Em90DeliveryController {

    @Autowired
    public Em90DeliveryService em90DeliveryService;


    /**
     * 查询em90工单交车弹框 true弹框  false不弹框
     */
    @GetMapping(value = "/selectEm90DeliveryFrame")
    @ApiOperation(value = "查询em90工单交车弹框 true弹框  false不弹框")
    public ResponseDTO<Boolean> selectEm90DeliveryFrame(@RequestParam("ownerCode") String ownerCode, @RequestParam("roNo")String roNo, @RequestParam("vin")String vin){
        return ResponseUtils.success(em90DeliveryService.selectEm90DeliveryFrame(ownerCode, roNo, vin));
    }

    @PostMapping(value = "/submitReportReason")
    @ApiOperation(value = "提交未提交报告原因")
    public ResponseDTO<Void> submitReportReason(@RequestBody ReportReasonDto reportReasonDto){
        return ResponseUtils.success(em90DeliveryService.submitReportReason(reportReasonDto));
    }


    /**
     * 环检接车发送邮件
     */
    @PostMapping("/em90TakeDeliverPrecheckEmail")
    @ApiOperation(value = "环检接车发送邮件")
    public ResponseDTO<Void> em90TakeDeliverPrecheckEmail(@RequestBody VehicleEntranceVo vehicleEntranceVO) {
        return ResponseUtils.success(em90DeliveryService.em90TakeDeliverPrecheckEmail(vehicleEntranceVO));
    }
}
