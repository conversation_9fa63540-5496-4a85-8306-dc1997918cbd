package com.volvo.bff.volvoworks.wechat;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.HashMap;
import java.util.Map;

@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.volvo"})
@ComponentScan(basePackages = {"com.volvo"})
@EnableAsync
public class Application {

    public static void main(String[] args) {
        Map<String, Object> props = new HashMap<>();
        props.put("spring.main.allow-bean-definition-overriding", true);
        SpringApplication app = new SpringApplication(Application.class);
        app.setDefaultProperties(props);
        app.run(args);
    }

}
