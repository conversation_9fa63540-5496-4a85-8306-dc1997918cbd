package com.volvo.bff.volvoworks.wechat.common.model.dto.LucencyWorkshop;

import lombok.Data;

//开单dto
@Data
public class BeginOrderDTO {

    //经销商code
    private String ownerCode;
    //车牌号
    private String license;
    //vin
    private String vin;
    //工单号
    private String roNo;
    //开单时间
    private String roCreateDate;
    //预交车时间
    private String endTimeSupposed;
    //是否质检
    private String completeTag;
    //工单类型
    private String repairTypeCode;
    //标签
    private String label;
    //缺货状态
    private String isStockUp;
    //工单状态
    private String roStatus;
    //送修人
    private String deliverer;
    //服务顾问
    private String serviceAdvisor;
    //车型
    private String model;
    //结算单确认状态
    private String orderConfirmStatus;






}
