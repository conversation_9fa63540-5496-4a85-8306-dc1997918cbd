package com.volvo.bff.volvoworks.wechat.common.model.dto.stockmanage;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PartInventoryDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 系统ID
     */


    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */


    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 盘点单号
     */
    private String inventoryNo;

    /**
     * 盘点日期
     */
    private Date inventoryDate;

    /**
     * 不含税盘盈金额
     */
    private BigDecimal profitAmount;

    /**
     * 盘亏项目数
     */
    private Integer lossCount;

    /**
     * 盘亏金额
     */
    private BigDecimal lossAmount;

    /**
     * 经手人
     */
    private String handler;

    /**
     * 盘盈项目数
     */
    private Integer profitCount;

    /**
     * 是否确认
     */
    private Integer isConfirmed;

    /**
     * 是否入帐
     */
    private Integer isFinished;

    /**
     * 报溢标志
     */
    private Integer profitTag;

    /**
     * 报损标志
     */
    private Integer lossTag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 锁定人
     */
    private String lockUser;

    /**
     * 是否删除
     */

    private Boolean isDeleted;

    //盘点类型
    private Integer inventoryType;
}
